{"data": {"initData": {"cells": {"canvasDesignerInfo": {"editCanvasConfig": {"projectName": "点焊机器人(深度学习V2)", "width": 1920, "height": 1080, "filterShow": false, "hueRotate": 0, "saturate": 1, "contrast": 1, "brightness": 1, "opacity": 1, "rotateZ": 0, "rotateX": 0, "rotateY": 0, "skewX": 0, "skewY": 0, "blendMode": "normal", "selectColor": true, "chartThemeColor": "dark", "chartThemeSetting": {"title": {"show": true, "textStyle": {"color": "#BFBFBF", "fontSize": 18}, "subtextStyle": {"color": "#A2A2A2", "fontSize": 14}}, "xAxis": {"show": true, "name": "", "nameGap": 15, "nameTextStyle": {"color": "#B9B8CE", "fontSize": 12}, "inverse": false, "axisLabel": {"show": true, "fontSize": 12, "color": "#B9B8CE", "rotate": 0}, "position": "bottom", "axisLine": {"show": true, "lineStyle": {"color": "#B9B8CE", "width": 1}, "onZero": true}, "axisTick": {"show": true, "length": 5}, "splitLine": {"show": false, "lineStyle": {"color": "#484753", "width": 1, "type": "solid"}}}, "yAxis": {"show": true, "name": "", "nameGap": 15, "nameTextStyle": {"color": "#B9B8CE", "fontSize": 12}, "inverse": false, "axisLabel": {"show": true, "fontSize": 12, "color": "#B9B8CE", "rotate": 0}, "position": "left", "axisLine": {"show": true, "lineStyle": {"color": "#B9B8CE", "width": 1}, "onZero": true}, "axisTick": {"show": true, "length": 5}, "splitLine": {"show": true, "lineStyle": {"color": "#484753", "width": 1, "type": "solid"}}}, "legend": {"show": true, "type": "scroll", "x": "center", "y": "top", "icon": "circle", "orient": "horizontal", "textStyle": {"color": "#B9B8CE", "fontSize": 18}, "itemHeight": 15, "itemWidth": 15, "pageTextStyle": {"color": "#B9B8CE"}}, "grid": {"show": false, "left": "10%", "top": "60", "right": "10%", "bottom": "60"}, "renderer": "svg"}, "previewScaleType": "fit"}, "componentList": [{"id": "4o3hffh808cy68=", "isGroup": false, "attr": {"x": 16, "y": 15, "w": 937, "h": 1056, "offsetX": 0, "offsetY": 0, "zIndex": -1}, "styles": {"filterShow": false, "hueRotate": 0, "saturate": 1, "contrast": 1, "brightness": 1, "opacity": 1, "rotateZ": 0, "rotateX": 0, "rotateY": 0, "skewX": 0, "skewY": 0, "blendMode": "normal", "animations": []}, "preview": {"overFlowHidden": false}, "status": {"lock": false, "hide": false}, "request": {"requestDataType": 0, "requestHttpType": "get", "requestUrl": "", "requestIntervalUnit": "second", "requestContentType": 0, "requestParamsBodyType": "none", "requestSQLContent": {"sql": "select * from  where"}, "requestParams": {"Body": {"form-data": {}, "x-www-form-urlencoded": {}, "json": "", "xml": ""}, "Header": {}, "Params": {}}}, "events": {"baseEvent": {}, "advancedEvents": {}, "interactEvents": []}, "key": "Border05", "chartConfig": {"key": "Border05", "chartKey": "VBorder05", "conKey": "VCBorder05", "title": "边框-05", "category": "Borders", "categoryName": "边框", "package": "Decorates", "chartFrame": "static", "image": "border05.png"}, "option": {"colors": ["#1d48c4", "#d3e1f8"], "backgroundColor": "#00000000"}}, {"id": "289xz0iwiotbls=", "isGroup": false, "attr": {"x": 960, "y": 21, "w": 941, "h": 1040, "offsetX": 0, "offsetY": 0, "zIndex": -1}, "styles": {"filterShow": false, "hueRotate": 0, "saturate": 1, "contrast": 1, "brightness": 1, "opacity": 1, "rotateZ": 0, "rotateX": 0, "rotateY": 0, "skewX": 0, "skewY": 0, "blendMode": "normal", "animations": []}, "preview": {"overFlowHidden": false}, "status": {"lock": false, "hide": false}, "request": {"requestDataType": 0, "requestHttpType": "get", "requestUrl": "", "requestIntervalUnit": "second", "requestContentType": 0, "requestParamsBodyType": "none", "requestSQLContent": {"sql": "select * from  where"}, "requestParams": {"Body": {"form-data": {}, "x-www-form-urlencoded": {}, "json": "", "xml": ""}, "Header": {}, "Params": {}}}, "events": {"baseEvent": {}, "advancedEvents": {}, "interactEvents": []}, "key": "Border05", "chartConfig": {"key": "Border05", "chartKey": "VBorder05", "conKey": "VCBorder05", "title": "边框-05", "category": "Borders", "categoryName": "边框", "package": "Decorates", "chartFrame": "static", "image": "border05.png"}, "option": {"colors": ["#1d48c4", "#d3e1f8"], "backgroundColor": "#00000000"}}, {"id": "61h88shrm8lc0=", "isGroup": false, "attr": {"x": 9, "y": 13, "w": 1895, "h": 1060, "offsetX": 0, "offsetY": 0, "zIndex": -1}, "styles": {"filterShow": false, "hueRotate": 0, "saturate": 1, "contrast": 1, "brightness": 1, "opacity": 1, "rotateZ": 0, "rotateX": 0, "rotateY": 0, "skewX": 0, "skewY": 0, "blendMode": "normal", "animations": []}, "preview": {"overFlowHidden": false}, "status": {"lock": false, "hide": false}, "request": {"requestDataType": 0, "requestHttpType": "get", "requestUrl": "", "requestIntervalUnit": "second", "requestContentType": 0, "requestParamsBodyType": "none", "requestSQLContent": {"sql": "select * from  where"}, "requestParams": {"Body": {"form-data": {}, "x-www-form-urlencoded": {}, "json": "", "xml": ""}, "Header": {}, "Params": {}}}, "events": {"baseEvent": {}, "advancedEvents": {}, "interactEvents": []}, "key": "Border04", "chartConfig": {"key": "Border04", "chartKey": "VBorder04", "conKey": "VCBorder04", "title": "边框-04", "category": "Borders", "categoryName": "边框", "package": "Decorates", "chartFrame": "static", "image": "border04.png"}, "option": {"borderTitle": "点焊机器人-自动", "borderTitleWidth": 250, "borderTitleHeight": 32, "borderTitleSize": 18, "borderTitleColor": "#fff", "colors": ["#8aa<PERSON>b", "#1f33a2"], "backgroundColor": "#00000000"}}, {"id": "1wapxrnph9ce8=", "isGroup": false, "attr": {"x": 88, "y": 79, "w": 794, "h": 950, "offsetX": 0, "offsetY": 0, "zIndex": -1}, "styles": {"filterShow": false, "hueRotate": 0, "saturate": 1, "contrast": 1, "brightness": 1, "opacity": 1, "rotateZ": 0, "rotateX": 0, "rotateY": 0, "skewX": 0, "skewY": 0, "blendMode": "normal", "animations": []}, "preview": {"overFlowHidden": false}, "status": {"lock": false, "hide": false}, "request": {"requestDataType": 0, "requestHttpType": "get", "requestUrl": "", "requestIntervalUnit": "second", "requestContentType": 0, "requestParamsBodyType": "none", "requestSQLContent": {"sql": "select * from  where"}, "requestParams": {"Body": {"form-data": {}, "x-www-form-urlencoded": {}, "json": "", "xml": ""}, "Header": {}, "Params": {}}}, "events": {"baseEvent": {}, "advancedEvents": {}, "interactEvents": []}, "key": "ImgViewer", "chartConfig": {"key": "ImgViewer", "chartKey": "VImgViewer", "conKey": "VCImgViewer", "title": "图片展示", "category": "Viewer", "categoryName": "图像展示", "package": "Custom", "chartFrame": "common", "image": "Img.png"}, "option": {"dataset": {"path": "/data/"}, "rowNum": 5, "waitTime": 2, "unit": "", "sort": true, "color": "#1370fb", "textColor": "#CDD2F8FF", "borderColor": "#1370fb80", "carousel": "single", "indexFontSize": 12, "leftFontSize": 12, "rightFontSize": 12}}, {"id": "n4okqmn59a800=", "isGroup": false, "attr": {"x": 991, "y": 79, "w": 460, "h": 37, "offsetX": 0, "offsetY": 0, "zIndex": -1}, "styles": {"filterShow": false, "hueRotate": 0, "saturate": 1, "contrast": 1, "brightness": 1, "opacity": 1, "rotateZ": 0, "rotateX": 0, "rotateY": 0, "skewX": 0, "skewY": 0, "blendMode": "normal", "animations": []}, "preview": {"overFlowHidden": false}, "status": {"lock": false, "hide": false}, "request": {"requestDataType": 0, "requestHttpType": "get", "requestUrl": "", "requestIntervalUnit": "second", "requestContentType": 0, "requestParamsBodyType": "none", "requestSQLContent": {"sql": "select * from  where"}, "requestParams": {"Body": {"form-data": {}, "x-www-form-urlencoded": {}, "json": "", "xml": ""}, "Header": {}, "Params": {}}}, "events": {"baseEvent": {}, "advancedEvents": {}, "interactEvents": []}, "key": "InputsTab2", "chartConfig": {"key": "InputsTab2", "chartKey": "VInputsTab2", "conKey": "VCInputsTab2", "title": "自定义标签", "category": "Inputs", "categoryName": "控件", "package": "Informations", "chartFrame": "static", "image": "inputs_tab.png"}, "interactActions": [{"interactType": "change", "interactName": "选择完成", "componentEmitEvents": {"data": [{"value": "data", "label": "选择项"}]}}], "option": {"componentInteractEventKey": "data", "tabLabel": "选项1", "tabValue": "1", "tabType": "segment", "dataset": {"options": [{"label": "实时数据", "value": "1"}, {"label": "历史数据", "value": "2"}], "checks": [{"tabLabel": "1", "ids": ["4o3hffh808cy68=", "289xz0iwiotbls=", "1wapxrnph9ce8=", "1zsnpa1rxfk000=", "4fqtio35yfw000=", "4ng9fbe2pqu000=", "424zxm5ebmu000=", "2l2isae55gu000=", "5tiufthlbgw000=", "19y0k7xfh30g00=", "hgwir4z1p8o00="]}, {"tabLabel": "2", "ids": ["21hd14rhwh0g00="]}, {"tabLabel": "10086", "ids": []}], "allChecks": ["4o3hffh808cy68=", "289xz0iwiotbls=", "1wapxrnph9ce8=", "1zsnpa1rxfk000=", "4fqtio35yfw000=", "4ng9fbe2pqu000=", "424zxm5ebmu000=", "2l2isae55gu000=", "5tiufthlbgw000=", "19y0k7xfh30g00=", "21hd14rhwh0g00=", "hgwir4z1p8o00="]}}}, {"id": "1zsnpa1rxfk000=", "isGroup": false, "attr": {"x": 1082, "y": 164, "w": 674, "h": 320, "offsetX": 0, "offsetY": 0, "zIndex": -1}, "styles": {"filterShow": false, "hueRotate": 0, "saturate": 1, "contrast": 1, "brightness": 1, "opacity": 1, "rotateZ": 0, "rotateX": 0, "rotateY": 0, "skewX": 0, "skewY": 0, "blendMode": "normal", "animations": []}, "preview": {"overFlowHidden": false}, "status": {"lock": false, "hide": false}, "request": {"requestDataType": 0, "requestHttpType": "get", "requestUrl": "", "requestIntervalUnit": "second", "requestContentType": 0, "requestParamsBodyType": "none", "requestSQLContent": {"sql": "select * from  where"}, "requestParams": {"Body": {"form-data": {}, "x-www-form-urlencoded": {}, "json": "", "xml": ""}, "Header": {}, "Params": {}}}, "events": {"baseEvent": {}, "advancedEvents": {}, "interactEvents": []}, "key": "InputsForm", "chartConfig": {"key": "InputsForm", "chartKey": "VInputsForm", "conKey": "VCInputsForm", "title": "表单展示", "category": "Viewer", "categoryName": "图像", "package": "Custom", "chartFrame": "common", "image": "inputs_tab.png"}, "interactActions": [{"interactType": "change", "interactName": "选择完成", "componentEmitEvents": {"data": [{"value": "data", "label": "选择项"}]}}], "option": {"componentInteractEventKey": "data", "dataset": {"title": "点焊实时数据展示", "color": "#FFFFFFFF", "options": [{"label": "表单1", "value": "1"}, {"label": "表单2", "value": "2"}, {"label": "表单3", "value": "3"}], "allChecks": []}}}, {"id": "5sdi48jnks0000=", "isGroup": false, "attr": {"x": 1551, "y": 72.5, "w": 300, "h": 50, "offsetX": 0, "offsetY": 0, "zIndex": -1}, "styles": {"filterShow": false, "hueRotate": 0, "saturate": 1, "contrast": 1, "brightness": 1, "opacity": 1, "rotateZ": 0, "rotateX": 0, "rotateY": 0, "skewX": 0, "skewY": 0, "blendMode": "normal", "animations": []}, "preview": {"overFlowHidden": false}, "status": {"lock": false, "hide": false}, "request": {"requestDataType": 0, "requestHttpType": "get", "requestUrl": "", "requestIntervalUnit": "second", "requestContentType": 0, "requestParamsBodyType": "none", "requestSQLContent": {"sql": "select * from  where"}, "requestParams": {"Body": {"form-data": {}, "x-www-form-urlencoded": {}, "json": "", "xml": ""}, "Header": {}, "Params": {}}}, "events": {"baseEvent": {}, "advancedEvents": {}, "interactEvents": []}, "key": "TimeCommon", "chartConfig": {"key": "TimeCommon", "chartKey": "VTimeCommon", "conKey": "VCTimeCommon", "title": "通用时间", "category": "Mores", "categoryName": "更多", "package": "Decorates", "chartFrame": "static", "image": "time.png"}, "option": {"timeSize": 24, "timeLineHeight": 50, "timeTextIndent": 2, "timeColor": "#E6F7FF", "fontWeight": "normal", "showShadow": true, "hShadow": 0, "vShadow": 0, "blurShadow": 8, "colorShadow": "#0075ff"}}, {"id": "4fqtio35yfw000=", "isGroup": false, "attr": {"x": 997, "y": 555, "w": 873, "h": 481, "offsetX": 0, "offsetY": 0, "zIndex": -1}, "styles": {"filterShow": false, "hueRotate": 0, "saturate": 1, "contrast": 1, "brightness": 1, "opacity": 1, "rotateZ": 0, "rotateX": 0, "rotateY": 0, "skewX": 0, "skewY": 0, "blendMode": "normal", "animations": []}, "preview": {"overFlowHidden": false}, "status": {"lock": false, "hide": false}, "request": {"requestDataType": 0, "requestHttpType": "get", "requestUrl": "", "requestIntervalUnit": "second", "requestContentType": 0, "requestParamsBodyType": "none", "requestSQLContent": {"sql": "select * from  where"}, "requestParams": {"Body": {"form-data": {}, "x-www-form-urlencoded": {}, "json": "", "xml": ""}, "Header": {}, "Params": {}}}, "events": {"baseEvent": {}, "advancedEvents": {}, "interactEvents": []}, "key": "Border05", "chartConfig": {"key": "Border05", "chartKey": "VBorder05", "conKey": "VCBorder05", "title": "边框-05", "category": "Borders", "categoryName": "边框", "package": "Decorates", "chartFrame": "static", "image": "border05.png"}, "option": {"colors": ["#1d48c4", "#d3e1f8"], "backgroundColor": "#00000000"}}, {"id": "4ng9fbe2pqu000=", "isGroup": false, "attr": {"x": 997, "y": 132, "w": 870, "h": 409, "offsetX": 0, "offsetY": 0, "zIndex": -1}, "styles": {"filterShow": false, "hueRotate": 0, "saturate": 1, "contrast": 1, "brightness": 1, "opacity": 1, "rotateZ": 0, "rotateX": 0, "rotateY": 0, "skewX": 0, "skewY": 0, "blendMode": "normal", "animations": []}, "preview": {"overFlowHidden": false}, "status": {"lock": false, "hide": false}, "request": {"requestDataType": 0, "requestHttpType": "get", "requestUrl": "", "requestIntervalUnit": "second", "requestContentType": 0, "requestParamsBodyType": "none", "requestSQLContent": {"sql": "select * from  where"}, "requestParams": {"Body": {"form-data": {}, "x-www-form-urlencoded": {}, "json": "", "xml": ""}, "Header": {}, "Params": {}}}, "events": {"baseEvent": {}, "advancedEvents": {}, "interactEvents": []}, "key": "Border05", "chartConfig": {"key": "Border05", "chartKey": "VBorder05", "conKey": "VCBorder05", "title": "边框-05", "category": "Borders", "categoryName": "边框", "package": "Decorates", "chartFrame": "static", "image": "border05.png"}, "option": {"colors": ["#1d48c4", "#d3e1f8"], "backgroundColor": "#00000000"}}, {"id": "424zxm5ebmu000=", "isGroup": false, "attr": {"x": 1240, "y": 723, "w": 156, "h": 91, "offsetX": 0, "offsetY": 0, "zIndex": -1}, "styles": {"filterShow": false, "hueRotate": 0, "saturate": 1, "contrast": 1, "brightness": 1, "opacity": 1, "rotateZ": 0, "rotateX": 0, "rotateY": 0, "skewX": 0, "skewY": 0, "blendMode": "normal", "animations": []}, "preview": {"overFlowHidden": false}, "status": {"lock": false, "hide": false}, "request": {"requestDataType": 0, "requestHttpType": "get", "requestUrl": "", "requestIntervalUnit": "second", "requestContentType": 0, "requestParamsBodyType": "none", "requestSQLContent": {"sql": "select * from  where"}, "requestParams": {"Body": {"form-data": {}, "x-www-form-urlencoded": {}, "json": "", "xml": ""}, "Header": {}, "Params": {}}}, "events": {"baseEvent": {}, "advancedEvents": {}, "interactEvents": []}, "key": "SignalViewer", "chartConfig": {"key": "SignalViewer", "chartKey": "VSignalViewer", "conKey": "VCSignalViewer", "title": "信号灯展示", "category": "Viewer", "categoryName": "图像", "package": "Custom", "chartFrame": "common", "image": "Signal.jpg"}, "option": {"dataset": {"settingItemBoxes": [{"signal": "0", "color": "#FF0000"}, {"signal": "1", "color": "#008000"}, {"signal": "yellow", "color": "#FFFF00"}], "text": "机器人上电"}, "width": 100, "height": 100, "carousel": "single"}}, {"id": "2l2isae55gu000=", "isGroup": false, "attr": {"x": 1473, "y": 720, "w": 156, "h": 91, "offsetX": 0, "offsetY": 0, "zIndex": -1}, "styles": {"filterShow": false, "hueRotate": 0, "saturate": 1, "contrast": 1, "brightness": 1, "opacity": 1, "rotateZ": 0, "rotateX": 0, "rotateY": 0, "skewX": 0, "skewY": 0, "blendMode": "normal", "animations": []}, "preview": {"overFlowHidden": false}, "status": {"lock": false, "hide": false}, "request": {"requestDataType": 0, "requestHttpType": "get", "requestUrl": "", "requestIntervalUnit": "second", "requestContentType": 0, "requestParamsBodyType": "none", "requestSQLContent": {"sql": "select * from  where"}, "requestParams": {"Body": {"form-data": {}, "x-www-form-urlencoded": {}, "json": "", "xml": ""}, "Header": {}, "Params": {}}}, "events": {"baseEvent": {}, "advancedEvents": {}, "interactEvents": []}, "key": "SignalViewer", "chartConfig": {"key": "SignalViewer", "chartKey": "VSignalViewer", "conKey": "VCSignalViewer", "title": "信号灯展示", "category": "Viewer", "categoryName": "图像", "package": "Custom", "chartFrame": "common", "image": "Signal.jpg"}, "option": {"dataset": {"settingItemBoxes": [{"signal": "0", "color": "#FF0000"}, {"signal": "1", "color": "#008000"}, {"signal": "yellow", "color": "#FFFF00"}], "text": "自动运行中"}, "width": 100, "height": 100, "carousel": "single"}}, {"id": "5tiufthlbgw000=", "isGroup": false, "attr": {"x": 1240, "y": 883, "w": 156, "h": 91, "offsetX": 0, "offsetY": 0, "zIndex": -1}, "styles": {"filterShow": false, "hueRotate": 0, "saturate": 1, "contrast": 1, "brightness": 1, "opacity": 1, "rotateZ": 0, "rotateX": 0, "rotateY": 0, "skewX": 0, "skewY": 0, "blendMode": "normal", "animations": []}, "preview": {"overFlowHidden": false}, "status": {"lock": false, "hide": false}, "request": {"requestDataType": 0, "requestHttpType": "get", "requestUrl": "", "requestIntervalUnit": "second", "requestContentType": 0, "requestParamsBodyType": "none", "requestSQLContent": {"sql": "select * from  where"}, "requestParams": {"Body": {"form-data": {}, "x-www-form-urlencoded": {}, "json": "", "xml": ""}, "Header": {}, "Params": {}}}, "events": {"baseEvent": {}, "advancedEvents": {}, "interactEvents": []}, "key": "SignalViewer", "chartConfig": {"key": "SignalViewer", "chartKey": "VSignalViewer", "conKey": "VCSignalViewer", "title": "信号灯展示", "category": "Viewer", "categoryName": "图像", "package": "Custom", "chartFrame": "common", "image": "Signal.jpg"}, "option": {"dataset": {"settingItemBoxes": [{"signal": "0", "color": "#FF0000"}, {"signal": "1", "color": "#008000"}, {"signal": "yellow", "color": "#FFFF00"}], "text": "指示灯-本地"}, "width": 100, "height": 100, "carousel": "single"}}, {"id": "19y0k7xfh30g00=", "isGroup": false, "attr": {"x": 1473, "y": 878, "w": 156, "h": 91, "offsetX": 0, "offsetY": 0, "zIndex": -1}, "styles": {"filterShow": false, "hueRotate": 0, "saturate": 1, "contrast": 1, "brightness": 1, "opacity": 1, "rotateZ": 0, "rotateX": 0, "rotateY": 0, "skewX": 0, "skewY": 0, "blendMode": "normal", "animations": []}, "preview": {"overFlowHidden": false}, "status": {"lock": false, "hide": false}, "request": {"requestDataType": 0, "requestHttpType": "get", "requestUrl": "", "requestIntervalUnit": "second", "requestContentType": 0, "requestParamsBodyType": "none", "requestSQLContent": {"sql": "select * from  where"}, "requestParams": {"Body": {"form-data": {}, "x-www-form-urlencoded": {}, "json": "", "xml": ""}, "Header": {}, "Params": {}}}, "events": {"baseEvent": {}, "advancedEvents": {}, "interactEvents": []}, "key": "SignalViewer", "chartConfig": {"key": "SignalViewer", "chartKey": "VSignalViewer", "conKey": "VCSignalViewer", "title": "信号灯展示", "category": "Viewer", "categoryName": "图像", "package": "Custom", "chartFrame": "common", "image": "Signal.jpg"}, "option": {"dataset": {"settingItemBoxes": [{"signal": "0", "color": "#FF0000"}, {"signal": "1", "color": "#008000"}, {"signal": "yellow", "color": "#FFFF00"}], "text": "指示灯-远程"}, "width": 100, "height": 100, "carousel": "single"}}, {"id": "21hd14rhwh0g00=", "isGroup": false, "attr": {"x": 34, "y": 148, "w": 1841, "h": 893, "offsetX": 0, "offsetY": 0, "zIndex": -1}, "styles": {"filterShow": false, "hueRotate": 0, "saturate": 1, "contrast": 1, "brightness": 1, "opacity": 1, "rotateZ": 0, "rotateX": 0, "rotateY": 0, "skewX": 0, "skewY": 0, "blendMode": "normal", "animations": []}, "preview": {"overFlowHidden": false}, "status": {"lock": false, "hide": false}, "request": {"requestDataType": 0, "requestHttpType": "get", "requestUrl": "", "requestIntervalUnit": "second", "requestContentType": 0, "requestParamsBodyType": "none", "requestSQLContent": {"sql": "select * from  where"}, "requestParams": {"Body": {"form-data": {}, "x-www-form-urlencoded": {}, "json": "", "xml": ""}, "Header": {}, "Params": {}}}, "events": {"baseEvent": {}, "advancedEvents": {}, "interactEvents": []}, "key": "Table<PERSON>iewer", "chartConfig": {"key": "Table<PERSON>iewer", "chartKey": "VTableViewer", "conKey": "VCTableViewer", "title": "历史数据", "category": "Inputs", "categoryName": "控件", "package": "Informations", "chartFrame": "static", "image": "txt.png"}, "option": {"dataset": {"extend": [{"key": "extend1", "label": "钢卷号"}]}, "color": "#00000000", "fontColor": "#FFFFFFFF", "fontSize": 25, "index": false}}, {"id": "hgwir4z1p8o00=", "isGroup": false, "attr": {"x": 1182, "y": 580, "w": 500, "h": 70, "offsetX": 0, "offsetY": 0, "zIndex": 1}, "styles": {"filterShow": false, "hueRotate": 0, "saturate": 1, "contrast": 1, "brightness": 1, "opacity": 1, "rotateZ": 0, "rotateX": 0, "rotateY": 0, "skewX": 0, "skewY": 0, "blendMode": "normal", "animations": []}, "preview": {"overFlowHidden": false}, "status": {"lock": false, "hide": false}, "request": {"requestDataType": 0, "requestHttpType": "get", "requestUrl": "", "requestIntervalUnit": "second", "requestContentType": 0, "requestParamsBodyType": "none", "requestSQLContent": {"sql": "select * from  where"}, "requestParams": {"Body": {"form-data": {}, "x-www-form-urlencoded": {}, "json": "", "xml": ""}, "Header": {}, "Params": {}}}, "events": {"baseEvent": {}, "advancedEvents": {}, "interactEvents": []}, "key": "Decorates03", "chartConfig": {"key": "Decorates03", "chartKey": "VDecorates03", "conKey": "VCDecorates03", "title": "实时信号", "category": "Decorates", "categoryName": "装饰", "package": "Decorates", "chartFrame": "static", "image": "decorates03.png"}, "option": {"dataset": "实时信号", "textColor": "#fff", "textSize": 32, "colors": ["#1dc1f5", "#1dc1f5"]}}], "requestGlobalConfig": {"requestDataPond": [], "requestOriginUrl": "", "requestInterval": 30, "requestIntervalUnit": "second", "requestParams": {"Body": {"form-data": {}, "x-www-form-urlencoded": {}, "json": "", "xml": ""}, "Header": {}, "Params": {}}}}, "cells": [{"@type": "com.bx.canvas.message.NodeDTO", "categoryType": "image", "componentId": "949921624677", "craft": "false", "currentPattern": "mode1", "dictEName": "mmyolodetection", "dictId": 327, "dictPattern": [{"patternData": {"params": [82451760, 82490787, 82538365, 82578159, 82616363, 84869083, 84902710, 84948935], "port": [82399440, 82121961]}, "index": 0, "modalName": "模式1", "modalKey": "mode1", "id": 84986704}], "dynamicLib": "IVS_image_deeplearning_mmyolo", "editVersion": 26, "front": false, "groupId": "0", "inPorts": [{"portSequence": 1, "dataTransType": "shareMemory", "portDescription": "输入待推理的图片", "portName": "图片", "portColor": "#bd10e0", "useGlobalVariable": false, "portType": 0, "sequence": 82121961, "valueType": "image", "globalVariableKey": "", "id": "5SbEcMhOex", "portKey": "image"}], "isFront": false, "name": "目标检测推理（mmyolo_onnx）", "outParams": {"cudaenable": "false", "yamlfilepath": "D:\\client_plus\\client_vue\\conf\\pro\\mode\\coco_dianhanneiquan.yaml", "imgsizewidth": "640", "confidencethreshold": "0.2", "modeltype": "1", "iouThreshold": "0.5", "modelpath": "D:\\client_plus\\client_vue\\conf\\pro\\mode\\yolov8m_dianhanneiquan_4.onnx", "imgsizeheight": "640"}, "outPorts": [{"portSequence": 1, "dataTransType": "message", "portDescription": "输出目标框在图像中的位置", "portName": "字符串", "portColor": "#62a314", "useGlobalVariable": false, "portType": 1, "sequence": 82399440, "valueType": "String", "globalVariableKey": "", "id": "JfHZ3sTwiN", "portKey": "detection_result"}], "params": {"schema": {}, "type": "default", "default": [{"paramKey": "modelpath", "index": 0, "paramName": "onnx模型路径", "paramDescription": "onnx模型路径", "paramType": "string", "paramSelect": "true", "paramsId": 82451760, "paramDefaultValue": "\"\""}, {"paramKey": "yamlfilepath", "index": 1, "paramName": "配置文件路径", "paramDescription": "模型配置文件路径", "paramType": "string", "paramSelect": "true", "paramsId": 82490787, "paramDefaultValue": "\"\""}, {"paramKey": "cudaenable", "index": 2, "paramName": "cuda使能", "paramDescription": "是否使用GPU推理", "paramType": "boolean", "paramSelect": "true", "paramsId": 82538365, "paramDefaultValue": "false"}, {"paramKey": "modeltype", "index": 3, "paramName": "模型类型", "paramDescription": "yolo模型的类型", "paramType": "number", "paramSelect": "true", "paramsId": 82578159, "paramDefaultValue": "1"}, {"paramKey": "<PERSON><PERSON><PERSON><PERSON>", "index": 4, "paramName": "置信度", "paramDescription": "目标检测的置信度", "paramType": "float", "paramSelect": "true", "paramsId": 82616363, "paramDefaultValue": "0.3"}, {"paramKey": "imgsizewidth", "index": 5, "paramName": "图片宽度尺寸", "paramDescription": "图片宽度尺寸", "paramType": "number", "paramSelect": "true", "paramsId": 84869083, "paramDefaultValue": "640"}, {"paramKey": "imgsizeheight", "index": 6, "paramName": "图片高度尺寸", "paramDescription": "图片高度尺寸", "paramType": "number", "paramSelect": "true", "paramsId": 84902710, "paramDefaultValue": "640"}, {"paramKey": "iouThreshold", "index": 7, "paramName": "IOU阈值", "paramDescription": "IOU阈值", "paramType": "float", "paramSelect": "true", "paramsId": 84948935, "paramDefaultValue": "0.1"}]}, "portChanged": 0, "position": {"x": 290, "y": 1180}, "serverId": 197, "serviceRequests": {"other": {}, "file": {}}, "specificOutParams": {"craft_false": "1", "craft_true_available": "false", "craft_false_available": "false", "craft_true": "1"}, "type": "node", "typeName": "目标检测推理（mmyolo_onnx）"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "9bad607e-04d9-4636-b395-c565ed745bbd", "source": {"cell": "582474475318", "port": "LvI3NEm6qF"}, "target": {"cell": "1wapxrnph9ce8=", "port": "Gv7lg9VpQX"}, "type": "edge"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "28872a52-11a1-4d28-b210-695287de2f3a", "source": {"cell": "597393491182", "port": "ekTyiQMny7"}, "target": {"cell": "5tiufthlbgw000=", "port": "Wgc0_bV9-1"}, "type": "edge"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "d2c09498-6b66-4922-89f7-d5bd450ccfda", "source": {"cell": "465148797819", "port": "cYcOxwIITO"}, "target": {"cell": "793965957592", "port": "ZcRg1cUnXV"}, "type": "edge"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "dbf42c70-57b4-4b59-ab56-9894b691fdd7", "source": {"cell": "825863991957", "port": "5bVioQcEiy"}, "target": {"cell": "919694131565", "port": "k2yNiqWZg2"}, "type": "edge"}, {"@type": "com.bx.canvas.message.NodeDTO", "categoryType": "switch", "componentId": "567427463187", "craft": "false", "currentPattern": "model", "dictEName": "PathAllocation", "dictId": 351, "dictPattern": [{"patternData": {"params": [17874269], "port": [17762669, 17846145, 17852656]}, "index": 0, "modalName": "模式1", "modalKey": "model", "id": 17911773}], "dynamicLib": "IVS_comm", "editVersion": 0, "front": false, "groupId": "0", "inPorts": [{"portSequence": 1, "dataTransType": "message", "portDescription": "无具体描述", "portName": "通用端口", "portColor": "#8df0d7", "useGlobalVariable": false, "portType": 0, "sequence": 17762669, "valueType": "object", "globalVariableKey": "", "id": "AVJsdVc0sr", "portKey": "all"}, {"portSequence": 1, "dataTransType": "message", "portDescription": "无具体描述", "portName": "Json数据", "portColor": "#eed4e9", "useGlobalVariable": false, "portType": 0, "sequence": 17852656, "valueType": "Json", "globalVariableKey": "", "id": "xWacOraywg", "portKey": "JudgingParameters"}], "isFront": false, "name": "路径分配", "outParams": {"JudgingParameters": "101"}, "outPorts": [{"portSequence": 1, "dataTransType": "message", "portDescription": "无具体描述", "portName": "通用端口", "portColor": "#8df0d7", "useGlobalVariable": false, "portType": 1, "sequence": 17846145, "valueType": "object", "globalVariableKey": "", "id": "Lfmlkf1k5K", "portKey": "all"}], "params": {"schema": {}, "type": "default", "default": [{"paramKey": "JudgingParameters", "index": 0, "paramName": "对照参数", "paramType": "string", "paramSelect": "true", "paramsId": 17874269, "paramDefaultValue": ""}]}, "portChanged": 0, "position": {"x": 1130, "y": 2560}, "serverId": 1, "serviceRequests": {"other": {}, "file": {}}, "specificOutParams": {"craft_false": "1", "craft_true_available": "false", "craft_false_available": "false", "craft_true": "1"}, "type": "node", "typeName": "路径分配"}, {"@type": "com.bx.canvas.message.NodeDTO", "categoryType": "switch", "componentId": "234557254182", "craft": "false", "currentPattern": "modal1", "dictEName": "Myswitch", "dictId": 178, "dictPattern": [{"patternData": {"params": [95029796, 85219138, 85228235], "port": [94954926, 95016006]}, "modalName": "模式1", "modalKey": "modal1", "id": 1}], "dynamicLib": "IVS_comm", "editVersion": 46, "front": false, "groupId": "0", "inPorts": [{"portSequence": 1, "dataTransType": "message", "portDescription": "无具体描述", "portName": "通用端口", "portColor": "#8df0d7", "useGlobalVariable": false, "portType": 0, "sequence": 94954926, "valueType": "object", "globalVariableKey": "", "id": "eg34E7flBF", "portKey": "signal"}], "isFront": false, "name": "测试开关", "outParams": {"sleep_time": "100"}, "outPorts": [{"portSequence": 1, "dataTransType": "message", "portDescription": "无具体描述", "portName": "通用端口", "portColor": "#8df0d7", "useGlobalVariable": false, "portType": 1, "sequence": 95016006, "valueType": "object", "globalVariableKey": "", "id": "snRH9vbDAK", "portKey": "signal"}], "params": {"schema": {}, "type": "default", "default": [{"paramKey": "sleep_time", "index": 0, "paramName": "休眠时间(ms)", "paramType": "number", "paramSelect": "true", "paramsId": 95029796, "paramDefaultValue": "100"}, {"paramKey": "1", "index": 1, "paramName": "1", "paramDescription": "事件号", "paramType": "object", "paramSelect": "false", "paramsId": 85219138, "paramDefaultValue": "1"}, {"paramKey": "2", "index": 2, "paramName": "2", "paramDescription": "处理号", "paramType": "object", "paramSelect": "false", "paramsId": 85228235, "paramDefaultValue": "2"}]}, "portChanged": 0, "position": {"x": 690, "y": 1600}, "serverId": 1, "serviceRequests": {"other": {"1": "1", "2": "2"}, "file": {}}, "specificOutParams": {"craft_false": "1", "craft_true_available": "false", "craft_false_available": "false", "craft_true": "1"}, "type": "node", "typeName": "测试开关"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "6ccf4964-5d6c-48d5-aa6f-cb71708e9f2f", "source": {"cell": "582474475318", "port": "zgIdGCDGZe"}, "target": {"cell": "182867127472", "port": "7rRu9lOald"}, "type": "edge"}, {"@type": "com.bx.canvas.message.NodeDTO", "categoryType": "image", "componentId": "919694131565", "craft": "false", "currentPattern": "model1", "dictEName": "DHJsonCheck", "dictId": 340, "dictPattern": [{"patternData": {"params": [], "port": [14103759, 14164952]}, "index": 0, "modalName": "模式1", "modalKey": "model1", "id": 14176876}], "dynamicLib": "IVS_custom", "editVersion": 0, "front": false, "groupId": "0", "inPorts": [{"portSequence": 1, "dataTransType": "message", "portDescription": "无具体描述", "portName": "Json数据", "portColor": "#eed4e9", "useGlobalVariable": false, "portType": 0, "sequence": 14103759, "valueType": "Json", "globalVariableKey": "", "id": "k2yNiqWZg2", "portKey": "recvInfo"}], "isFront": false, "name": "点焊电文校验", "outParams": {}, "outPorts": [{"portSequence": 1, "dataTransType": "message", "portDescription": "无具体描述", "portName": "Json数据", "portColor": "#eed4e9", "useGlobalVariable": false, "portType": 1, "sequence": 14164952, "valueType": "Json", "globalVariableKey": "", "id": "tzA8E6hLkC", "portKey": "sendInfo"}], "params": {"schema": {}, "type": "default", "default": []}, "portChanged": 0, "position": {"x": 1390, "y": 390}, "serverId": 1, "serviceRequests": {}, "specificOutParams": {"craft_false": "1", "craft_true_available": "false", "craft_false_available": "false", "craft_true": "1"}, "type": "node", "typeName": "点焊电文校验"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "5633f304-4569-439e-a435-46ce93645ba1", "source": {"cell": "963471776488", "port": "IUvgyHZpkg"}, "target": {"cell": "248272868819", "port": "x1fxIxsaIX"}, "type": "edge"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "32e8a81b-502e-4d49-8299-df25f20bdbbe", "source": {"cell": "793965957592", "port": "7yuXzFOBXf"}, "target": {"cell": "285125113236", "port": "DgDRPG0eCA"}, "type": "edge"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "c771824e-4cfd-4419-ab2a-e2e696c0e6cb", "source": {"cell": "963471776488", "port": "9CnEkI3e2X"}, "target": {"cell": "465148797819", "port": "0L4JEz6dNX"}, "type": "edge"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "d98e8073-6300-4008-a9ce-f76b18d8c11a", "source": {"cell": "618763974276", "port": "uMRR1v9j0F"}, "target": {"cell": "876757873293", "port": "4KB2UcrhyU"}, "type": "edge"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "c1522b0a-85ab-4c7b-8dbb-1d3d8dd37fe0", "source": {"cell": "846896937993", "port": "eNeMrpmRqL"}, "target": {"cell": "234557254182", "port": "eg34E7flBF"}, "type": "edge"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "dd7667c4-c7e0-4a00-ab52-7c2fba2900c9", "source": {"cell": "793965957592", "port": "7yuXzFOBXf"}, "target": {"cell": "712719389498", "port": "Mww70IGNbj"}, "type": "edge"}, {"@type": "com.bx.canvas.message.NodeDTO", "categoryType": "server", "componentId": "825863991957", "craft": "false", "currentPattern": "Scheduled_Reading_of_DB_Blocks", "dictEName": "Snap7_Read", "dictId": 257, "dictPattern": [{"patternData": {"params": [73802638, 73884959, 73915681, 73932295, 73946200, 73967846, 73984307, 74003820], "port": [73750511]}, "index": 0, "modalName": "西门子读取DB块模式", "modalKey": "Scheduled_Reading_of_DB_Blocks", "id": 74027400}], "dynamicLib": "IVS_communication", "editVersion": 78, "front": false, "groupId": "0", "inPorts": [], "isFront": false, "name": "Snap7服务端", "outParams": {"RackID": "0", "PLCip": "************", "SlotNum": "1", "SleepTime": "1000", "EdgeSignalIndex": "0", "StartNum": "0", "ByteLength": "0", "DBblockID": "67"}, "outPorts": [{"portSequence": 1, "dataTransType": "message", "portDescription": "无具体描述", "portName": "Json数据", "portColor": "#eed4e9", "useGlobalVariable": false, "portType": 1, "sequence": 73750511, "valueType": "Json", "globalVariableKey": "", "id": "5bVioQcEiy", "portKey": "sendInfo"}], "params": {"schema": {}, "type": "default", "default": [{"paramKey": "PLCip", "index": 0, "paramName": "PLCIP", "paramType": "string", "paramSelect": "true", "paramsId": 73802638, "paramDefaultValue": "127.0.0.1"}, {"paramKey": "<PERSON><PERSON><PERSON>", "index": 1, "paramName": "机架号", "paramType": "number", "paramSelect": "true", "paramsId": 73884959, "paramDefaultValue": "-1"}, {"paramKey": "SlotNum", "index": 2, "paramName": "插槽号", "paramType": "number", "paramSelect": "true", "paramsId": 73915681, "paramDefaultValue": "-1"}, {"paramKey": "DBblockID", "index": 3, "paramName": "DB块编号", "paramType": "number", "paramSelect": "true", "paramsId": 73932295, "paramDefaultValue": "-1"}, {"paramKey": "StartNum", "index": 4, "paramName": "起始字节索引", "paramType": "number", "paramSelect": "true", "paramsId": 73946200, "paramDefaultValue": "-1"}, {"paramKey": "ByteLength", "index": 5, "paramName": "字节长度", "paramType": "number", "paramSelect": "true", "paramsId": 73967846, "paramDefaultValue": "-1"}, {"paramKey": "EdgeSignalIndex", "index": 6, "paramName": "边沿信号索引", "paramType": "number", "paramSelect": "true", "paramsId": 73984307, "paramDefaultValue": "-1"}, {"paramKey": "SleepTime", "index": 7, "paramName": "PLC读取周期", "paramType": "number", "paramSelect": "true", "paramsId": 74003820, "paramDefaultValue": "-1"}]}, "portChanged": 0, "position": {"x": 1240, "y": 220}, "serverId": 1, "serviceRequests": {"other": {}, "file": {}}, "specificOutParams": {"craft_false": "1", "craft_true_available": "false", "craft_false_available": "false", "craft_true": "1"}, "type": "node", "typeName": "Snap7服务端"}, {"@type": "com.bx.canvas.message.NodeDTO", "categoryType": "pointCloud", "componentId": "197597297959", "craft": "false", "currentPattern": "modal1", "dictEName": "jsonsplit", "dictId": 99, "dictPattern": [{"patternData": {"params": [], "port": [0, "7q24HsU61g"]}, "modalName": "模式1", "modalKey": "modal1", "id": 1}], "dynamicLib": "IVS_comm", "editVersion": 82, "front": false, "groupId": "0", "inPorts": [{"portSequence": 1, "dataTransType": "message", "portDescription": "接收到的Json数据", "portName": "Json数据", "portColor": "#eed4e9", "useGlobalVariable": "false", "portType": 0, "sequence": 0, "valueType": "Json", "globalVariableKey": "", "id": "4843yCnfJ7", "portKey": "json"}], "isFront": false, "name": "Json拆分", "outParams": {}, "outPorts": [{"portSequence": 10, "portDescription": "新建端口描述", "portsort": "1", "portName": "1", "portColor": "#eed4e9", "connected": false, "portType": 1, "sequence": "7q24HsU61g", "valueType": "Json", "id": "pn2OlYuhMZ", "portKey": "1"}], "params": {"schema": {}, "type": "default", "default": []}, "portChanged": 3, "position": {"x": 1350, "y": 2400}, "serverId": 1, "serviceRequests": {}, "specificOutParams": {"craft_false": "1", "craft_true_available": "false", "craft_false_available": "false", "craft_true": "1"}, "type": "node", "typeName": "Json拆分"}, {"@type": "com.bx.canvas.message.NodeDTO", "categoryType": "output", "componentId": "373731774121", "craft": "false", "currentPattern": "modal1", "dictEName": "output", "dictId": 157, "dictPattern": [{"patternData": {"params": [], "port": [68811044, 68939376]}, "modalName": "模式1", "modalKey": "modal1", "id": 1}], "dynamicLib": "IVS_comm", "editVersion": 73, "front": false, "groupId": "0", "inPorts": [{"portSequence": 1, "dataTransType": "message", "portDescription": "结果信息", "portName": "Json数据", "portColor": "#eed4e9", "useGlobalVariable": false, "portType": 0, "sequence": 68811044, "valueType": "Json", "globalVariableKey": "", "id": "Fub5QARWVf", "portKey": "recvInfo"}], "isFront": false, "name": "输出组件", "outParams": {}, "outPorts": [{"portSequence": 1, "dataTransType": "message", "portDescription": "结果信息", "portName": "Json数据", "portColor": "#eed4e9", "useGlobalVariable": false, "portType": 1, "sequence": 68939376, "valueType": "Json", "globalVariableKey": "", "id": "kkPWTrXQBK", "portKey": "sendInfo"}], "params": {"schema": {}, "type": "default", "default": []}, "portChanged": 0, "position": {"x": 1070, "y": 2080}, "serverId": 1, "serviceRequests": {}, "specificOutParams": {"craft_false": "1", "craft_true_available": "false", "craft_false_available": "false", "craft_true": "1"}, "type": "node", "typeName": "输出组件"}, {"@type": "com.bx.canvas.message.NodeDTO", "categoryType": "image", "componentId": "822592254239", "craft": "false", "currentPattern": "ReadSignal", "dictEName": "Snap7_ReadSignal", "dictId": 378, "dictPattern": [{"patternData": {"params": [9083163, 9151882, 9164283, 9173889, 9185588, 9196826], "port": [9083160]}, "index": 0, "modalName": "周期读取信号灯", "modalKey": "ReadSignal", "id": 9083166}], "dynamicLib": "IVS_communication", "editVersion": 7, "front": false, "groupId": "0", "inPorts": [], "isFront": false, "name": "Snap7信号灯读取", "outParams": {"RackID": "0", "PLCip": "************", "SlotNum": "1", "SleepTime": "5000", "DBStruct": "[{\"variable_type\":\"Bool\",\"variable_name\":\"1\",\"variable_offset\":\"0.1\",\"variable_pattern\":\"1\"},{\"variable_name\":\"2\",\"variable_type\":\"Bool\",\"variable_offset\":\"0.2\",\"variable_pattern\":\"1\"},{\"variable_name\":\"3\",\"variable_type\":\"Bool\",\"variable_offset\":\"1.4\",\"variable_pattern\":\"1\"},{\"variable_name\":\"4\",\"variable_type\":\"Bool\",\"variable_offset\":\"1.5\",\"variable_pattern\":\"1\"}]", "DBblockID": "67"}, "outPorts": [{"portSequence": 1, "dataTransType": "message", "portDescription": "无具体描述", "portName": "Json数据", "portColor": "#eed4e9", "useGlobalVariable": false, "portType": 1, "sequence": 9083160, "valueType": "Json", "globalVariableKey": "", "id": "d8JbXoPuFB", "portKey": "sendInfo"}], "params": {"schema": {}, "type": "default", "default": [{"paramKey": "PLCip", "index": 0, "paramName": "PLCip", "paramType": "string", "paramSelect": "true", "paramsId": 9083163, "paramDefaultValue": ""}, {"paramKey": "<PERSON><PERSON><PERSON>", "index": 1, "paramName": "<PERSON><PERSON><PERSON>", "paramType": "string", "paramSelect": "true", "paramsId": 9151882, "paramDefaultValue": ""}, {"paramKey": "SlotNum", "index": 2, "paramName": "SlotNum", "paramType": "string", "paramSelect": "true", "paramsId": 9164283, "paramDefaultValue": ""}, {"paramKey": "DBblockID", "index": 3, "paramName": "DBblockID", "paramType": "string", "paramSelect": "true", "paramsId": 9173889, "paramDefaultValue": ""}, {"paramKey": "SleepTime", "index": 4, "paramName": "SleepTime", "paramType": "string", "paramSelect": "true", "paramsId": 9185588, "paramDefaultValue": ""}, {"paramKey": "DBStruct", "index": 5, "paramName": "DBStruct", "paramType": "DBStruct", "paramSelect": "true", "paramsId": 9196826, "paramDefaultValue": ""}]}, "portChanged": 0, "position": {"x": 1880, "y": 1850}, "serverId": 189, "serviceRequests": {"other": {}, "file": {}}, "specificOutParams": {"craft_false": "1", "craft_true_available": "false", "craft_false_available": "false", "craft_true": "1"}, "type": "node", "typeName": "Snap7信号灯读取"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "87490033-8b27-460b-9121-6621a332a43e", "source": {"cell": "597393491182", "port": "cuaPGnX40O"}, "target": {"cell": "2l2isae55gu000=", "port": "r_V1bJ3f3g"}, "type": "edge"}, {"@type": "com.bx.canvas.message.NodeDTO", "categoryType": "judge", "componentId": "846896937993", "craft": "false", "currentPattern": "modal1", "dictEName": "condition_judgment", "dictId": 93, "dictPattern": [{"patternData": {"params": [33453723, 33526528, 3548090, 91324435], "port": []}, "modalName": "模式1", "modalKey": "modal1", "id": 1}], "dynamicLib": "IVS_comm", "editVersion": 109, "front": false, "groupId": "0", "inPorts": [{"portSequence": 10, "portDescription": "新建端口描述", "portName": "新建端口", "portColor": "#62a314", "connected": false, "portType": 0, "valueType": "String", "id": "71V7tkwEuG", "portKey": "36FBixn6Fl"}], "isFront": false, "name": "条件判断", "outParams": {"output_false": "顺时针", "output_true": "逆时针", "expr": "%36FBixn6Fl%==1", "exp_way": "false"}, "outPorts": [{"portSequence": 10, "portDescription": "判断为真", "portName": "真", "portColor": "pink", "connected": false, "portType": 2, "valueType": "object", "id": "nwVJu4KNSv", "portKey": "true"}, {"portSequence": 10, "portDescription": "判断为假", "portName": "假", "portColor": "pink", "connected": false, "portType": 3, "valueType": "object", "id": "eNeMrpmRqL", "portKey": "false"}, {"portSequence": 10, "portDescription": "新建端口描述", "portName": "新建端口", "portColor": "#62a314", "connected": false, "portType": 2, "valueType": "String", "id": "Fj3gJaBcQq", "portKey": "36FBixn6Fl_true"}, {"portSequence": 10, "portDescription": "新建端口描述", "portName": "新建端口", "portColor": "#62a314", "connected": false, "portType": 3, "valueType": "String", "id": "DgxLQ0QxK7", "portKey": "36FBixn6Fl_false"}], "params": {"schema": {}, "type": "default", "default": [{"paramKey": "expr", "index": 0, "paramName": "条件表达式", "paramDescription": "表达式规则见文档", "paramType": "string", "paramSelect": "true", "paramsId": 33453723, "paramDefaultValue": "1>0"}, {"paramKey": "output_true", "index": 1, "paramName": "条件为true时输出", "paramDescription": "条件为true时输出", "paramType": "string", "paramSelect": "true", "paramsId": 33526528, "paramDefaultValue": "{}"}, {"paramKey": "output_false", "index": 2, "paramName": "条件为false时输出", "paramDescription": "条件为false时输出", "paramType": "string", "paramSelect": "true", "paramsId": 3548090, "paramDefaultValue": "{}"}, {"paramKey": "exp_way", "index": 3, "paramName": "异常时输出方向", "paramDescription": "异常时输出方向", "paramType": "boolean", "paramSelect": "true", "paramsId": 91324435, "paramDefaultValue": "false"}]}, "portChanged": 1, "position": {"x": 910, "y": 1620}, "serverId": 188, "serviceRequests": {"other": {}, "file": {}}, "specificOutParams": {"craft_false": "1", "craft_true_available": "false", "craft_false_available": "false", "craft_true": "1"}, "type": "node", "typeName": "条件判断"}, {"@type": "com.bx.canvas.message.NodeDTO", "categoryType": "pointCloud", "componentId": "967761472766", "craft": "false", "currentPattern": "modal1", "dictEName": "jsonsplit", "dictId": 99, "dictPattern": [{"patternData": {"params": [], "port": [0, "bG0XHG0SBH", "aXucrx5qfV"]}, "modalName": "模式1", "modalKey": "modal1", "id": 1}], "dynamicLib": "IVS_comm", "editVersion": 82, "front": false, "groupId": "0", "inPorts": [{"portSequence": 1, "dataTransType": "message", "portDescription": "接收到的Json数据", "portName": "Json数据", "portColor": "#eed4e9", "useGlobalVariable": false, "portType": 0, "sequence": 0, "valueType": "Json", "globalVariableKey": "", "id": "Ubv1hV08jO", "portKey": "json"}], "isFront": false, "name": "Json拆分", "outParams": {}, "outPorts": [{"portSequence": 10, "portDescription": "新建端口描述", "portsort": "1", "portName": "故障码", "portColor": "#eed4e9", "connected": false, "portType": 1, "sequence": "aXucrx5qfV", "valueType": "Json", "id": "8M4oFyi51t", "portKey": "errorCode"}, {"portSequence": 10, "portDescription": "新建端口描述", "portsort": "2", "portName": "检测结果", "portColor": "#eed4e9", "connected": false, "portType": 1, "sequence": "bG0XHG0SBH", "valueType": "Json", "id": "wsKTmLhyt0", "portKey": "outcome"}], "params": {"schema": {}, "type": "default", "default": []}, "portChanged": 3, "position": {"x": 1490, "y": 2110}, "serverId": 1, "serviceRequests": {}, "specificOutParams": {"craft_false": "1", "craft_true_available": "false", "craft_false_available": "false", "craft_true": "1"}, "type": "node", "typeName": "Json拆分"}, {"@type": "com.bx.canvas.message.NodeDTO", "categoryType": "visual", "componentId": "1wapxrnph9ce8=", "craft": "false", "currentPattern": "image_memory", "dictEName": "ImgViewer", "dictId": 303, "dictPattern": [{"patternData": {"params": [84652978], "port": [54230258, 35699749]}, "index": 1, "modalName": "内存池", "modalKey": "image_memory", "id": 52093263}], "dynamicLib": "visual", "editVersion": 73, "front": false, "groupId": "0", "inPorts": [{"portSequence": 1, "portDescription": "新建端口描述", "portName": "图片", "portColor": "#bd10e0", "useGlobalVariable": "false", "connected": false, "portType": 0, "sequence": 54230258, "valueType": "image", "globalVariableKey": "", "id": "Gv7lg9VpQX", "portKey": "info"}, {"portSequence": 1, "portDescription": "新建端口描述", "portName": "Json数据", "portColor": "#eed4e9", "useGlobalVariable": "false", "connected": false, "portType": 0, "sequence": 35699749, "valueType": "Json", "globalVariableKey": "", "id": "diAX5t1ndo", "portKey": "recvInfo"}], "isFront": false, "name": "图像展示", "outParams": {"FormStruct": "[{\"variable_name\":\"extend1\",\"variable_ExtendName\":\"coilCode\"}]"}, "outPorts": [], "params": {"schema": {}, "type": "default", "default": [{"paramKey": "FormStruct", "index": 0, "paramName": "FormStruct", "paramType": "FormStruct", "paramSelect": "true", "paramsId": 84652978, "paramDefaultValue": ""}]}, "portChanged": 3, "position": {"x": 1580, "y": 1730}, "serverId": 169, "serviceRequests": {"other": {}, "file": {}}, "specificOutParams": {"craft_false": "1", "craft_true_available": "false", "craft_false_available": "false", "craft_true": "1"}, "type": "node", "typeName": "图像展示"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "4c945740-5d79-4264-9f6c-b4e212bb4389", "source": {"cell": "495634352414", "port": "8tO8OxZbWf"}, "target": {"cell": "************", "port": "giymcyfijK"}, "type": "edge"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "033accbc-173e-4c77-aa19-fed7ca6a78d4", "source": {"cell": "967761472766", "port": "8M4oFyi51t"}, "target": {"cell": "899931471126", "port": "6Yqj7sejVG"}, "type": "edge"}, {"@type": "com.bx.canvas.message.NodeDTO", "categoryType": "visual", "componentId": "2l2isae55gu000=", "craft": "false", "currentPattern": "json", "dictEName": "SignalViewer", "dictId": 370, "dictPattern": [{"patternData": {"params": [], "port": [75808818]}, "index": 0, "modalName": "json", "modalKey": "json", "id": 75853135}], "dynamicLib": "visual", "editVersion": 31, "front": false, "groupId": "0", "inPorts": [{"portSequence": 1, "dataTransType": "message", "portDescription": "输入信号", "portName": "Json数据", "portColor": "#eed4e9", "useGlobalVariable": false, "sequence": 75808818, "portType": 0, "valueType": "Json", "globalVariableKey": "", "id": "r_V1bJ3f3g", "portKey": "info"}], "isFront": false, "name": "信号灯", "outParams": {}, "outPorts": [], "params": {"schema": {}, "type": "default", "default": []}, "portChanged": 0, "position": {"x": 1980, "y": 2340}, "serverId": 169, "serviceRequests": {}, "specificOutParams": {"craft_false": "1", "craft_true_available": "false", "craft_false_available": "false", "craft_true": "1"}, "type": "node", "typeName": "信号灯"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "39ccc8d5-3724-4a28-b0d6-d79ec7a12f8a", "source": {"cell": "618763974276", "port": "uMRR1v9j0F"}, "target": {"cell": "488936155112", "port": "2GSqKn1DJk"}, "type": "edge"}, {"@type": "com.bx.canvas.message.NodeDTO", "categoryType": "pointCloud", "componentId": "182867127472", "craft": "false", "currentPattern": "modal1", "dictEName": "josnmerge", "dictId": 110, "dictPattern": [{"patternData": {"port": [0, "nGOWa2dms6", "ZwpIxp7baM", "uoI9mIJYTf", "lOaqAvW8ZL"]}, "modalName": "模式1", "modalKey": "modal1", "id": 1}], "dynamicLib": "IVS_comm", "editVersion": 65, "front": false, "groupId": "0", "inPorts": [{"portSequence": 10, "portDescription": "新建端口描述", "portsort": "1", "portName": "角度值", "portColor": "#62a314", "connected": false, "portType": 0, "sequence": "ZwpIxp7baM", "valueType": "String", "id": "Y60qQMGd93", "portKey": "angle"}, {"portSequence": 10, "portDescription": "新建端口描述", "portsort": "2", "portName": "是否塌陷", "portColor": "#62a314", "connected": false, "portType": 0, "sequence": "nGOWa2dms6", "valueType": "String", "id": "SDP8KLWuMB", "portKey": "isFloor"}, {"portSequence": 10, "portDescription": "新建端口描述", "portsort": "3", "portName": "差值", "portColor": "#62a314", "connected": false, "portType": 0, "sequence": "lOaqAvW8ZL", "valueType": "String", "id": "uxt3vrNQCQ", "portKey": "subtract"}, {"portSequence": 10, "portDescription": "新建端口描述", "portsort": "4", "portName": "带头方向", "portColor": "#62a314", "connected": false, "portType": 0, "sequence": "uoI9mIJYTf", "valueType": "String", "id": "7rRu9lOald", "portKey": "direct"}], "isFront": false, "name": "Json合并", "outParams": {}, "outPorts": [{"portSequence": 1, "dataTransType": "message", "portDescription": "合并后的数据", "portName": "Json数据", "portColor": "#eed4e9", "useGlobalVariable": "false", "portType": 1, "sequence": 0, "valueType": "Json", "globalVariableKey": "", "id": "PdWOcIpUAW", "portKey": "json"}], "params": {"schema": {}, "type": "default", "default": []}, "portChanged": 2, "position": {"x": 370, "y": 1950}, "serverId": 1, "serviceRequests": {}, "specificOutParams": {"craft_false": "1", "craft_true_available": "false", "craft_false_available": "false", "craft_true": "1"}, "type": "node", "typeName": "Json合并"}, {"@type": "com.bx.canvas.message.NodeDTO", "categoryType": "image", "componentId": "285125113236", "craft": "false", "currentPattern": "default", "dictEName": "save", "dictId": 251, "dictPattern": [{"patternData": {"params": [], "port": [57169757]}, "modalName": "默认路径模式", "index": 0, "modalKey": "default", "id": 1}, {"patternData": {"params": [60412366], "port": [57169757]}, "index": 1, "modalName": "指定路径模式", "modalKey": "fixed_path", "id": 60365873}, {"patternData": {"params": [60412366, 50156221], "port": [57169757]}, "index": 2, "modalName": "指定路径和文件名模式", "modalKey": "fixed_pathAndName", "id": 50177931}, {"patternData": {"params": [60412366], "port": [14054172, 57169757]}, "index": 3, "modalName": "指定路径和传入文件名模式", "modalKey": "inPortFileName_infoPath", "id": 14035026}], "dynamicLib": "IVS_image_basicoperation", "editVersion": 63, "front": false, "groupId": "0", "inPorts": [{"portSequence": 1, "dataTransType": "message", "portDescription": "卷号", "portName": "Json数据", "portColor": "#eed4e9", "useGlobalVariable": "true", "sequence": 14054172, "portType": 0, "valueType": "Json", "globalVariableKey": "coilKey", "id": "bAcxUsUHgv", "portKey": "CoilNum"}, {"portSequence": 1, "dataTransType": "shareMemory", "portDescription": "无", "portName": "图片", "portColor": "#bd10e0", "useGlobalVariable": "false", "sequence": 57169757, "portType": 0, "valueType": "image", "globalVariableKey": "", "id": "DgDRPG0eCA", "portKey": "image"}], "isFront": false, "name": "图像保存", "outParams": {"FixFileName": "\"\"", "FixFilePath": "\"\""}, "outPorts": [], "params": {"schema": {}, "type": "default", "default": [{"paramKey": "FixFilePath", "index": 0, "paramName": "指定路径", "paramDescription": "指定图片保存路径", "paramType": "string", "paramSelect": "true", "paramsId": 60412366, "paramDefaultValue": "\"\""}, {"paramKey": "FixFileName", "index": 1, "paramName": "指定文件名", "paramDescription": "指定图片名", "paramType": "object", "paramSelect": "true", "paramsId": 50156221, "paramDefaultValue": "\"\""}]}, "portChanged": 0, "position": {"x": 1600, "y": 1230}, "serverId": 1, "serviceRequests": {"other": {}, "file": {}}, "specificOutParams": {"craft_false": "1", "craft_true_available": "false", "craft_false_available": "false", "craft_true": "1"}, "type": "node", "typeName": "图像保存"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "91c4f408-9835-4222-923e-87735de7b4d4", "source": {"cell": "567427463187", "port": "Lfmlkf1k5K"}, "target": {"cell": "715117172481", "port": "Bx5xIUNONG"}, "type": "edge"}, {"@type": "com.bx.canvas.message.NodeDTO", "categoryType": "pointCloud", "componentId": "465148797819", "craft": "false", "currentPattern": "modal1", "dictEName": "event_analysis", "dictId": 158, "dictPattern": [{"patternData": {"params": [], "port": [510475, "8yY8waLXzk", "hlST1K0gDg", "WzEfPOuFbd"]}, "modalName": "模式1", "modalKey": "modal1", "id": 1}], "dynamicLib": "IVS_comm", "editVersion": 61, "front": false, "groupId": "0", "inPorts": [{"portSequence": 1, "dataTransType": "message", "portDescription": "事件号", "portName": "Json数据", "portColor": "#eed4e9", "useGlobalVariable": "false", "portType": 0, "sequence": 510475, "valueType": "Json", "globalVariableKey": "", "id": "0L4JEz6dNX", "portKey": "eventName"}], "isFront": false, "name": "事件解析", "outParams": {}, "outPorts": [{"portSequence": 10, "portDescription": "新建端口描述", "portName": "103", "portColor": "#eed4e9", "connected": false, "portType": 1, "sequence": "WzEfPOuFbd", "valueType": "Json", "id": "cYcOxwIITO", "portKey": "103"}, {"portSequence": 10, "portDescription": "新建端口描述", "portName": "101", "portColor": "#eed4e9", "connected": false, "portType": 1, "sequence": "hlST1K0gDg", "valueType": "Json", "id": "r9fmFKfVY3", "portKey": "101"}, {"portSequence": 10, "portDescription": "新建端口描述", "portName": "102", "portColor": "#eed4e9", "connected": false, "portType": 1, "sequence": "8yY8waLXzk", "valueType": "Json", "id": "NKWmn8YbuA", "portKey": "102"}], "params": {"schema": {}, "type": "default", "default": []}, "portChanged": 3, "position": {"x": 1400, "y": 690}, "serverId": 1, "serviceRequests": {}, "specificOutParams": {"craft_false": "1", "craft_true_available": "false", "craft_false_available": "false", "craft_true": "1"}, "type": "node", "typeName": "事件解析"}, {"@type": "com.bx.canvas.message.NodeDTO", "categoryType": "image", "componentId": "899931471126", "craft": "false", "currentPattern": "model1", "dictEName": "DHJsonPackage", "dictId": 341, "dictPattern": [{"patternData": {"params": [], "port": [14208792, 14452406, 14459026, 14465579, 14473176]}, "index": 0, "modalName": "模式1", "modalKey": "model1", "id": 14481738}], "dynamicLib": "IVS_custom", "editVersion": 0, "front": false, "groupId": "0", "inPorts": [{"portSequence": 1, "dataTransType": "message", "portDescription": "无具体描述", "portName": "错误码", "portColor": "#eed4e9", "useGlobalVariable": "false", "portType": 0, "sequence": 14465579, "valueType": "Json", "globalVariableKey": "", "id": "6Yqj7sejVG", "portKey": "errorCode"}, {"portSequence": 1, "dataTransType": "message", "portDescription": "无具体描述", "portName": "事件号", "portColor": "#eed4e9", "useGlobalVariable": "true", "portType": 0, "sequence": 14452406, "valueType": "Json", "globalVariableKey": "eventCode", "id": "X3BKGn7G3h", "portKey": "eventName"}, {"portSequence": 1, "dataTransType": "message", "portDescription": "无具体描述", "portName": "处理号", "portColor": "#eed4e9", "useGlobalVariable": "true", "portType": 0, "sequence": 14459026, "valueType": "Json", "globalVariableKey": "handleCode", "id": "tfWayoau70", "portKey": "handleNum"}, {"portSequence": 1, "dataTransType": "message", "portDescription": "outcome内容", "portName": "结果", "portColor": "#eed4e9", "useGlobalVariable": "false", "portType": 0, "sequence": 14208792, "valueType": "Json", "globalVariableKey": "", "id": "Vz1OTdNnJ9", "portKey": "recvInfo"}], "isFront": false, "name": "点焊电文封装", "outParams": {}, "outPorts": [{"portSequence": 1, "dataTransType": "message", "portDescription": "无具体描述", "portName": "结果", "portColor": "#eed4e9", "useGlobalVariable": "false", "portType": 1, "sequence": 14473176, "valueType": "Json", "globalVariableKey": "", "id": "IERlZOOISe", "portKey": "sendInfo"}], "params": {"schema": {}, "type": "default", "default": []}, "portChanged": 0, "position": {"x": 1160, "y": 2250}, "serverId": 1, "serviceRequests": {}, "specificOutParams": {"craft_false": "1", "craft_true_available": "false", "craft_false_available": "false", "craft_true": "1"}, "type": "node", "typeName": "点焊电文封装"}, {"@type": "com.bx.canvas.message.NodeDTO", "categoryType": "client", "componentId": "842193788383", "craft": "false", "currentPattern": "json", "dictEName": "udpclient", "dictId": 5, "dictPattern": [{"patternData": {"params": [53784860, 53853998], "port": [70599034]}, "modalName": "模式1", "index": 0, "modalKey": "json", "id": 1}, {"patternData": {"params": [53784860, 53853998, 118878], "port": [70599034]}, "index": 1, "modalName": "模式2", "modalKey": "separator", "id": 52538111}], "dynamicLib": "IVS_communication", "editVersion": 152, "front": false, "groupId": "0", "inPorts": [{"portSequence": 1, "dataTransType": "message", "portDescription": "接收数据", "portName": "Json数据", "portColor": "#eed4e9", "useGlobalVariable": false, "portType": 0, "sequence": 70599034, "valueType": "Json", "globalVariableKey": "", "id": "UPksBmPAvr", "portKey": "recvInfo"}], "isFront": false, "name": "Udp客户端", "outParams": {"ip": "127.0.0.1", "port": "10012", "char": "#"}, "outPorts": [], "params": {"schema": {}, "type": "default", "default": [{"paramKey": "port", "index": 0, "paramName": "端口", "paramDescription": "发送对应的端口号", "paramType": "number", "paramsValue": ["10012"], "paramSelect": "true", "paramsId": 53784860, "paramDefaultValue": "10012"}, {"paramKey": "ip", "index": 1, "paramName": "IP地址", "paramDescription": "发送对方的IP地址", "paramType": "string", "paramsValue": ["127.0.0.1"], "paramSelect": "true", "paramsId": 53853998, "paramDefaultValue": "127.0.0.1"}, {"paramKey": "char", "index": 3, "paramName": "分割字符", "paramDescription": "用于分割的字符", "paramType": "string", "paramSelect": "true", "paramsId": 118878, "paramDefaultValue": "#"}]}, "portChanged": 0, "position": {"x": 1350, "y": 2680}, "serverId": 189, "serviceRequests": {"other": {}, "file": {}}, "specificOutParams": {"craft_false": "1", "craft_true_available": "false", "craft_false_available": "false", "craft_true": "1"}, "type": "node", "typeName": "Udp客户端"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "de067db4-75aa-4213-8262-99cb93b8c270", "source": {"cell": "582474475318", "port": "nYeMgIeq5b"}, "target": {"cell": "667365751218", "port": "OxFJ4z0Yk6"}, "type": "edge"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "2320e67a-942b-4833-8183-903cf299102d", "source": {"cell": "967761472766", "port": "wsKTmLhyt0"}, "target": {"cell": "899931471126", "port": "Vz1OTdNnJ9"}, "type": "edge"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "fc966b98-47c9-4ceb-8c79-235fd0f9cf65", "source": {"cell": "846896937993", "port": "nwVJu4KNSv"}, "target": {"cell": "234557254182", "port": "eg34E7flBF"}, "type": "edge"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "4490bc63-3ef3-4953-930f-fc1e9cf085c5", "source": {"cell": "182867127472", "port": "PdWOcIpUAW"}, "target": {"cell": "618763974276", "port": "UqRwnjpeEG"}, "type": "edge"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "f28a4383-9d13-4f1d-9ab9-38b3d8100ed3", "source": {"cell": "582474475318", "port": "zgIdGCDGZe"}, "target": {"cell": "846896937993", "port": "71V7tkwEuG"}, "type": "edge"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "2a564ee1-c09d-4e55-a69c-a8b1ea69055b", "source": {"cell": "899931471126", "port": "IERlZOOISe"}, "target": {"cell": "197597297959", "port": "4843yCnfJ7"}, "type": "edge"}, {"@type": "com.bx.canvas.message.NodeDTO", "categoryType": "image", "componentId": "582474475318", "craft": "false", "currentPattern": "mode1", "dictEName": "innercirclerecognitionByDL", "dictId": 361, "dictPattern": [{"patternData": {"params": [96516252, 96567694, 71165274, 71192325, 71219635, 93030718, 93067952], "port": [93018130, 96479549, 92800633, 92873173, 92857283, 96318816, 96495476, 71082312]}, "index": 0, "modalName": "模式1", "modalKey": "mode1", "id": 96587062}], "dynamicLib": "IVS_image_CustomizationComponents", "editVersion": 15, "front": false, "groupId": "0", "inPorts": [{"portSequence": 1, "dataTransType": "message", "portDescription": "无具体描述", "portName": "字符串", "portColor": "#62a314", "useGlobalVariable": false, "portType": 0, "sequence": 96479549, "valueType": "String", "globalVariableKey": "", "id": "FtdSBuZzzW", "portKey": "detection_result"}, {"portSequence": 1, "dataTransType": "shareMemory", "portDescription": "无具体描述", "portName": "图片", "portColor": "#bd10e0", "useGlobalVariable": false, "portType": 0, "sequence": 96318816, "valueType": "image", "globalVariableKey": "", "id": "N4MOU3bGso", "portKey": "image"}, {"portSequence": 1, "dataTransType": "shareMemory", "portDescription": "无具体描述", "portName": "图片", "portColor": "#bd10e0", "useGlobalVariable": false, "portType": 0, "sequence": 71082312, "valueType": "image", "globalVariableKey": "", "id": "pzZDZb6yQO", "portKey": "image_seg"}], "isFront": false, "name": "内圈塌陷带头识别（深度学习）", "outParams": {"droopthreshold": "20", "lenth": "50", "maxiterm": "100", "lowthreshold": "100", "width": "50", "highthreshold": "255", "interthreshold": "10"}, "outPorts": [{"portSequence": 1, "dataTransType": "message", "portDescription": "无具体描述", "portsort": "1", "portName": "角度值", "portColor": "#62a314", "useGlobalVariable": false, "portType": 1, "sequence": 92873173, "valueType": "String", "globalVariableKey": "", "id": "nYeMgIeq5b", "portKey": "FinalHeadAngle"}, {"portSequence": 1, "dataTransType": "message", "portDescription": "无具体描述", "portsort": "2", "portName": "是否塌陷", "portColor": "#62a314", "useGlobalVariable": false, "portType": 1, "sequence": 92800633, "valueType": "String", "globalVariableKey": "", "id": "nKuhZGbnkz", "portKey": "droopFlag"}, {"portSequence": 1, "dataTransType": "message", "portDescription": "无具体描述", "portsort": "3", "portName": "差值", "portColor": "#62a314", "useGlobalVariable": false, "portType": 1, "sequence": 93018130, "valueType": "String", "globalVariableKey": "", "id": "DukFnSoQ8F", "portKey": "deltaAngle"}, {"portSequence": 1, "dataTransType": "message", "portDescription": "无具体描述", "portsort": "4", "portName": "带头方向", "portColor": "#62a314", "useGlobalVariable": false, "portType": 1, "sequence": 92857283, "valueType": "String", "globalVariableKey": "", "id": "zgIdGCDGZe", "portKey": "HeadDirection"}, {"portSequence": 1, "dataTransType": "shareMemory", "portDescription": "无具体描述", "portName": "图片", "portColor": "#bd10e0", "useGlobalVariable": false, "portType": 1, "sequence": 96495476, "valueType": "image", "globalVariableKey": "", "id": "LvI3NEm6qF", "portKey": "image"}], "params": {"schema": {}, "type": "default", "default": [{"paramKey": "lowthreshold", "index": 0, "paramName": "低阈值", "paramType": "string", "paramSelect": "true", "paramsId": 96516252, "paramDefaultValue": "5"}, {"paramKey": "highthreshold", "index": 1, "paramName": "高阈值", "paramType": "string", "paramSelect": "true", "paramsId": 96567694, "paramDefaultValue": "10"}, {"paramKey": "droo<PERSON><PERSON><PERSON><PERSON>", "index": 2, "paramName": "塌陷阈值", "paramType": "string", "paramSelect": "true", "paramsId": 71165274, "paramDefaultValue": "20"}, {"paramKey": "lenth", "index": 3, "paramName": "轮廓筛选长度", "paramType": "string", "paramSelect": "true", "paramsId": 71192325, "paramDefaultValue": "50"}, {"paramKey": "width", "index": 4, "paramName": "轮廓筛选宽度", "paramType": "string", "paramSelect": "true", "paramsId": 71219635, "paramDefaultValue": "50"}, {"paramKey": "maxiterm", "index": 5, "paramName": "最大迭代次数", "paramType": "string", "paramSelect": "true", "paramsId": 93030718, "paramDefaultValue": "100"}, {"paramKey": "interthreshold", "index": 6, "paramName": "内点阈值", "paramType": "string", "paramSelect": "true", "paramsId": 93067952, "paramDefaultValue": "10"}]}, "portChanged": 0, "position": {"x": 370, "y": 1380}, "serverId": 195, "serviceRequests": {"other": {}, "file": {}}, "specificOutParams": {"craft_false": "1", "craft_true_available": "false", "craft_false_available": "false", "craft_true": "1"}, "type": "node", "typeName": "内圈塌陷带头识别（深度学习）"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "442f56f6-1000-4588-95ad-82bbc0a665c1", "source": {"cell": "899931471126", "port": "IERlZOOISe"}, "target": {"cell": "996925484463", "port": "tj1D8jmFpy"}, "type": "edge"}, {"@type": "com.bx.canvas.message.NodeDTO", "categoryType": "judge", "componentId": "581485577486", "craft": "false", "currentPattern": "modal1", "dictEName": "condition_judgment", "dictId": 93, "dictPattern": [{"patternData": {"params": [33453723, 33526528, 3548090, 91324435], "port": []}, "modalName": "模式1", "modalKey": "modal1", "id": 1}], "dynamicLib": "IVS_comm", "editVersion": 109, "front": false, "groupId": "0", "inPorts": [{"portSequence": 10, "portDescription": "新建端口描述", "portName": "新建端口", "portColor": "#62a314", "connected": false, "portType": 0, "valueType": "String", "id": "ncAhPRau3t", "portKey": "36FBixn6Fl"}], "isFront": false, "name": "条件判断", "outParams": {"output_false": "否", "output_true": "是", "expr": "%36FBixn6Fl%==1", "exp_way": "false"}, "outPorts": [{"portSequence": 10, "portDescription": "判断为真", "portName": "真", "portColor": "pink", "connected": false, "portType": 2, "valueType": "object", "id": "1Yr5CnYioG", "portKey": "true"}, {"portSequence": 10, "portDescription": "判断为假", "portName": "假", "portColor": "pink", "connected": false, "portType": 3, "valueType": "object", "id": "QTyWv5saR4", "portKey": "false"}, {"portSequence": 10, "portDescription": "新建端口描述", "portName": "新建端口", "portColor": "#62a314", "connected": false, "portType": 2, "valueType": "String", "id": "4oEQ9ppMSO", "portKey": "36FBixn6Fl_true"}, {"portSequence": 10, "portDescription": "新建端口描述", "portName": "新建端口", "portColor": "#62a314", "connected": false, "portType": 3, "valueType": "String", "id": "6bX1b1Xy7O", "portKey": "36FBixn6Fl_false"}], "params": {"schema": {}, "type": "default", "default": [{"paramKey": "expr", "index": 0, "paramName": "条件表达式", "paramDescription": "表达式规则见文档", "paramType": "string", "paramSelect": "true", "paramsId": 33453723, "paramDefaultValue": "1>0"}, {"paramKey": "output_true", "index": 1, "paramName": "条件为true时输出", "paramDescription": "条件为true时输出", "paramType": "string", "paramSelect": "true", "paramsId": 33526528, "paramDefaultValue": "{}"}, {"paramKey": "output_false", "index": 2, "paramName": "条件为false时输出", "paramDescription": "条件为false时输出", "paramType": "string", "paramSelect": "true", "paramsId": 3548090, "paramDefaultValue": "{}"}, {"paramKey": "exp_way", "index": 3, "paramName": "异常时输出方向", "paramDescription": "异常时输出方向", "paramType": "boolean", "paramSelect": "true", "paramsId": 91324435, "paramDefaultValue": "false"}]}, "portChanged": 1, "position": {"x": 60, "y": 1530}, "serverId": 188, "serviceRequests": {"other": {}, "file": {}}, "specificOutParams": {"craft_false": "1", "craft_true_available": "false", "craft_false_available": "false", "craft_true": "1"}, "type": "node", "typeName": "条件判断"}, {"@type": "com.bx.canvas.message.NodeDTO", "categoryType": "image", "componentId": "************", "craft": "false", "currentPattern": "mode1", "dictEName": "mmsegmentation", "dictId": 374, "dictPattern": [{"patternData": {"params": [73819395, 73881749, 73901594], "port": [73725275, 73806591]}, "index": 0, "modalName": "模式1", "modalKey": "mode1", "id": 73923830}], "dynamicLib": "IVS_image_deeplearning_mmlab", "editVersion": 8, "front": false, "groupId": "0", "inPorts": [{"portSequence": 1, "dataTransType": "shareMemory", "portDescription": "无具体描述", "portName": "图片", "portColor": "#bd10e0", "useGlobalVariable": false, "portType": 0, "sequence": 73725275, "valueType": "image", "globalVariableKey": "", "id": "giymcyfijK", "portKey": "image"}], "isFront": false, "name": "语义分割推理", "outParams": {"score": "0.8", "devicename": "cpu", "modelpath": "D:\\client_plus\\client_vue\\conf\\pro\\mode\\neiquan_Mask2former_new"}, "outPorts": [{"portSequence": 1, "dataTransType": "shareMemory", "portDescription": "无具体描述", "portName": "图片", "portColor": "#bd10e0", "useGlobalVariable": false, "portType": 1, "sequence": 73806591, "valueType": "image", "globalVariableKey": "", "id": "FRbE4Xld5v", "portKey": "image_seg"}], "params": {"schema": {}, "type": "default", "default": [{"paramKey": "devicename", "index": 0, "paramName": "推理设备名称", "paramType": "string", "paramSelect": "true", "paramsId": 73819395, "paramDefaultValue": "cpu"}, {"paramKey": "modelpath", "index": 1, "paramName": "模型权重路径", "paramType": "string", "paramSelect": "true", "paramsId": 73881749, "paramDefaultValue": "\"\""}, {"paramKey": "score", "index": 2, "paramName": "得分", "paramType": "string", "paramSelect": "true", "paramsId": 73901594, "paramDefaultValue": "0.8"}]}, "portChanged": 0, "position": {"x": 900, "y": 1260}, "serverId": 196, "serviceRequests": {"other": {}, "file": {}}, "specificOutParams": {"craft_false": "1", "craft_true_available": "false", "craft_false_available": "false", "craft_true": "1"}, "type": "node", "typeName": "语义分割推理"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "bb6af192-1333-490f-8b80-f7db3ed9fec1", "source": {"cell": "495634352414", "port": "8tO8OxZbWf"}, "target": {"cell": "949921624677", "port": "5SbEcMhOex"}, "type": "edge"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "2dd4da76-31fb-4bfa-aaf8-f8610deea5ff", "source": {"cell": "************", "port": "InMm4FZla7"}, "target": {"cell": "715117172481", "port": "Bx5xIUNONG"}, "type": "edge"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "bcf331ea-103a-4ba4-bc66-d0dc02435d11", "source": {"cell": "996925484463", "port": "PvBuWIf4hj"}, "target": {"cell": "842193788383", "port": "UPksBmPAvr"}, "type": "edge"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "6aa02a79-5804-46cc-9a10-1a6d2b1c9013", "source": {"cell": "949921624677", "port": "JfHZ3sTwiN"}, "target": {"cell": "582474475318", "port": "FtdSBuZzzW"}, "type": "edge"}, {"@type": "com.bx.canvas.message.NodeDTO", "categoryType": "visual", "componentId": "424zxm5ebmu000=", "craft": "false", "currentPattern": "json", "dictEName": "SignalViewer", "dictId": 370, "dictPattern": [{"patternData": {"params": [], "port": [75808818]}, "index": 0, "modalName": "json", "modalKey": "json", "id": 75853135}], "dynamicLib": "visual", "editVersion": 31, "front": false, "groupId": "0", "inPorts": [{"portSequence": 1, "dataTransType": "message", "portDescription": "输入信号", "portName": "Json数据", "portColor": "#eed4e9", "useGlobalVariable": false, "sequence": 75808818, "portType": 0, "valueType": "Json", "globalVariableKey": "", "id": "JcLxPsK-lh", "portKey": "info"}], "isFront": false, "name": "信号灯", "outParams": {}, "outPorts": [], "params": {"schema": {}, "type": "default", "default": []}, "portChanged": 0, "position": {"x": 2140, "y": 2340}, "serverId": 169, "serviceRequests": {}, "specificOutParams": {"craft_false": "1", "craft_true_available": "false", "craft_false_available": "false", "craft_true": "1"}, "type": "node", "typeName": "信号灯"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "5bf81682-a0a9-451b-a02b-662a33c9db14", "source": {"cell": "248272868819", "port": "kjFkFa5MaO"}, "target": {"cell": "1wapxrnph9ce8=", "port": "diAX5t1ndo"}, "type": "edge"}, {"@type": "com.bx.canvas.message.NodeDTO", "categoryType": "image", "componentId": "495634352414", "craft": "false", "currentPattern": "mode1", "dictEName": "rotate", "dictId": 345, "dictPattern": [{"patternData": {"params": [87585329], "port": [87519472, 87576724]}, "modalName": "模式1", "index": 0, "modalKey": "mode1", "id": 1}], "dynamicLib": "IVS_image_geomestrytransformation", "editVersion": 8, "front": false, "groupId": "0", "inPorts": [{"portSequence": 1, "dataTransType": "shareMemory", "portDescription": "无具体描述", "portName": "图片", "portColor": "#bd10e0", "useGlobalVariable": false, "portType": 0, "sequence": 87519472, "valueType": "image", "globalVariableKey": "", "id": "XfZr7mIyvX", "portKey": "image"}], "isFront": false, "name": "图像旋转", "outParams": {"rotatecode": "2"}, "outPorts": [{"portSequence": 1, "dataTransType": "shareMemory", "portDescription": "无具体描述", "portName": "图片", "portColor": "#bd10e0", "useGlobalVariable": false, "portType": 1, "sequence": 87576724, "valueType": "image", "globalVariableKey": "", "id": "8tO8OxZbWf", "portKey": "image"}], "params": {"schema": {}, "type": "default", "default": [{"paramKey": "rotatecode", "index": 0, "paramName": "旋转角度", "paramType": "string", "paramSelect": "true", "paramsId": 87585329, "paramDefaultValue": "2"}]}, "portChanged": 0, "position": {"x": 1050, "y": 1060}, "serverId": 1, "serviceRequests": {"other": {}, "file": {}}, "specificOutParams": {"craft_false": "1", "craft_true_available": "false", "craft_false_available": "false", "craft_true": "1"}, "type": "node", "typeName": "图像旋转"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "02cd7f63-d902-4e6e-b07d-6f06c87a8b71", "source": {"cell": "488936155112", "port": "bIe4vNePqB"}, "target": {"cell": "373731774121", "port": "Fub5QARWVf"}, "type": "edge"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "cab8f8e1-137f-4cf5-a510-adbb1800b0ed", "source": {"cell": "793965957592", "port": "7yuXzFOBXf"}, "target": {"cell": "495634352414", "port": "XfZr7mIyvX"}, "type": "edge"}, {"@type": "com.bx.canvas.message.NodeDTO", "categoryType": "image", "componentId": "793965957592", "craft": "false", "currentPattern": "model2", "dictEName": "mv_camera", "dictId": 243, "dictPattern": [{"patternData": {"params": [45844317, 45920638, 45991447, 46044256, 46954144, 13640491, 13785010, 13856604], "port": [45785677, 45590809]}, "index": 0, "modalName": "图片共享内存保存", "modalKey": "model1", "map_row_parentKey": "", "id": 46068486}, {"patternData": {"params": [45844317, 45920638, 45991447, 46044256, 46954144, 13640491, 13785010, 13856604], "port": [45785677, 45590809]}, "index": 1, "modalName": "图片内存池保存", "modalKey": "model2", "id": 90071374}], "dynamicLib": "IVS_datacollect", "editVersion": 29, "front": false, "groupId": "0", "inPorts": [{"portSequence": 1, "dataTransType": "message", "portDescription": "无具体描述", "portName": "Json数据", "portColor": "#eed4e9", "useGlobalVariable": false, "portType": 0, "sequence": 45590809, "valueType": "Json", "globalVariableKey": "", "id": "ZcRg1cUnXV", "portKey": "key_in"}], "isFront": false, "name": "GigE相机数据采集", "outParams": {"m_dExposureEdit": "5000", "m_fGammaEdit": "0.45", "m_strNetExport": "*************", "m_strGammaSelector": "1", "m_dGainEdit": "1", "m_strCurrentIp": "*************", "m_strGammaEnable": "1", "m_nOutTime": "1000"}, "outPorts": [{"portSequence": 1, "dataTransType": "shareMemory", "portDescription": "无具体描述", "portName": "图片", "portColor": "#bd10e0", "useGlobalVariable": false, "portType": 1, "sequence": 45785677, "valueType": "image", "globalVariableKey": "", "id": "7yuXzFOBXf", "portKey": "CMv_shard"}], "params": {"schema": {}, "type": "default", "default": [{"paramKey": "m_strCurrentIp", "index": 0, "paramName": "相机端ip", "paramDescription": "相机IP地址", "paramType": "string", "paramSelect": "true", "paramsId": 45844317, "paramDefaultValue": "***********"}, {"paramKey": "m_strNetExport", "index": 1, "paramName": "电脑端ip", "paramDescription": "cp端ip", "paramType": "string", "paramSelect": "true", "paramsId": 45920638, "paramDefaultValue": "*************"}, {"paramKey": "m_dExposureEdit", "index": 2, "paramName": "曝光时间编辑", "paramDescription": "曝光时间编辑", "paramType": "string", "paramSelect": "true", "paramsId": 45991447, "paramDefaultValue": "5000"}, {"paramKey": "m_dGainEdit", "index": 3, "paramName": "获取增益编辑", "paramDescription": "获取增益编辑", "paramType": "string", "paramSelect": "true", "paramsId": 46044256, "paramDefaultValue": "1"}, {"paramKey": "m_nOutTime", "index": 4, "paramName": "超时时间", "paramDescription": "获取数据的超时时间(ms)", "paramType": "string", "paramSelect": "true", "paramsId": 46954144, "paramDefaultValue": "1000"}, {"paramKey": "m_strGammaEnable", "index": 5, "paramName": "Gamma(伽马)使能", "paramDescription": "设置Gamma(伽马)校正（0为关闭，1为开启）", "paramType": "string", "paramSelect": "true", "paramsId": 13640491, "paramDefaultValue": "1"}, {"paramKey": "m_strGammaSelector", "index": 6, "paramName": "Gamma(伽马) 选择", "paramDescription": "设置Gamma(伽马)校正GammaSelector模式选择（1:User2:sRGB）", "paramType": "string", "paramSelect": "true", "paramsId": 13785010, "paramDefaultValue": "1"}, {"paramKey": "m_fGammaEdit", "index": 7, "paramName": "Gamma(伽马)数值调节", "paramDescription": "伽马调节编辑", "paramType": "string", "paramSelect": "true", "paramsId": 13856604, "paramDefaultValue": "0.45"}]}, "portChanged": 0, "position": {"x": 1280, "y": 940}, "serverId": 1, "serviceRequests": {"other": {}, "file": {}}, "specificOutParams": {"craft_false": "1", "craft_true_available": "false", "craft_false_available": "false", "craft_true": "1"}, "type": "node", "typeName": "GigE相机数据采集"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "f1a95cd2-d12e-4928-a36d-f3c177c6499a", "source": {"cell": "597393491182", "port": "Jjah1j8M9p"}, "target": {"cell": "19y0k7xfh30g00=", "port": "seZlklZia2"}, "type": "edge"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "646d5b43-69a6-4233-91ae-10d52bf0d983", "source": {"cell": "234557254182", "port": "snRH9vbDAK"}, "target": {"cell": "667365751218", "port": "XeYFUcYy1R"}, "type": "edge"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "22a4dd8b-d335-403d-9391-0393fa92f78c", "source": {"cell": "197597297959", "port": "pn2OlYuhMZ"}, "target": {"cell": "567427463187", "port": "xWacOraywg"}, "type": "edge"}, {"@type": "com.bx.canvas.message.NodeDTO", "categoryType": "visual", "componentId": "19y0k7xfh30g00=", "craft": "false", "currentPattern": "json", "dictEName": "SignalViewer", "dictId": 370, "dictPattern": [{"patternData": {"params": [], "port": [75808818]}, "index": 0, "modalName": "json", "modalKey": "json", "id": 75853135}], "dynamicLib": "visual", "editVersion": 31, "front": false, "groupId": "0", "inPorts": [{"portSequence": 1, "dataTransType": "message", "portDescription": "输入信号", "portName": "Json数据", "portColor": "#eed4e9", "useGlobalVariable": false, "sequence": 75808818, "portType": 0, "valueType": "Json", "globalVariableKey": "", "id": "seZlklZia2", "portKey": "info"}], "isFront": false, "name": "信号灯", "outParams": {}, "outPorts": [], "params": {"schema": {}, "type": "default", "default": []}, "portChanged": 0, "position": {"x": 1820, "y": 2340}, "serverId": 169, "serviceRequests": {}, "specificOutParams": {"craft_false": "1", "craft_true_available": "false", "craft_false_available": "false", "craft_true": "1"}, "type": "node", "typeName": "信号灯"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "74f3b5e1-e417-459b-9a4a-54ec163e81a9", "source": {"cell": "899931471126", "port": "IERlZOOISe"}, "target": {"cell": "567427463187", "port": "AVJsdVc0sr"}, "type": "edge"}, {"@type": "com.bx.canvas.message.NodeDTO", "categoryType": "pointCloud", "componentId": "248272868819", "craft": "false", "currentPattern": "modal1", "dictEName": "josnmerge", "dictId": 110, "dictPattern": [{"patternData": {"params": [], "port": [0, "SX1mh1iCRM"]}, "modalName": "模式1", "modalKey": "modal1", "id": 1}], "dynamicLib": "IVS_comm", "editVersion": 73, "front": false, "groupId": "0", "inPorts": [{"portSequence": 10, "portDescription": "新建端口描述", "portName": "新建端口", "portColor": "#eed4e9", "useGlobalVariable": "false", "connected": false, "portType": 0, "sequence": "SX1mh1iCRM", "valueType": "Json", "globalVariableKey": "", "id": "x1fxIxsaIX", "portKey": "extend1"}], "isFront": false, "name": "Json合并", "outParams": {}, "outPorts": [{"portSequence": 1, "dataTransType": "message", "portDescription": "合并后的数据", "portName": "Json数据", "portColor": "#eed4e9", "useGlobalVariable": "false", "portType": 1, "sequence": 0, "valueType": "Json", "globalVariableKey": "", "id": "kjFkFa5MaO", "portKey": "json"}], "params": {"schema": {}, "type": "default", "default": []}, "portChanged": 2, "position": {"x": 1870, "y": 1520}, "serverId": 188, "serviceRequests": {}, "specificOutParams": {"craft_false": "1", "craft_true_available": "false", "craft_false_available": "false", "craft_true": "1"}, "type": "node", "typeName": "Json合并"}, {"@type": "com.bx.canvas.message.NodeDTO", "categoryType": "input", "componentId": "963471776488", "craft": "false", "currentPattern": "modal1", "dictEName": "input", "dictId": 156, "dictPattern": [{"patternData": {"port": [68725798, "t8L3mazjtF", "r0HBuQQD1F", "FzRyWnoPGb"]}, "modalName": "模式1", "modalKey": "modal1", "id": 1}], "dynamicLib": "IVS_comm", "editVersion": 74, "front": false, "groupId": "0", "inPorts": [{"portSequence": 1, "dataTransType": "message", "portDescription": "输入消息", "portName": "Json数据", "portColor": "#eed4e9", "useGlobalVariable": "false", "portType": 0, "sequence": 68725798, "valueType": "Json", "globalVariableKey": "", "id": "61I6fNkOdC", "portKey": "recvInfo"}], "isFront": false, "name": "输入组件", "outParams": {}, "outPorts": [{"portSequence": 10, "portDescription": "新建端口描述", "portName": "事件号", "portColor": "#eed4e9", "useGlobalVariable": "true", "connected": false, "portType": 1, "sequence": "t8L3mazjtF", "valueType": "Json", "globalVariableKey": "eventCode", "id": "9CnEkI3e2X", "portKey": "eventName"}, {"portSequence": 10, "portDescription": "新建端口描述", "portName": "处理号", "portColor": "#eed4e9", "useGlobalVariable": "true", "connected": false, "portType": 1, "sequence": "r0HBuQQD1F", "valueType": "Json", "globalVariableKey": "handleCode", "id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "portKey": "handleNum"}, {"portSequence": 10, "portDescription": "新建端口描述", "portName": "钢卷号", "portColor": "#eed4e9", "useGlobalVariable": "true", "connected": false, "portType": 1, "sequence": "FzRyWnoPGb", "valueType": "Json", "globalVariableKey": "coilKey", "id": "IUvgyHZpkg", "portKey": "coilCode"}], "params": {"schema": {}, "type": "default", "default": []}, "portChanged": 3, "position": {"x": 1610, "y": 490}, "serverId": 1, "serviceRequests": {}, "specificOutParams": {"craft_false": "1", "craft_true_available": "false", "craft_false_available": "false", "craft_true": "1"}, "type": "node", "typeName": "输入组件"}, {"@type": "com.bx.canvas.message.NodeDTO", "categoryType": "switch", "componentId": "488936155112", "craft": "false", "currentPattern": "modal1", "dictEName": "Myswitch", "dictId": 178, "dictPattern": [{"patternData": {"params": [95029796, 85219138, 85228235], "port": [94954926, 95016006]}, "modalName": "模式1", "modalKey": "modal1", "id": 1}], "dynamicLib": "IVS_comm", "editVersion": 46, "front": false, "groupId": "0", "inPorts": [{"portSequence": 1, "dataTransType": "message", "portDescription": "无具体描述", "portName": "通用端口", "portColor": "#8df0d7", "useGlobalVariable": false, "portType": 0, "sequence": 94954926, "valueType": "object", "globalVariableKey": "", "id": "2GSqKn1DJk", "portKey": "signal"}], "isFront": false, "name": "测试开关", "outParams": {"sleep_time": "1000"}, "outPorts": [{"portSequence": 1, "dataTransType": "message", "portDescription": "无具体描述", "portName": "通用端口", "portColor": "#8df0d7", "useGlobalVariable": false, "portType": 1, "sequence": 95016006, "valueType": "object", "globalVariableKey": "", "id": "bIe4vNePqB", "portKey": "signal"}], "params": {"schema": {}, "type": "default", "default": [{"paramKey": "sleep_time", "index": 0, "paramName": "休眠时间(ms)", "paramType": "number", "paramSelect": "true", "paramsId": 95029796, "paramDefaultValue": "100"}, {"paramKey": "1", "index": 1, "paramName": "1", "paramDescription": "事件号", "paramType": "object", "paramSelect": "false", "paramsId": 85219138, "paramDefaultValue": "1"}, {"paramKey": "2", "index": 2, "paramName": "2", "paramDescription": "处理号", "paramType": "object", "paramSelect": "false", "paramsId": 85228235, "paramDefaultValue": "2"}]}, "portChanged": 0, "position": {"x": 1540, "y": 1910}, "serverId": 1, "serviceRequests": {"other": {"1": "1", "2": "2"}, "file": {}}, "specificOutParams": {"craft_false": "1", "craft_true_available": "false", "craft_false_available": "false", "craft_true": "1"}, "type": "node", "typeName": "测试开关"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "006ddbc3-7697-455e-9aac-b521bcbdd32e", "source": {"cell": "582474475318", "port": "DukFnSoQ8F"}, "target": {"cell": "667365751218", "port": "LN199ZYFVE"}, "type": "edge"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "465f51d9-e83e-4b47-bfe5-e3a04461a385", "source": {"cell": "************", "port": "FRbE4Xld5v"}, "target": {"cell": "582474475318", "port": "pzZDZb6yQO"}, "type": "edge"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "af732745-f6bc-40f0-a4be-36b24176cbf8", "source": {"cell": "582474475318", "port": "nKuhZGbnkz"}, "target": {"cell": "182867127472", "port": "SDP8KLWuMB"}, "type": "edge"}, {"@type": "com.bx.canvas.message.NodeDTO", "categoryType": "switch", "componentId": "256176733178", "craft": "false", "currentPattern": "modal1", "dictEName": "Myswitch", "dictId": 178, "dictPattern": [{"patternData": {"params": [95029796, 85219138, 85228235], "port": [94954926, 95016006]}, "modalName": "模式1", "modalKey": "modal1", "id": 1}], "dynamicLib": "IVS_comm", "editVersion": 46, "front": false, "groupId": "0", "inPorts": [{"portSequence": 1, "dataTransType": "message", "portDescription": "无具体描述", "portName": "通用端口", "portColor": "#8df0d7", "useGlobalVariable": "false", "portType": 0, "sequence": 94954926, "valueType": "object", "globalVariableKey": "", "id": "MM740tgSrn", "portKey": "signal"}], "isFront": false, "name": "测试开关", "outParams": {"sleep_time": "100"}, "outPorts": [{"portSequence": 1, "dataTransType": "message", "portDescription": "无具体描述", "portName": "通用端口", "portColor": "#8df0d7", "useGlobalVariable": "false", "portType": 1, "sequence": 95016006, "valueType": "object", "globalVariableKey": "", "id": "tArv0G7IAx", "portKey": "signal"}], "params": {"schema": {}, "type": "default", "default": [{"paramKey": "sleep_time", "index": 0, "paramName": "休眠时间(ms)", "paramType": "number", "paramSelect": "true", "paramsId": 95029796, "paramDefaultValue": "100"}, {"paramKey": "1", "index": 1, "paramName": "1", "paramDescription": "事件号", "paramType": "object", "paramSelect": "false", "paramsId": 85219138, "paramDefaultValue": "1"}, {"paramKey": "2", "index": 2, "paramName": "2", "paramDescription": "处理号", "paramType": "object", "paramSelect": "false", "paramsId": 85228235, "paramDefaultValue": "2"}]}, "portChanged": 0, "position": {"x": -190, "y": 1560}, "serverId": 1, "serviceRequests": {"other": {"1": "1", "2": "2"}, "file": {}}, "specificOutParams": {"craft_false": "1", "craft_true_available": "false", "craft_false_available": "false", "craft_true": "1"}, "type": "node", "typeName": "测试开关"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "d4a9cb63-cb5d-4815-b8e5-b45ffc722598", "source": {"cell": "582474475318", "port": "nYeMgIeq5b"}, "target": {"cell": "182867127472", "port": "Y60qQMGd93"}, "type": "edge"}, {"@type": "com.bx.canvas.message.NodeDTO", "categoryType": "image", "componentId": "712719389498", "craft": "false", "currentPattern": "modal1", "dictEName": "GetTimeDate", "dictId": 376, "dictPattern": [{"patternData": {"params": [], "port": [35431206, 35482562]}, "modalName": "模式1", "modalKey": "modal1", "id": 1}], "dynamicLib": "IVS_comm", "editVersion": 2, "front": false, "groupId": "0", "inPorts": [{"portSequence": 1, "dataTransType": "message", "portDescription": "无具体描述", "portName": "通用端口", "portColor": "#8df0d7", "useGlobalVariable": false, "portType": 0, "sequence": 35482562, "valueType": "object", "globalVariableKey": "", "id": "Mww70IGNbj", "portKey": "signal"}], "isFront": false, "name": "当前时间", "outParams": {}, "outPorts": [{"portSequence": 1, "dataTransType": "message", "portDescription": "无具体描述", "portName": "Json数据", "portColor": "#eed4e9", "useGlobalVariable": false, "portType": 1, "sequence": 35431206, "valueType": "Json", "globalVariableKey": "", "id": "DMKQQqbLIy", "portKey": "CurrentTime"}], "params": {"schema": {}, "type": "default", "default": []}, "portChanged": 0, "position": {"x": 1300, "y": 1290}, "serverId": 188, "serviceRequests": {}, "specificOutParams": {"craft_false": "1", "craft_true_available": "false", "craft_false_available": "false", "craft_true": "1"}, "type": "node", "typeName": "当前时间"}, {"@type": "com.bx.canvas.message.NodeDTO", "categoryType": "visual", "componentId": "1zsnpa1rxfk000=", "craft": "false", "currentPattern": "model1", "dictEName": "InputsForm", "dictId": 373, "dictPattern": [{"patternData": {"params": [66125792], "port": [66107011]}, "index": 0, "modalName": "model1", "modalKey": "model1", "id": 66155247}], "dynamicLib": "visual", "editVersion": 18, "front": false, "groupId": "0", "inPorts": [{"portSequence": 1, "dataTransType": "message", "portName": "Json数据", "portColor": "#eed4e9", "useGlobalVariable": false, "sequence": 66107011, "portType": 0, "valueType": "Json", "globalVariableKey": "", "id": "_ghtVDKib3", "portKey": "recvInfo"}], "isFront": false, "name": "表格组件", "outParams": {"FormStruct": "[{\"variable_name\":\"angle\",\"variable_ExtendName\":\"抚平角度\"},{\"variable_name\":\"isFloor\",\"variable_ExtendName\":\"是否塌陷\"},{\"variable_name\":\"direct\",\"variable_ExtendName\":\"带头方向\"},{\"variable_name\":\"time\",\"variable_ExtendName\":\"拍摄时间\"},{\"variable_name\":\"coilNum\",\"variable_ExtendName\":\"钢卷号\"},{\"variable_name\":\"subtract\",\"variable_ExtendName\":\"差值\"}]"}, "outPorts": [], "params": {"schema": {}, "type": "default", "default": [{"paramKey": "FormStruct", "index": 0, "paramName": "表格参数", "paramType": "FormStruct", "paramSelect": "true", "paramsId": 66125792, "paramDefaultValue": ""}]}, "portChanged": 0, "position": {"x": -290, "y": 2150}, "serverId": 169, "serviceRequests": {"other": {}, "file": {}}, "specificOutParams": {"craft_false": "1", "craft_true_available": "false", "craft_false_available": "false", "craft_true": "1"}, "type": "node", "typeName": "表格组件"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "bfe95fc2-e014-4901-932b-be364785f467", "source": {"cell": "373731774121", "port": "kkPWTrXQBK"}, "target": {"cell": "967761472766", "port": "Ubv1hV08jO"}, "type": "edge"}, {"@type": "com.bx.canvas.message.NodeDTO", "categoryType": "pointCloud", "componentId": "618763974276", "craft": "false", "currentPattern": "modal1", "dictEName": "josnmerge", "dictId": 110, "dictPattern": [{"patternData": {"params": [], "port": [0, "52Mvd971At", "OU1DfwkC0x", "fzxiJSFKqS"]}, "modalName": "模式1", "modalKey": "modal1", "id": 1}], "dynamicLib": "IVS_comm", "editVersion": 65, "front": false, "groupId": "0", "inPorts": [{"portSequence": 10, "portDescription": "新建端口描述", "portName": "eventName", "portColor": "#eed4e9", "useGlobalVariable": "true", "connected": false, "portType": 0, "sequence": "52Mvd971At", "valueType": "Json", "globalVariableKey": "eventCode", "id": "df0FZYDBQU", "portKey": "eventName"}, {"portSequence": 10, "portDescription": "新建端口描述", "portName": "handleNum", "portColor": "#eed4e9", "useGlobalVariable": "true", "connected": false, "portType": 0, "sequence": "OU1DfwkC0x", "valueType": "Json", "globalVariableKey": "handleCode", "id": "YnaJeEJ1VH", "portKey": "handleNum"}, {"portSequence": 10, "portDescription": "新建端口描述", "portName": "outcome", "portColor": "#eed4e9", "connected": false, "portType": 0, "sequence": "fzxiJSFKqS", "valueType": "Json", "id": "UqRwnjpeEG", "portKey": "outcome"}], "isFront": false, "name": "Json合并", "outParams": {}, "outPorts": [{"portSequence": 1, "dataTransType": "message", "portDescription": "合并后的数据", "portName": "Json数据", "portColor": "#eed4e9", "useGlobalVariable": "false", "portType": 1, "sequence": 0, "valueType": "Json", "globalVariableKey": "", "id": "uMRR1v9j0F", "portKey": "json"}], "params": {"schema": {}, "type": "default", "default": []}, "portChanged": 2, "position": {"x": 1180, "y": 1870}, "serverId": 1, "serviceRequests": {}, "specificOutParams": {"craft_false": "1", "craft_true_available": "false", "craft_false_available": "false", "craft_true": "1"}, "type": "node", "typeName": "Json合并"}, {"@type": "com.bx.canvas.message.NodeDTO", "categoryType": "pointCloud", "componentId": "597393491182", "craft": "false", "currentPattern": "modal1", "dictEName": "jsonsplit", "dictId": 99, "dictPattern": [{"patternData": {"params": [], "port": [0, "Be2RKljWPK", "PJidBBlo2u", "Up01seTrXC", "93rV2MAaUW"]}, "modalName": "模式1", "modalKey": "modal1", "id": 1}], "dynamicLib": "IVS_comm", "editVersion": 90, "front": false, "groupId": "0", "inPorts": [{"portSequence": 1, "dataTransType": "message", "portDescription": "接收到的Json数据", "portName": "Json数据", "portColor": "#eed4e9", "useGlobalVariable": "false", "sequence": 0, "portType": 0, "valueType": "Json", "globalVariableKey": "", "id": "WYud1uQhxd", "portKey": "json"}], "isFront": false, "name": "Json拆分", "outParams": {}, "outPorts": [{"portSequence": 10, "portDescription": "新建端口描述", "portName": "0.1", "portColor": "#eed4e9", "connected": false, "portType": 1, "sequence": "93rV2MAaUW", "valueType": "Json", "id": "ekTyiQMny7", "portKey": "0.1"}, {"portSequence": 10, "portDescription": "新建端口描述", "portName": "0.2", "portColor": "#eed4e9", "connected": false, "portType": 1, "sequence": "Up01seTrXC", "valueType": "Json", "id": "Jjah1j8M9p", "portKey": "0.2"}, {"portSequence": 10, "portDescription": "新建端口描述", "portName": "1.4", "portColor": "#eed4e9", "connected": false, "portType": 1, "sequence": "PJidBBlo2u", "valueType": "Json", "id": "cuaPGnX40O", "portKey": "1.4"}, {"portSequence": 10, "portDescription": "新建端口描述", "portName": "1.5", "portColor": "#eed4e9", "connected": false, "portType": 1, "sequence": "Be2RKljWPK", "valueType": "Json", "id": "GtXCXmGKs0", "portKey": "1.5"}], "params": {"schema": {}, "type": "default", "default": []}, "portChanged": 3, "position": {"x": 1800, "y": 2070}, "serverId": 188, "serviceRequests": {}, "specificOutParams": {"craft_false": "1", "craft_true_available": "false", "craft_false_available": "false", "craft_true": "1"}, "type": "node", "typeName": "Json拆分"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "b248a19b-f891-4823-bea4-ebcbc22eae84", "source": {"cell": "495634352414", "port": "8tO8OxZbWf"}, "target": {"cell": "582474475318", "port": "N4MOU3bGso"}, "type": "edge"}, {"@type": "com.bx.canvas.message.NodeDTO", "categoryType": "pointCloud", "componentId": "876757873293", "craft": "false", "currentPattern": "json", "dictEName": "writefile", "dictId": 41, "dictPattern": [{"patternData": {"params": [], "port": [0]}, "modalName": "json", "index": 0, "modalKey": "json", "id": 1}, {"patternData": {"params": [58151322], "port": [0]}, "index": 1, "modalName": "字符分割", "modalKey": "separator", "id": 58139393}], "dynamicLib": "IVS_comm", "editVersion": 102, "front": false, "groupId": "0", "inPorts": [{"portSequence": 1, "dataTransType": "message", "portDescription": "用于保存文件", "portName": "通用端口", "portColor": "#8df0d7", "useGlobalVariable": false, "portType": 0, "sequence": 0, "valueType": "object", "globalVariableKey": "", "id": "4KB2UcrhyU", "portKey": "info"}], "isFront": false, "name": "保存文件", "outParams": {"char": ","}, "outPorts": [], "params": {"schema": {}, "type": "default", "default": [{"paramKey": "char", "index": 0, "paramName": "分割字符", "paramType": "string", "paramSelect": "true", "paramsId": 58151322, "paramDefaultValue": ","}]}, "portChanged": 0, "position": {"x": 910, "y": 2000}, "serverId": 1, "serviceRequests": {"other": {}, "file": {}}, "specificOutParams": {"craft_false": "1", "craft_true_available": "false", "craft_false_available": "false", "craft_true": "1"}, "type": "node", "typeName": "保存文件"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "8ff2c7af-7303-401e-85f2-562a929ea873", "source": {"cell": "919694131565", "port": "tzA8E6hLkC"}, "target": {"cell": "963471776488", "port": "61I6fNkOdC"}, "type": "edge"}, {"@type": "com.bx.canvas.message.NodeDTO", "categoryType": "pointCloud", "componentId": "667365751218", "craft": "false", "currentPattern": "modal1", "dictEName": "josnmerge", "dictId": 110, "dictPattern": [{"patternData": {"params": [], "port": [0, "nGOWa2dms6", "ZwpIxp7baM", "uoI9mIJYTf", "lOaqAvW8ZL", "yUE8nUOmVZ", "HuGVht4wEI"]}, "modalName": "模式1", "modalKey": "modal1", "id": 1}], "dynamicLib": "IVS_comm", "editVersion": 65, "front": false, "groupId": "0", "inPorts": [{"portSequence": 10, "portDescription": "新建端口描述", "portName": "钢卷号", "portColor": "#62a314", "useGlobalVariable": "true", "connected": false, "portType": 0, "sequence": "HuGVht4wEI", "valueType": "String", "globalVariableKey": "coilKey", "id": "iMtDWxxHU3", "portKey": "coilNum"}, {"portSequence": 10, "portDescription": "新建端口描述", "portName": "拍摄时间", "portColor": "#eed4e9", "connected": false, "portType": 0, "sequence": "yUE8nUOmVZ", "valueType": "Json", "id": "BkCtl71c1w", "portKey": "time"}, {"portSequence": 10, "portDescription": "新建端口描述", "portsort": "1", "portName": "角度值", "portColor": "#62a314", "connected": false, "portType": 0, "sequence": "ZwpIxp7baM", "valueType": "String", "id": "OxFJ4z0Yk6", "portKey": "angle"}, {"portSequence": 10, "portDescription": "新建端口描述", "portsort": "2", "portName": "是否塌陷", "portColor": "#62a314", "connected": false, "portType": 0, "sequence": "nGOWa2dms6", "valueType": "String", "id": "QLQlyffwD6", "portKey": "isFloor"}, {"portSequence": 10, "portDescription": "新建端口描述", "portsort": "3", "portName": "差值", "portColor": "#62a314", "connected": false, "portType": 0, "sequence": "lOaqAvW8ZL", "valueType": "String", "id": "LN199ZYFVE", "portKey": "subtract"}, {"portSequence": 10, "portDescription": "新建端口描述", "portsort": "4", "portName": "带头方向", "portColor": "#62a314", "connected": false, "portType": 0, "sequence": "uoI9mIJYTf", "valueType": "String", "id": "XeYFUcYy1R", "portKey": "direct"}], "isFront": false, "name": "Json合并", "outParams": {}, "outPorts": [{"portSequence": 1, "dataTransType": "message", "portDescription": "合并后的数据", "portName": "Json数据", "portColor": "#eed4e9", "useGlobalVariable": "false", "portType": 1, "sequence": 0, "valueType": "Json", "globalVariableKey": "", "id": "eoD6E7il7d", "portKey": "json"}], "params": {"schema": {}, "type": "default", "default": []}, "portChanged": 2, "position": {"x": -380, "y": 2020}, "serverId": 1, "serviceRequests": {}, "specificOutParams": {"craft_false": "1", "craft_true_available": "false", "craft_false_available": "false", "craft_true": "1"}, "type": "node", "typeName": "Json合并"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "a2f6176e-9092-45e4-a691-7488650e4a6a", "source": {"cell": "582474475318", "port": "nKuhZGbnkz"}, "target": {"cell": "581485577486", "port": "ncAhPRau3t"}, "type": "edge"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "5600b87e-5029-46e5-81eb-d1cc24154a8b", "source": {"cell": "581485577486", "port": "QTyWv5saR4"}, "target": {"cell": "256176733178", "port": "MM740tgSrn"}, "type": "edge"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "88208980-1301-491c-a6b2-6505fcd27e21", "source": {"cell": "712719389498", "port": "DMKQQqbLIy"}, "target": {"cell": "667365751218", "port": "BkCtl71c1w"}, "type": "edge"}, {"@type": "com.bx.canvas.message.NodeDTO", "categoryType": "image", "componentId": "************", "craft": "false", "currentPattern": "default", "dictEName": "save", "dictId": 251, "dictPattern": [{"patternData": {"params": [], "port": [57169757]}, "modalName": "默认路径模式", "index": 0, "modalKey": "default", "id": 1}, {"patternData": {"params": [60412366], "port": [57169757]}, "index": 1, "modalName": "指定路径模式", "modalKey": "fixed_path", "id": 60365873}, {"patternData": {"params": [60412366, 50156221], "port": [57169757]}, "index": 2, "modalName": "指定路径和文件名模式", "modalKey": "fixed_pathAndName", "id": 50177931}, {"patternData": {"params": [60412366], "port": [14054172, 57169757]}, "index": 3, "modalName": "指定路径和传入文件名模式", "modalKey": "inPortFileName_infoPath", "id": 14035026}], "dynamicLib": "IVS_image_basicoperation", "editVersion": 63, "front": false, "groupId": "0", "inPorts": [{"portSequence": 1, "dataTransType": "message", "portDescription": "卷号", "portName": "Json数据", "portColor": "#eed4e9", "useGlobalVariable": "true", "sequence": 14054172, "portType": 0, "valueType": "Json", "globalVariableKey": "coilKey", "id": "N1ToElI2i2", "portKey": "CoilNum"}, {"portSequence": 1, "dataTransType": "shareMemory", "portDescription": "无", "portName": "图片", "portColor": "#bd10e0", "useGlobalVariable": "false", "sequence": 57169757, "portType": 0, "valueType": "image", "globalVariableKey": "", "id": "rdSTx6Hv3v", "portKey": "image"}], "isFront": false, "name": "图像保存", "outParams": {"FixFileName": "\"\"", "FixFilePath": "\"\""}, "outPorts": [], "params": {"schema": {}, "type": "default", "default": [{"paramKey": "FixFilePath", "index": 0, "paramName": "指定路径", "paramDescription": "指定图片保存路径", "paramType": "string", "paramSelect": "true", "paramsId": 60412366, "paramDefaultValue": "\"\""}, {"paramKey": "FixFileName", "index": 1, "paramName": "指定文件名", "paramDescription": "指定图片名", "paramType": "object", "paramSelect": "true", "paramsId": 50156221, "paramDefaultValue": "\"\""}]}, "portChanged": 0, "position": {"x": 1010, "y": 1400}, "serverId": 1, "serviceRequests": {"other": {}, "file": {}}, "specificOutParams": {"craft_false": "1", "craft_true_available": "false", "craft_false_available": "false", "craft_true": "1"}, "type": "node", "typeName": "图像保存"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "3a31928e-c64d-445a-bc4e-252268c67532", "source": {"cell": "597393491182", "port": "GtXCXmGKs0"}, "target": {"cell": "424zxm5ebmu000=", "port": "JcLxPsK-lh"}, "type": "edge"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "91b8cd96-414d-4919-80ce-6079e770aeaf", "source": {"cell": "197597297959", "port": "pn2OlYuhMZ"}, "target": {"cell": "996925484463", "port": "4jm92Sn8Ke"}, "type": "edge"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "dc487a71-0bb5-44cd-9a95-ddff4d5780be", "source": {"cell": "667365751218", "port": "eoD6E7il7d"}, "target": {"cell": "1zsnpa1rxfk000=", "port": "_ghtVDKib3"}, "type": "edge"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "24a5f737-d4ba-4af6-b631-c8974968f841", "source": {"cell": "256176733178", "port": "tArv0G7IAx"}, "target": {"cell": "667365751218", "port": "QLQlyffwD6"}, "type": "edge"}, {"@type": "com.bx.canvas.message.NodeDTO", "categoryType": "visual", "componentId": "5tiufthlbgw000=", "craft": "false", "currentPattern": "json", "dictEName": "SignalViewer", "dictId": 370, "dictPattern": [{"patternData": {"params": [], "port": [75808818]}, "index": 0, "modalName": "json", "modalKey": "json", "id": 75853135}], "dynamicLib": "visual", "editVersion": 31, "front": false, "groupId": "0", "inPorts": [{"portSequence": 1, "dataTransType": "message", "portDescription": "输入信号", "portName": "Json数据", "portColor": "#eed4e9", "useGlobalVariable": false, "sequence": 75808818, "portType": 0, "valueType": "Json", "globalVariableKey": "", "id": "Wgc0_bV9-1", "portKey": "info"}], "isFront": false, "name": "信号灯", "outParams": {}, "outPorts": [], "params": {"schema": {}, "type": "default", "default": []}, "portChanged": 0, "position": {"x": 1650, "y": 2340}, "serverId": 169, "serviceRequests": {}, "specificOutParams": {"craft_false": "1", "craft_true_available": "false", "craft_false_available": "false", "craft_true": "1"}, "type": "node", "typeName": "信号灯"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "76ea1f12-fb60-4d55-85d2-75fce8b44a9b", "source": {"cell": "581485577486", "port": "1Yr5CnYioG"}, "target": {"cell": "256176733178", "port": "MM740tgSrn"}, "type": "edge"}, {"@type": "com.bx.canvas.message.NodeDTO", "categoryType": "switch", "componentId": "************", "craft": "false", "currentPattern": "model", "dictEName": "PathAllocation", "dictId": 351, "dictPattern": [{"patternData": {"params": [17874269], "port": [17762669, 17846145, 17852656]}, "index": 0, "modalName": "模式1", "modalKey": "model", "id": 17911773}], "dynamicLib": "IVS_comm", "editVersion": 0, "front": false, "groupId": "0", "inPorts": [{"portSequence": 1, "dataTransType": "message", "portDescription": "无具体描述", "portName": "通用端口", "portColor": "#8df0d7", "useGlobalVariable": false, "portType": 0, "sequence": 17762669, "valueType": "object", "globalVariableKey": "", "id": "wrWedCAe1F", "portKey": "all"}, {"portSequence": 1, "dataTransType": "message", "portDescription": "无具体描述", "portName": "Json数据", "portColor": "#eed4e9", "useGlobalVariable": false, "portType": 0, "sequence": 17852656, "valueType": "Json", "globalVariableKey": "", "id": "IryrPruIDn", "portKey": "JudgingParameters"}], "isFront": false, "name": "路径分配", "outParams": {"JudgingParameters": "103"}, "outPorts": [{"portSequence": 1, "dataTransType": "message", "portDescription": "无具体描述", "portName": "通用端口", "portColor": "#8df0d7", "useGlobalVariable": false, "portType": 1, "sequence": 17846145, "valueType": "object", "globalVariableKey": "", "id": "InMm4FZla7", "portKey": "all"}], "params": {"schema": {}, "type": "default", "default": [{"paramKey": "JudgingParameters", "index": 0, "paramName": "对照参数", "paramType": "string", "paramSelect": "true", "paramsId": 17874269, "paramDefaultValue": ""}]}, "portChanged": 0, "position": {"x": 870, "y": 2510}, "serverId": 1, "serviceRequests": {"other": {}, "file": {}}, "specificOutParams": {"craft_false": "1", "craft_true_available": "false", "craft_false_available": "false", "craft_true": "1"}, "type": "node", "typeName": "路径分配"}, {"@type": "com.bx.canvas.message.NodeDTO", "categoryType": "client", "componentId": "715117172481", "craft": "false", "currentPattern": "Scheduled_Reading_of_DB_Blocks", "dictEName": "Snap7_Write", "dictId": 260, "dictPattern": [{"patternData": {"params": [83908940, 83941204, 83955431, 83968376, 83983097, 84014207, 84031667, 86409646, 78363772], "port": [83779280]}, "index": 0, "modalName": "西门子读取DB块模式", "modalKey": "Scheduled_Reading_of_DB_Blocks", "id": 84048149}], "dynamicLib": "IVS_communication", "editVersion": 62, "front": false, "groupId": "0", "inPorts": [{"portSequence": 1, "dataTransType": "message", "portDescription": "无具体描述", "portName": "Json数据", "portColor": "#eed4e9", "useGlobalVariable": false, "sequence": 83779280, "portType": 0, "valueType": "Json", "globalVariableKey": "", "id": "Bx5xIUNONG", "portKey": "recvInfo"}], "isFront": false, "name": "Snap7客户端", "outParams": {"RackID": "0", "PLCip": "************", "SlotNum": "1", "SleepTime": "1000", "EdgeSignalIndex": "0", "StartNum": "0", "ByteLength": "0", "DBblockID": "67"}, "outPorts": [], "params": {"schema": {}, "type": "default", "default": [{"paramKey": "PLCip", "index": 0, "paramName": "PLCIP", "paramType": "string", "paramSelect": "true", "paramsId": 83908940, "paramDefaultValue": "127.0.0.1"}, {"paramKey": "<PERSON><PERSON><PERSON>", "index": 1, "paramName": "机架号", "paramType": "number", "paramSelect": "true", "paramsId": 83941204, "paramDefaultValue": "-1"}, {"paramKey": "SlotNum", "index": 2, "paramName": "插槽号", "paramType": "number", "paramSelect": "true", "paramsId": 83955431, "paramDefaultValue": "-1"}, {"paramKey": "DBblockID", "index": 3, "paramName": "DB块编号", "paramType": "number", "paramSelect": "true", "paramsId": 83968376, "paramDefaultValue": "-1"}, {"paramKey": "StartNum", "index": 4, "paramName": "起始字节索引", "paramType": "number", "paramSelect": "true", "paramsId": 83983097, "paramDefaultValue": "-1"}, {"paramKey": "EdgeSignalIndex", "index": 5, "paramName": "边沿信号索引", "paramType": "number", "paramSelect": "true", "paramsId": 84014207, "paramDefaultValue": "-1"}, {"paramKey": "SleepTime", "index": 6, "paramName": "PLC读取周期", "paramType": "number", "paramSelect": "true", "paramsId": 84031667, "paramDefaultValue": "-1"}, {"paramKey": "ByteLength", "index": 7, "paramName": "读取字节长度", "paramType": "number", "paramSelect": "true", "paramsId": 86409646, "paramDefaultValue": "-1"}, {"paramKey": "DBStruct", "index": 8, "paramName": "DB块结构体", "paramType": "DBStruct", "paramSelect": "true", "paramsId": 78363772, "paramDefaultValue": ""}]}, "portChanged": 0, "position": {"x": 1000, "y": 2700}, "serverId": 189, "serviceRequests": {"other": {}, "file": {}}, "specificOutParams": {"craft_false": "1", "craft_true_available": "false", "craft_false_available": "false", "craft_true": "1"}, "type": "node", "typeName": "Snap7客户端"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "9a4794ab-fc49-46b1-a618-6853037e6320", "source": {"cell": "822592254239", "port": "d8JbXoPuFB"}, "target": {"cell": "597393491182", "port": "WYud1uQhxd"}, "type": "edge"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "16ab0037-7aaf-4239-9874-378eb3d4fbfb", "source": {"cell": "899931471126", "port": "IERlZOOISe"}, "target": {"cell": "************", "port": "wrWedCAe1F"}, "type": "edge"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "e11ea040-5073-4f04-a739-1ac2d0d52abb", "source": {"cell": "582474475318", "port": "DukFnSoQ8F"}, "target": {"cell": "182867127472", "port": "uxt3vrNQCQ"}, "type": "edge"}, {"@type": "com.bx.canvas.message.NodeDTO", "categoryType": "switch", "componentId": "996925484463", "craft": "false", "currentPattern": "model", "dictEName": "PathAllocation", "dictId": 351, "dictPattern": [{"patternData": {"params": [17874269], "port": [17762669, 17846145, 17852656]}, "index": 0, "modalName": "模式1", "modalKey": "model", "id": 17911773}], "dynamicLib": "IVS_comm", "editVersion": 0, "front": false, "groupId": "0", "inPorts": [{"portSequence": 1, "dataTransType": "message", "portDescription": "无具体描述", "portName": "通用端口", "portColor": "#8df0d7", "useGlobalVariable": false, "portType": 0, "sequence": 17762669, "valueType": "object", "globalVariableKey": "", "id": "tj1D8jmFpy", "portKey": "all"}, {"portSequence": 1, "dataTransType": "message", "portDescription": "无具体描述", "portName": "Json数据", "portColor": "#eed4e9", "useGlobalVariable": false, "portType": 0, "sequence": 17852656, "valueType": "Json", "globalVariableKey": "", "id": "4jm92Sn8Ke", "portKey": "JudgingParameters"}], "isFront": false, "name": "路径分配", "outParams": {"JudgingParameters": "102"}, "outPorts": [{"portSequence": 1, "dataTransType": "message", "portDescription": "无具体描述", "portName": "通用端口", "portColor": "#8df0d7", "useGlobalVariable": false, "portType": 1, "sequence": 17846145, "valueType": "object", "globalVariableKey": "", "id": "PvBuWIf4hj", "portKey": "all"}], "params": {"schema": {}, "type": "default", "default": [{"paramKey": "JudgingParameters", "index": 0, "paramName": "对照参数", "paramType": "string", "paramSelect": "true", "paramsId": 17874269, "paramDefaultValue": ""}]}, "portChanged": 0, "position": {"x": 1480, "y": 2530}, "serverId": 1, "serviceRequests": {"other": {}, "file": {}}, "specificOutParams": {"craft_false": "1", "craft_true_available": "false", "craft_false_available": "false", "craft_true": "1"}, "type": "node", "typeName": "路径分配"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "d508e005-623b-4509-b08c-38a741a63d82", "source": {"cell": "************", "port": "FRbE4Xld5v"}, "target": {"cell": "************", "port": "rdSTx6Hv3v"}, "type": "edge"}, {"@type": "com.bx.canvas.message.EdgeDTO", "connectionId": "c83eef07-4b63-4633-9cd1-d506261e5462", "source": {"cell": "197597297959", "port": "pn2OlYuhMZ"}, "target": {"cell": "************", "port": "IryrPruIDn"}, "type": "edge"}], "createBy": "<PERSON><PERSON><PERSON><PERSON>", "createTime": "2024-10-08 17:04:05", "id": 761, "projectName": "点焊机器人(深度学习V3)（103）", "projectScreenshot": "/data/resources/screenshot/20241010122225A668.jpg", "sourceId": 742, "sourceName": "点焊机器人(深度学习V2)", "updateBy": "<PERSON><PERSON><PERSON><PERSON>", "updateTime": "2024-10-10 12:22:25", "@version": "1.9.1", "projectId": "761"}, "handleNum": "1", "isAsync": "1", "componentEName": "project761"}}, "eventName": "1001", "from": "ptplat"}