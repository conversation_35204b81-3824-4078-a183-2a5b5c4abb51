package com.ruoyi.common.config.deserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import java.io.IOException;

/**
 * 自定义Double反序列化器
 * 当JSON中没有该字段时，保持为null而不是默认值0.0
 * 
 * <AUTHOR>
 */
public class NullableDoubleDeserializer extends JsonDeserializer<Double> {
    
    @Override
    public Double deserialize(JsonParser p, DeserializationContext ctxt) 
            throws IOException, JsonProcessingException {
        String text = p.getText();
        if (text == null || text.trim().isEmpty()) {
            return null;
        }
        try {
            return Double.parseDouble(text);
        } catch (NumberFormatException e) {
            return null;
        }
    }
    
    @Override
    public Double getNullValue(DeserializationContext ctxt) {
        return null;
    }
}
