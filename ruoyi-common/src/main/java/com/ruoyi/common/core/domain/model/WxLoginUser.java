package com.ruoyi.common.core.domain.model;

/**
 * 微信小程序登录用户
 * 
 * <AUTHOR>
 */
public class WxLoginUser extends LoginUser
{
    private static final long serialVersionUID = 1L;

    /**
     * 微信用户对象（简化为基本属性，避免循环依赖）
     */
    private Long wxUserId;
    private String nickName;
    private String avatarUrl;
    private String phone;
    private String openid;

    public WxLoginUser()
    {
        super();
    }

    public WxLoginUser(Long wxUserId, String nickName, String openid)
    {
        this.wxUserId = wxUserId;
        this.nickName = nickName;
        this.openid = openid;
        this.setUserId(wxUserId);
    }

    public Long getWxUserId()
    {
        return wxUserId;
    }

    public void setWxUserId(Long wxUserId)
    {
        this.wxUserId = wxUserId;
        this.setUserId(wxUserId);
    }

    public String getNickName()
    {
        return nickName;
    }

    public void setNickName(String nickName)
    {
        this.nickName = nickName;
    }

    public String getAvatarUrl()
    {
        return avatarUrl;
    }

    public void setAvatarUrl(String avatarUrl)
    {
        this.avatarUrl = avatarUrl;
    }

    public String getPhone()
    {
        return phone;
    }

    public void setPhone(String phone)
    {
        this.phone = phone;
    }

    public String getOpenid()
    {
        return openid;
    }

    public void setOpenid(String openid)
    {
        this.openid = openid;
    }

    @Override
    public String getUsername()
    {
        return nickName != null ? nickName : "微信用户";
    }
    
    /**
     * 获取微信用户信息包装器
     * 用于兼容以前的代码
     */
    public WxUserWrapper getWxUser()
    {
        return new WxUserWrapper(wxUserId, nickName, avatarUrl, phone, openid);
    }
    
    /**
     * 微信用户信息包装器，避免循环依赖
     */
    public class WxUserWrapper
    {
        private Long userId;
        private String nickName;
        private String avatarUrl;
        private String phone;
        private String openid;
        
        public WxUserWrapper(Long userId, String nickName, String avatarUrl, String phone, String openid)
        {
            this.userId = userId;
            this.nickName = nickName;
            this.avatarUrl = avatarUrl;
            this.phone = phone;
            this.openid = openid;
        }
        
        public Long getUserId()
        {
            return userId;
        }
        
        public String getNickName()
        {
            return nickName;
        }
        
        public String getAvatarUrl()
        {
            return avatarUrl;
        }
        
        public String getPhone()
        {
            return phone;
        }
        
        public String getOpenid()
        {
            return openid;
        }
    }
} 