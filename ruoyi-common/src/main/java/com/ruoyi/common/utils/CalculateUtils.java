package com.ruoyi.common.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 数值计算工具类
 * 提供精确的数值计算方法，保留指定小数位数
 * 
 * <AUTHOR>
 */
public class CalculateUtils {

    /**
     * 计算百分比，保留两位小数
     * 
     * @param numerator 分子
     * @param denominator 分母
     * @return 百分比字符串，如"85.67%"
     */
    public static String calculatePercentage(Number numerator, Number denominator) {
        return calculatePercentage(numerator, denominator, 2);
    }

    /**
     * 计算百分比，保留指定小数位数
     * 
     * @param numerator 分子
     * @param denominator 分母
     * @param scale 小数位数
     * @return 百分比字符串
     */
    public static String calculatePercentage(Number numerator, Number denominator, int scale) {
        if (numerator == null || denominator == null) {
            return "0." + generateZeros(scale) + "%";
        }

        BigDecimal num = new BigDecimal(numerator.toString());
        BigDecimal den = new BigDecimal(denominator.toString());

        if (den.compareTo(BigDecimal.ZERO) == 0) {
            return "0." + generateZeros(scale) + "%";
        }
        
        BigDecimal percentage = num.divide(den, scale + 2, RoundingMode.HALF_UP)
                                  .multiply(new BigDecimal(100))
                                  .setScale(scale, RoundingMode.HALF_UP);
        
        return percentage + "%";
    }

    /**
     * 计算正确率
     * 
     * @param correctCount 正确数
     * @param totalCount 总数
     * @return 正确率百分比字符串
     */
    public static String calculateAccuracyRate(Integer correctCount, Integer totalCount) {
        int correct = correctCount != null ? correctCount : 0;
        int total = totalCount != null ? totalCount : 0;
        return calculatePercentage(correct, total);
    }

    /**
     * 计算完成率（正确题目数/总题目数）
     * 
     * @param correctCount 正确数
     * @param wrongCount 错误数
     * @param emptyCount 空题数
     * @return 完成率百分比字符串
     */
    public static String calculateFinishRate(Integer correctCount, Integer wrongCount, Integer emptyCount) {
        int correct = correctCount != null ? correctCount : 0;
        int wrong = wrongCount != null ? wrongCount : 0;
        int empty = emptyCount != null ? emptyCount : 0;
        int total = correct + wrong + empty;
        
        return calculatePercentage(correct+wrong, total);
    }

    /**
     * 保留指定小数位数
     * 
     * @param value 数值
     * @param scale 小数位数
     * @return 格式化后的数值
     */
    public static BigDecimal setScale(Number value, int scale) {
        if (value == null) {
            return BigDecimal.ZERO.setScale(scale, RoundingMode.HALF_UP);
        }
        return new BigDecimal(value.toString()).setScale(scale, RoundingMode.HALF_UP);
    }

    /**
     * 保留两位小数
     * 
     * @param value 数值
     * @return 保留两位小数的数值
     */
    public static BigDecimal setScale2(Number value) {
        return setScale(value, 2);
    }

    /**
     * 安全的除法运算，保留指定小数位数
     * 
     * @param dividend 被除数
     * @param divisor 除数
     * @param scale 小数位数
     * @return 计算结果
     */
    public static BigDecimal safeDivide(Number dividend, Number divisor, int scale) {
        if (dividend == null || divisor == null) {
            return BigDecimal.ZERO.setScale(scale, RoundingMode.HALF_UP);
        }
        
        BigDecimal div1 = new BigDecimal(dividend.toString());
        BigDecimal div2 = new BigDecimal(divisor.toString());
        
        if (div2.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO.setScale(scale, RoundingMode.HALF_UP);
        }
        
        return div1.divide(div2, scale, RoundingMode.HALF_UP);
    }

    /**
     * 安全的除法运算，保留两位小数
     * 
     * @param dividend 被除数
     * @param divisor 除数
     * @return 计算结果
     */
    public static BigDecimal safeDivide2(Number dividend, Number divisor) {
        return safeDivide(dividend, divisor, 2);
    }

    /**
     * 生成指定数量的零字符串（兼容Java 8）
     *
     * @param count 零的数量
     * @return 零字符串
     */
    private static String generateZeros(int count) {
        if (count <= 0) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append("0");
        }
        return sb.toString();
    }
}
