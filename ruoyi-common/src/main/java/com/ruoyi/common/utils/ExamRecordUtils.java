package com.ruoyi.common.utils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 考试记录工具类
 * 用于处理题目ID集合的转换
 * 
 * <AUTHOR>
 */
public class ExamRecordUtils {

    /**
     * 将题目ID列表转换为字符串（逗号分隔）
     * 
     * @param questionIds 题目ID列表
     * @return 逗号分隔的字符串
     */
    public static String questionIdsToString(List<Long> questionIds) {
        if (questionIds == null || questionIds.isEmpty()) {
            return null;
        }
        return questionIds.stream()
                .map(String::valueOf)
                .collect(Collectors.joining(","));
    }

    /**
     * 将字符串转换为题目ID列表
     * 
     * @param questionIdsStr 逗号分隔的字符串
     * @return 题目ID列表
     */
    public static List<Long> stringToQuestionIds(String questionIdsStr) {
        if (questionIdsStr == null || questionIdsStr.trim().isEmpty()) {
            return new ArrayList<>();
        }
        return Arrays.stream(questionIdsStr.split(","))
                .filter(s -> !s.trim().isEmpty())
                .map(String::trim)
                .map(Long::valueOf)
                .collect(Collectors.toList());
    }

    /**
     * 添加题目ID到集合字符串中
     * 
     * @param questionIdsStr 原有的题目ID字符串
     * @param questionId 要添加的题目ID
     * @return 更新后的题目ID字符串
     */
    public static String addQuestionId(String questionIdsStr, Long questionId) {
        List<Long> questionIds = stringToQuestionIds(questionIdsStr);
        if (!questionIds.contains(questionId)) {
            questionIds.add(questionId);
        }
        return questionIdsToString(questionIds);
    }

    /**
     * 从集合字符串中移除题目ID
     * 
     * @param questionIdsStr 原有的题目ID字符串
     * @param questionId 要移除的题目ID
     * @return 更新后的题目ID字符串
     */
    public static String removeQuestionId(String questionIdsStr, Long questionId) {
        List<Long> questionIds = stringToQuestionIds(questionIdsStr);
        questionIds.remove(questionId);
        return questionIdsToString(questionIds);
    }

    /**
     * 检查题目ID是否在集合中
     * 
     * @param questionIdsStr 题目ID字符串
     * @param questionId 要检查的题目ID
     * @return 是否包含该题目ID
     */
    public static boolean containsQuestionId(String questionIdsStr, Long questionId) {
        List<Long> questionIds = stringToQuestionIds(questionIdsStr);
        return questionIds.contains(questionId);
    }

    /**
     * 获取题目ID集合的数量
     * 
     * @param questionIdsStr 题目ID字符串
     * @return 题目数量
     */
    public static int getQuestionCount(String questionIdsStr) {
        List<Long> questionIds = stringToQuestionIds(questionIdsStr);
        return questionIds.size();
    }
}
