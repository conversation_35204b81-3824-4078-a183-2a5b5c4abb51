import request from '@/utils/request'

// 查询错题列表
export function listMistake(query) {
  return request({
    url: '/system/mistake/list',
    method: 'get',
    params: query
  })
}

// 查询错题详细
export function getMistake(mistakeId) {
  return request({
    url: '/system/mistake/' + mistakeId,
    method: 'get'
  })
}

// 新增错题
export function addMistake(data) {
  return request({
    url: '/system/mistake',
    method: 'post',
    data: data
  })
}

// 修改错题
export function updateMistake(data) {
  return request({
    url: '/system/mistake',
    method: 'put',
    data: data
  })
}

// 更新错题状态
export function updateMistakeStatus(mistakeId, status) {
  const data = {
    mistakeId,
    status
  }
  return request({
    url: '/system/mistake/changeStatus',
    method: 'put',
    data: data
  })
}

// 删除错题
export function delMistake(mistakeId) {
  return request({
    url: '/system/mistake/' + mistakeId,
    method: 'delete'
  })
}

// 导出错题
export function exportMistake(query) {
  return request({
    url: '/system/mistake/export',
    method: 'get',
    params: query
  })
}

// 查询用户的错题列表
export function listMistakeByUser(userId) {
  return request({
    url: '/system/mistake/user/' + userId,
    method: 'get'
  })
} 