import request from '@/utils/request'

// 获取首页统计数据
export function getDashboardStatistics(params) {
  return request({
    url: '/system/statistics/dashboard',
    method: 'get',
    params
  })
}

// 获取营业额趋势数据
export function getRevenueTrend() {
  return request({
    url: '/system/statistics/revenue/trend',
    method: 'get'
  })
}

// 获取指定时间段营业额统计
export function getCustomRangeRevenue(startDate, endDate) {
  return request({
    url: '/system/statistics/revenue/custom',
    method: 'get',
    params: {
      startDate,
      endDate
    }
  })
}

// 获取首页按渠道统计数据
export function getDashboardChannelStatistics(params) {
  return request({
    url: '/system/statistics/dashboard/channel',
    method: 'get',
    params
  })
}

// 获取指定时间段按渠道营业额统计
export function getCustomRangeChannelRevenue(startDate, endDate) {
  return request({
    url: '/system/statistics/revenue/channel/custom',
    method: 'get',
    params: {
      startDate,
      endDate
    }
  })
}

// 获取按渠道营业额趋势数据
export function getChannelRevenueTrend() {
  return request({
    url: '/system/statistics/revenue/channel/trend',
    method: 'get'
  })
}

// 获取按套餐统计的订单数量
export function getPackageOrderStatistics() {
  return request({
    url: '/system/statistics/package/orders',
    method: 'get'
  })
}
