import request from '@/utils/request'

// 查询考试记录列表
export function listExamRecord(query) {
  return request({
    url: '/system/examRecord/list',
    method: 'get',
    params: query
  })
}

// 查询考试记录详细
export function getExamRecord(recordId) {
  return request({
    url: '/system/examRecord/' + recordId,
    method: 'get'
  })
}

// 新增考试记录
export function addExamRecord(data) {
  return request({
    url: '/system/examRecord',
    method: 'post',
    data: data
  })
}

// 修改考试记录
export function updateExamRecord(data) {
  return request({
    url: '/system/examRecord',
    method: 'put',
    data: data
  })
}

// 删除考试记录
export function delExamRecord(recordId) {
  return request({
    url: '/system/examRecord/' + recordId,
    method: 'delete'
  })
}

// 导出考试记录
export function exportExamRecord(query) {
  return request({
    url: '/system/examRecord/export',
    method: 'get',
    params: query
  })
} 