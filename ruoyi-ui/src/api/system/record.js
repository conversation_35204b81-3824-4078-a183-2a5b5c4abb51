import request from '@/utils/request'

// 查询学习记录列表
export function listRecord(query) {
  return request({
    url: '/system/record/list',
    method: 'get',
    params: query
  })
}

// 查询学习记录详细
export function getRecord(recordId) {
  return request({
    url: '/system/record/' + recordId,
    method: 'get'
  })
}

// 新增学习记录
export function addRecord(data) {
  return request({
    url: '/system/record',
    method: 'post',
    data: data
  })
}

// 修改学习记录
export function updateRecord(data) {
  return request({
    url: '/system/record',
    method: 'put',
    data: data
  })
}

// 删除学习记录
export function delRecord(recordId) {
  return request({
    url: '/system/record/' + recordId,
    method: 'delete'
  })
}

// 导出学习记录
export function exportRecord(query) {
  return request({
    url: '/system/record/export',
    method: 'get',
    params: query
  })
}

// 查询用户学习记录列表
export function listRecordByUser(userId) {
  return request({
    url: '/system/record/listByUser/' + userId,
    method: 'get'
  })
}

// 查询用户在套餐下的学习记录列表
export function listRecordByPackage(userId, packageId) {
  return request({
    url: '/system/record/listByPackage/' + userId + '/' + packageId,
    method: 'get'
  })
}

// 查询用户的特定资料学习记录
export function getUserRecord(userId, materialId) {
  return request({
    url: '/system/record/getRecord/' + userId + '/' + materialId,
    method: 'get'
  })
}

// 更新学习进度
export function updateProgress(data) {
  return request({
    url: '/system/record/updateProgress',
    method: 'post',
    data: data
  })
} 