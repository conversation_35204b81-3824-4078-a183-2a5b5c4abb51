import request from '@/utils/request'

// 查询题库分类列表
export function listCategory(query) {
  return request({
    url: '/system/category/list',
    method: 'get',
    params: query
  })
}

// 查询题库分类树列表
export function listCategoryTree() {
  return request({
    url: '/system/category/tree',
    method: 'get'
  })
}

// 查询题库分类树选择数据
export function getTreeselect() {
  return listCategoryTree()
}

// 查询题库分类详细
export function getCategory(categoryId) {
  return request({
    url: '/system/category/' + categoryId,
    method: 'get'
  })
}

// 新增题库分类
export function addCategory(data) {
  return request({
    url: '/system/category',
    method: 'post',
    data: data
  })
}

// 修改题库分类
export function updateCategory(data) {
  return request({
    url: '/system/category',
    method: 'put',
    data: data
  })
}

// 删除题库分类
export function delCategory(categoryId) {
  return request({
    url: '/system/category/' + categoryId,
    method: 'delete'
  })
}

// 查询题库分类选择框列表
export function optionselect() {
  return request({
    url: '/system/category/optionselect',
    method: 'get'
  })
}



