import request from '@/utils/request'

// 查询题目列表
export function listQuestion(query) {
  return request({
    url: '/system/question/list',
    method: 'get',
    params: query
  })
}

// 查询题目详细
export function getQuestion(questionId) {
  return request({
    url: '/system/question/' + questionId,
    method: 'get'
  })
}

// 新增题目
export function addQuestion(data) {
  return request({
    url: '/system/question',
    method: 'post',
    data: data
  })
}

// 修改题目
export function updateQuestion(data) {
  return request({
    url: '/system/question',
    method: 'put',
    data: data
  })
}

// 删除题目
export function delQuestion(questionId) {
  return request({
    url: '/system/question/' + questionId,
    method: 'delete'
  })
}

// 导出题目
export function exportQuestion(query) {
  return request({
    url: '/system/question/export',
    method: 'post',
    data: query
  })
}

// 根据套餐ID查询题目列表
export function listQuestionByPackage(packageId, questionType) {
  return request({
    url: '/system/question/package/' + packageId + '/' + questionType,
    method: 'get'
  })
}

// 获取题目导入模板
export function importTemplate() {
  return request({
    url: '/system/question/importTemplate',
    method: 'post',
    responseType: 'blob'
  })
}

// 导入题目数据
export function importData(data) {
  return request({
    url: '/system/question/importData',
    method: 'post',
    data: data
  })
} 