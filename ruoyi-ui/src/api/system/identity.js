import request from '@/utils/request'

// 查询系统标识配置列表
export function listIdentity(query) {
  return request({
    url: '/system/config/list',
    method: 'get',
    params: query
  })
}

// 查询系统标识配置详细
export function getIdentity(configId) {
  return request({
    url: '/system/config/' + configId,
    method: 'get'
  })
}

// 根据参数键名查询系统标识配置值
export function getIdentityKey(configKey) {
  return request({
    url: '/system/config/configKey/' + configKey,
    method: 'get'
  })
}

// 新增系统标识配置
export function addIdentity(data) {
  return request({
    url: '/system/config',
    method: 'post',
    data: data
  })
}

// 修改系统标识配置
export function updateIdentity(data) {
  return request({
    url: '/system/config',
    method: 'put',
    data: data
  })
}

// 删除系统标识配置
export function delIdentity(configId) {
  return request({
    url: '/system/config/' + configId,
    method: 'delete'
  })
}

// 刷新系统标识配置缓存
export function refreshIdentityCache() {
  return request({
    url: '/system/config/refreshCache',
    method: 'delete'
  })
} 