import request from '@/utils/request'

// 查询题库套餐列表
export function listPackage(query) {
  return request({
    url: '/system/package/list',
    method: 'get',
    params: query
  })
}

// 查询题库套餐详细
export function getPackage(packageId) {
  return request({
    url: '/system/package/' + packageId,
    method: 'get'
  })
}

// 新增题库套餐
export function addPackage(data) {
  return request({
    url: '/system/package',
    method: 'post',
    data: data
  })
}

// 修改题库套餐
export function updatePackage(data) {
  return request({
    url: '/system/package',
    method: 'put',
    data: data
  })
}

// 删除题库套餐
export function delPackage(packageId) {
  return request({
    url: '/system/package/' + packageId,
    method: 'delete'
  })
}

// 导出题库套餐
export function exportPackage(query) {
  return request({
    url: '/system/package/export',
    method: 'get',
    params: query
  })
}

// 查询题库套餐选择框列表
export function optionselect() {
  return request({
    url: '/system/package/optionselect',
    method: 'get'
  })
} 