import request from '@/utils/request'

// 查询用户套餐列表
export function listUserPackage(query) {
  return request({
    url: '/system/userPackage/list',
    method: 'get',
    params: query
  })
}

// 查询用户套餐详细
export function getUserPackage(userPackageId) {
  return request({
    url: '/system/userPackage/' + userPackageId,
    method: 'get'
  })
}

// 新增用户套餐
export function addUserPackage(data) {
  return request({
    url: '/system/userPackage',
    method: 'post',
    data: data
  })
}

// 修改用户套餐
export function updateUserPackage(data) {
  return request({
    url: '/system/userPackage',
    method: 'put',
    data: data
  })
}

// 删除用户套餐
export function delUserPackage(userPackageId) {
  return request({
    url: '/system/userPackage/' + userPackageId,
    method: 'delete'
  })
}

// 导出用户套餐
export function exportUserPackage(query) {
  return request({
    url: '/system/userPackage/export',
    method: 'get',
    params: query
  })
}

// 更新过期状态
export function updateExpiredStatus() {
  return request({
    url: '/system/userPackage/updateExpiredStatus',
    method: 'post'
  })
}

// 导入用户套餐数据
export function importUserPackage(data) {
  return request({
    url: '/system/userPackage/importData',
    method: 'post',
    data: data
  })
}

// 下载用户套餐导入模板
export function importTemplate() {
  return request({
    url: '/system/userPackage/importTemplate',
    method: 'get'
  })
} 