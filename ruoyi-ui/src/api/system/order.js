import request from '@/utils/request'

// 查询订单列表
export function listOrder(query) {
  return request({
    url: '/system/order/list',
    method: 'get',
    params: query
  })
}

// 查询订单详细
export function getOrder(orderId) {
  return request({
    url: '/system/order/' + orderId,
    method: 'get'
  })
}

// 新增订单
export function addOrder(data) {
  return request({
    url: '/system/order',
    method: 'post',
    data: data
  })
}

// 修改订单
export function updateOrder(data) {
  return request({
    url: '/system/order',
    method: 'put',
    data: data
  })
}

// 删除订单
export function delOrder(orderId) {
  return request({
    url: '/system/order/' + orderId,
    method: 'delete'
  })
}

// 导出订单
export function exportOrder(query) {
  return request({
    url: '/system/order/export',
    method: 'get',
    params: query
  })
}

// 支付订单
export function payOrder(data) {
  return request({
    url: '/system/order/pay',
    method: 'post',
    data: data
  })
}

// 取消订单
export function cancelOrder(data) {
  return request({
    url: '/system/order/cancel',
    method: 'post',
    data: data
  })
}

// 微信小程序创建订单
export function createWxOrder(data) {
  return request({
    url: '/system/wxorder/create',
    method: 'post',
    data: data
  })
}

// 微信小程序查询用户订单列表
export function listWxOrder(userId) {
  return request({
    url: '/system/wxorder/list/' + userId,
    method: 'get'
  })
}

// 微信小程序获取订单详情
export function getWxOrder(orderNo) {
  return request({
    url: '/system/wxorder/' + orderNo,
    method: 'get'
  })
}

// 微信小程序支付订单
export function payWxOrder(data) {
  return request({
    url: '/system/wxorder/pay',
    method: 'post',
    data: data
  })
}

// 微信小程序取消订单
export function cancelWxOrder(data) {
  return request({
    url: '/system/wxorder/cancel',
    method: 'post',
    data: data
  })
} 