import request from '@/utils/request'

// 查询学习资料列表
export function listMaterial(query) {
  return request({
    url: '/system/material/list',
    method: 'get',
    params: query
  })
}

// 查询学习资料详细
export function getMaterial(materialId) {
  return request({
    url: '/system/material/' + materialId,
    method: 'get'
  })
}

// 新增学习资料
export function addMaterial(data) {
  return request({
    url: '/system/material',
    method: 'post',
    data: data
  })
}

// 修改学习资料
export function updateMaterial(data) {
  return request({
    url: '/system/material',
    method: 'put',
    data: data
  })
}

// 删除学习资料
export function delMaterial(materialId) {
  return request({
    url: '/system/material/' + materialId,
    method: 'delete'
  })
}

// 导出学习资料
export function exportMaterial(query) {
  return request({
    url: '/system/material/export',
    method: 'get',
    params: query
  })
}

// 根据套餐ID查询学习资料列表
export function listMaterialByPackage(packageId) {
  return request({
    url: '/system/material/listByPackage/' + packageId,
    method: 'get'
  })
}

// 根据资料类型查询学习资料列表
export function listMaterialByType(materialType) {
  return request({
    url: '/system/material/listByType/' + materialType,
    method: 'get'
  })
}

// 查看资料（增加浏览次数）
export function viewMaterial(materialId) {
  return request({
    url: '/system/material/view/' + materialId,
    method: 'post'
  })
}

// 下载资料（增加下载次数）
export function downloadMaterial(materialId) {
  return request({
    url: '/system/material/download/' + materialId,
    method: 'post'
  })
} 