import request from '@/utils/request'

// 查询微信用户列表
export function listWxUser(query) {
  return request({
    url: '/system/wxuser/list',
    method: 'get',
    params: query
  })
}

// 查询微信用户详细
export function getWxUser(userId) {
  return request({
    url: '/system/wxuser/' + userId,
    method: 'get'
  })
}

// 新增微信用户
export function addWxUser(data) {
  return request({
    url: '/system/wxuser',
    method: 'post',
    data: data
  })
}

// 修改微信用户
export function updateWxUser(data) {
  return request({
    url: '/system/wxuser',
    method: 'put',
    data: data
  })
}

// 删除微信用户
export function delWxUser(userId) {
  return request({
    url: '/system/wxuser/' + userId,
    method: 'delete'
  })
}

// 导出微信用户
export function exportWxUser(query) {
  return request({
    url: '/system/wxuser/export',
    method: 'get',
    params: query
  })
}

// 微信用户状态修改
export function changeWxUserStatus(userId, status) {
  const data = {
    userId,
    status
  }
  return request({
    url: '/system/wxuser/changeStatus',
    method: 'put',
    data: data
  })
}
