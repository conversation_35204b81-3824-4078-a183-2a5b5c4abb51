<template>
  <div class="app-container">
    <el-card>
      <div slot="header" class="clearfix">
        <span>订单详情</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="goBack">返回</el-button>
      </div>
      
      <el-descriptions title="订单基本信息" :column="2" border>
        <el-descriptions-item label="订单ID">{{ order.orderId }}</el-descriptions-item>
        <el-descriptions-item label="订单编号">{{ order.orderNo }}</el-descriptions-item>
        <el-descriptions-item label="订单金额">{{ order.amount }} 元</el-descriptions-item>
        <el-descriptions-item label="订单状态">
          <el-tag :type="statusTypeMap[order.status]">{{ statusFormat(order) }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ parseTime(order.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="支付时间">{{ parseTime(order.payTime) || '未支付' }}</el-descriptions-item>
        <el-descriptions-item label="过期时间">{{ parseTime(order.expireTime) || '未设置' }}</el-descriptions-item>
        <el-descriptions-item label="支付渠道">{{ payChannelFormat(order) }}</el-descriptions-item>
        <el-descriptions-item label="交易流水号" :span="2">{{ order.transactionId || '无' }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ order.remark || '无' }}</el-descriptions-item>
      </el-descriptions>
      
      <el-divider></el-divider>
      
      <el-descriptions title="用户信息" :column="2" border>
        <el-descriptions-item label="用户ID">{{ order.userId }}</el-descriptions-item>
        <el-descriptions-item label="用户昵称">{{ order.user ? order.user.nickName : '未知' }}</el-descriptions-item>
        <el-descriptions-item label="手机号码">{{ order.user ? order.user.phone : '未知' }}</el-descriptions-item>
        <el-descriptions-item label="用户头像">
          <el-image
            v-if="order.user && order.user.avatarUrl"
            style="width: 50px; height: 50px"
            :src="order.user.avatarUrl"
            fit="cover"
          ></el-image>
          <span v-else>无头像</span>
        </el-descriptions-item>
      </el-descriptions>
      
      <el-divider></el-divider>
      
      <el-descriptions title="套餐信息" :column="2" border>
        <el-descriptions-item label="套餐ID">{{ order.packageId }}</el-descriptions-item>
        <el-descriptions-item label="套餐名称">{{ order.packageName }}</el-descriptions-item>
        <el-descriptions-item label="套餐价格" v-if="order.examPackage">{{ order.examPackage.price }} 元</el-descriptions-item>
        <el-descriptions-item label="有效期天数" v-if="order.examPackage">{{ order.examPackage.validityDays }} 天</el-descriptions-item>
        <el-descriptions-item label="套餐介绍" :span="2" v-if="order.examPackage">{{ order.examPackage.introduction || '无介绍' }}</el-descriptions-item>
      </el-descriptions>
      
      <div class="order-actions" style="margin-top: 20px; text-align: center;">
        <el-button type="primary" @click="goBack">返回</el-button>
        <el-button 
          v-if="order.status === '0'" 
          type="success" 
          @click="handlePay"
        >支付订单</el-button>
        <el-button 
          v-if="order.status === '0'" 
          type="info" 
          @click="handleCancel"
        >取消订单</el-button>
      </div>
    </el-card>
    
    <!-- 支付订单对话框 -->
    <el-dialog title="订单支付" :visible.sync="payOpen" width="500px" append-to-body>
      <el-form ref="payForm" :model="payForm" :rules="payRules" label-width="100px">
        <el-form-item label="订单编号">
          <span>{{ payForm.orderNo }}</span>
        </el-form-item>
        <el-form-item label="订单金额">
          <span>{{ payForm.amount }} 元</span>
        </el-form-item>
        <el-form-item label="支付渠道" prop="payChannel">
          <el-select v-model="payForm.payChannel" placeholder="请选择支付渠道">
            <el-option label="微信支付" value="wechat" />
            <el-option label="支付宝" value="alipay" />
          </el-select>
        </el-form-item>
        <el-form-item label="交易流水号" prop="transactionId">
          <el-input v-model="payForm.transactionId" placeholder="请输入交易流水号" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitPayForm">确 定</el-button>
        <el-button @click="payCancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getOrder, payOrder, cancelOrder } from "@/api/system/order";

export default {
  name: "OrderDetail",
  dicts: ['sys_order_status', 'sys_pay_channel'],
  data() {
    return {
      // 订单ID
      orderId: null,
      // 订单数据
      order: {},
      // 是否显示支付弹出层
      payOpen: false,
      // 支付表单
      payForm: {},
      // 支付表单校验
      payRules: {
        payChannel: [
          { required: true, message: "支付渠道不能为空", trigger: "change" }
        ],
        transactionId: [
          { required: true, message: "交易流水号不能为空", trigger: "blur" }
        ]
      },
      // 订单状态颜色映射
      statusTypeMap: {
        "0": "warning",  // 待支付
        "1": "success",  // 已支付
        "2": "info",     // 已取消
        "3": "danger"    // 已过期
      }
    };
  },
  created() {
    this.orderId = this.$route.query.orderId;
    this.getInfo();
  },
  methods: {
    /** 获取订单详情 */
    getInfo() {
      getOrder(this.orderId).then(response => {
        this.order = response.data;
      });
    },
    // 订单状态字典翻译
    statusFormat(row) {
      return this.selectDictLabel(this.dict.type.sys_order_status, row.status);
    },
    // 支付渠道字典翻译
    payChannelFormat(row) {
      if (row.payChannel) {
        return this.selectDictLabel(this.dict.type.sys_pay_channel, row.payChannel);
      }
      return "-";
    },
    // 返回按钮
    goBack() {
      this.$router.push({ path: '/system/order' });
    },
    // 支付表单重置
    resetPayForm() {
      this.payForm = {
        orderNo: null,
        amount: 0,
        payChannel: null,
        transactionId: null
      };
      this.resetForm("payForm");
    },
    // 取消支付按钮
    payCancel() {
      this.payOpen = false;
      this.resetPayForm();
    },
    /** 支付按钮操作 */
    handlePay() {
      this.resetPayForm();
      this.payForm = {
        orderNo: this.order.orderNo,
        amount: this.order.amount,
        payChannel: null,
        transactionId: null
      };
      this.payOpen = true;
    },
    /** 取消订单操作 */
    handleCancel() {
      this.$modal.confirm('是否确认取消该订单?').then(function() {
        return cancelOrder({ orderNo: this.order.orderNo });
      }).then(() => {
        this.getInfo();
        this.$modal.msgSuccess("取消成功");
      }).catch(() => {});
    },
    /** 提交支付表单 */
    submitPayForm() {
      this.$refs["payForm"].validate(valid => {
        if (valid) {
          payOrder(this.payForm).then(response => {
            this.$modal.msgSuccess("支付成功");
            this.payOpen = false;
            this.getInfo();
          });
        }
      });
    }
  }
};
</script>

<style scoped>
.order-actions {
  margin-top: 20px;
}
</style> 