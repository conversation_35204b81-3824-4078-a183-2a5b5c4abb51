<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="订单号" prop="orderNo">
        <el-input
          v-model="queryParams.orderNo"
          placeholder="请输入订单编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户ID" prop="userId">
        <el-input
          v-model="queryParams.userId"
          placeholder="请输入用户ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户昵称" prop="nickName">
        <el-input
          v-model="queryParams.nickName"
          placeholder="请输入用户昵称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户手机号" prop="phone">
        <el-input
          v-model="queryParams.phone"
          placeholder="请输入用户手机号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="套餐ID" prop="packageId">
        <el-input
          v-model="queryParams.packageId"
          placeholder="请输入套餐ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="订单状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="订单状态" clearable>
          <el-option
            v-for="dict in dict.type.sys_order_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="支付渠道" prop="payChannel">
        <el-select v-model="queryParams.payChannel" placeholder="支付渠道" clearable>
          <el-option
            v-for="dict in dict.type.sys_pay_channel"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:order:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:order:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:order:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:order:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="orderList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="订单ID" align="center" prop="orderId" />
      <el-table-column label="订单编号" align="center" prop="orderNo" width="180" :show-overflow-tooltip="true" />
      <el-table-column label="用户ID" align="center" prop="userId" />
      <el-table-column label="用户昵称" align="center" prop="user.nickName" width="120" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          {{ scope.row.user ? scope.row.user.nickName : '-' }}
        </template>
      </el-table-column>
      <el-table-column label="用户手机号" align="center" prop="user.phone" width="120">
        <template slot-scope="scope">
          {{ scope.row.user ? scope.row.user.phone : '-' }}
        </template>
      </el-table-column>
      <el-table-column label="套餐名称" align="center" prop="packageName" :show-overflow-tooltip="true" />
      <el-table-column label="订单金额" align="center" prop="amount">
        <template slot-scope="scope">
          {{ scope.row.amount }} 元
        </template>
      </el-table-column>
      <el-table-column label="订单状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-tag :type="statusTypeMap[scope.row.status]">
            {{ statusFormat(scope.row) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="支付时间" align="center" prop="payTime" width="160">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.payTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="过期时间" align="center" prop="expireTime" width="160">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.expireTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="支付渠道" align="center" prop="payChannel">
        <template slot-scope="scope">
          {{ payChannelFormat(scope.row) }}
        </template>
      </el-table-column>
      <el-table-column label="交易流水号" align="center" prop="transactionId" width="180" :show-overflow-tooltip="true" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['system:order:query']"
          >查看</el-button>
          <el-button
            v-if="scope.row.status == 0"
            size="mini"
            type="text"
            icon="el-icon-check"
            @click="handlePay(scope.row)"
            v-hasPermi="['system:order:edit']"
          >支付</el-button>
          <el-button
            v-if="scope.row.status == 0"
            size="mini"
            type="text"
            icon="el-icon-close"
            @click="handleCancel(scope.row)"
            v-hasPermi="['system:order:edit']"
          >取消</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:order:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改订单对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="用户ID" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入用户ID" />
        </el-form-item>
        <el-form-item label="套餐ID" prop="packageId">
          <el-input v-model="form.packageId" placeholder="请输入套餐ID" />
        </el-form-item>
        <el-form-item label="套餐名称" prop="packageName">
          <el-input v-model="form.packageName" placeholder="请输入套餐名称" />
        </el-form-item>
        <el-form-item label="订单金额" prop="amount">
          <el-input-number v-model="form.amount" :precision="2" :step="0.1" controls-position="right" placeholder="请输入订单金额" />
        </el-form-item>
        <el-form-item label="订单状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择订单状态">
            <el-option
              v-for="dict in dict.type.sys_order_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 支付订单对话框 -->
    <el-dialog title="订单支付" :visible.sync="payOpen" width="500px" append-to-body>
      <el-form ref="payForm" :model="payForm" :rules="payRules" label-width="100px">
        <el-form-item label="订单编号">
          <span>{{ payForm.orderNo }}</span>
        </el-form-item>
        <el-form-item label="订单金额">
          <span>{{ payForm.amount }} 元</span>
        </el-form-item>
        <el-form-item label="支付渠道" prop="payChannel">
          <el-select v-model="payForm.payChannel" placeholder="请选择支付渠道">
            <el-option label="微信支付" value="wechat" />
            <el-option label="支付宝" value="alipay" />
          </el-select>
        </el-form-item>
        <el-form-item label="交易流水号" prop="transactionId" v-if="payForm.payChannel !== 'wechat'">
          <el-input v-model="payForm.transactionId" placeholder="请输入交易流水号" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitPayForm">确 定</el-button>
        <el-button @click="payCancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 微信支付对话框 -->
    <el-dialog title="微信支付" :visible.sync="wxPayOpen" width="400px" append-to-body :close-on-click-modal="false" :close-on-press-escape="false">
      <div class="wx-pay-container" style="text-align: center;">
        <div v-if="wxPayParams">
          <p>请扫描二维码完成支付</p>
          <p>订单编号：{{ payForm.orderNo }}</p>
          <p>金额：{{ payForm.amount }} 元</p>
          <div class="qrcode-box" style="margin: 20px auto; width: 200px; height: 200px; background-color: #f5f5f5; display: flex; justify-content: center; align-items: center;">
            <p>微信小程序支付</p>
            <!-- 实际项目中可以使用qrcode.js等生成二维码 -->
          </div>
          <p style="color: #999; font-size: 12px;">支付完成后会自动跳转</p>
        </div>
        <div v-else>
          <p>正在创建支付订单，请稍候...</p>
          <el-progress type="circle" :percentage="50" status="warning"></el-progress>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="wxPayCancel">取消支付</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listOrder, getOrder, addOrder, updateOrder, delOrder, exportOrder, payOrder, cancelOrder } from "@/api/system/order";
import { createWxPayOrder, queryWxPayOrder } from "@/api/system/wxpay";

export default {
  name: "Order",
  dicts: ['sys_order_status', 'sys_pay_channel'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 订单表格数据
      orderList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示支付弹出层
      payOpen: false,
      // 是否显示微信支付弹出层
      wxPayOpen: false,
      // 微信支付轮询定时器
      wxPayTimer: null,
      // 微信支付参数
      wxPayParams: null,
      // 日期范围
      dateRange: [],
      // 订单状态颜色映射
      statusTypeMap: {
        "0": "warning",  // 待支付
        "1": "success",  // 已支付
        "2": "info",     // 已取消
        "3": "danger"    // 已过期
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        orderNo: null,
        userId: null,
        nickName: null,
        phone: null,
        packageId: null,
        status: null,
        payChannel: null
      },
      // 表单参数
      form: {},
      // 支付表单
      payForm: {},
      // 表单校验
      rules: {
        userId: [
          { required: true, message: "用户ID不能为空", trigger: "blur" }
        ],
        packageId: [
          { required: true, message: "套餐ID不能为空", trigger: "blur" }
        ],
        packageName: [
          { required: true, message: "套餐名称不能为空", trigger: "blur" }
        ],
        amount: [
          { required: true, message: "订单金额不能为空", trigger: "blur" }
        ],
        status: [
          { required: true, message: "订单状态不能为空", trigger: "change" }
        ]
      },
      // 支付表单校验
      payRules: {
        payChannel: [
          { required: true, message: "支付渠道不能为空", trigger: "change" }
        ],
        transactionId: [
          { required: true, message: "交易流水号不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  beforeDestroy() {
    // 组件销毁前清除定时器
    if (this.wxPayTimer) {
      clearInterval(this.wxPayTimer);
    }
  },
  methods: {
    /** 查询订单列表 */
    getList() {
      this.loading = true;
      listOrder(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.orderList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 订单状态字典翻译
    statusFormat(row) {
      return this.selectDictLabel(this.dict.type.sys_order_status, row.status);
    },
    // 支付渠道字典翻译
    payChannelFormat(row) {
      if (row.payChannel) {
        return this.selectDictLabel(this.dict.type.sys_pay_channel, row.payChannel);
      }
      return "-";
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 取消支付按钮
    payCancel() {
      this.payOpen = false;
      this.resetPayForm();
    },
    // 取消微信支付按钮
    wxPayCancel() {
      this.wxPayOpen = false;
      // 清除轮询定时器
      if (this.wxPayTimer) {
        clearInterval(this.wxPayTimer);
      }
    },
    // 表单重置
    reset() {
      this.form = {
        orderId: null,
        orderNo: null,
        userId: null,
        packageId: null,
        packageName: null,
        amount: 0,
        status: "0",
        payTime: null,
        expireTime: null,
        payChannel: null,
        transactionId: null,
        remark: null
      };
      this.resetForm("form");
    },
    // 支付表单重置
    resetPayForm() {
      this.payForm = {
        orderNo: null,
        amount: 0,
        payChannel: null,
        transactionId: null
      };
      this.resetForm("payForm");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.orderId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加订单";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const orderId = row.orderId || this.ids[0]
      getOrder(orderId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改订单";
      });
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.$router.push({ path: '/system/order/detail', query: { orderId: row.orderId } })
    },
    /** 支付按钮操作 */
    handlePay(row) {
      this.resetPayForm();
      this.payForm = {
        orderNo: row.orderNo,
        amount: row.amount,
        payChannel: null,
        transactionId: null
      };
      this.payOpen = true;
    },
    /** 取消订单操作 */
    handleCancel(row) {
      this.$modal.confirm('是否确认取消订单编号为"' + row.orderNo + '"的订单?').then(function() {
        return cancelOrder({ orderNo: row.orderNo });
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("取消成功");
      }).catch(() => {});
    },
    /** 提交支付表单 */
    submitPayForm() {
      this.$refs["payForm"].validate(valid => {
        if (valid) {
          // 如果选择的是微信支付
          if (this.payForm.payChannel === "wechat") {
            this.handleWxPay();
          } else {
            // 其他支付方式（支付宝等）
            payOrder(this.payForm).then(response => {
              this.$modal.msgSuccess("支付成功");
              this.payOpen = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.orderId != null) {
            updateOrder(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addOrder(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const orderIds = row.orderId || this.ids;
      this.$modal.confirm('是否确认删除订单编号为"' + orderIds + '"的数据项?').then(function() {
        return delOrder(orderIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 处理微信支付 */
    handleWxPay() {
      // 关闭手动支付弹窗
      this.payOpen = false;

      // 打开微信支付弹窗
      this.wxPayOpen = true;

      // 模拟OpenID（实际使用时应从登录用户信息获取）
      const openid = "test_openid_" + new Date().getTime();

      // 创建微信支付订单
      createWxPayOrder({
        orderNo: this.payForm.orderNo,
        openid: openid
      }).then(response => {
        if (response.code === 200) {
          // 存储微信支付参数
          this.wxPayParams = response.data;

          // 启动轮询查询支付状态
          this.pollWxPayStatus();
        } else {
          this.$modal.msgError(response.msg || "创建微信支付订单失败");
          this.wxPayOpen = false;
        }
      });
    },

    /** 轮询查询微信支付状态 */
    pollWxPayStatus() {
      // 清除可能存在的旧定时器
      if (this.wxPayTimer) {
        clearInterval(this.wxPayTimer);
      }

      // 设置轮询间隔（3秒查询一次）
      this.wxPayTimer = setInterval(() => {
        queryWxPayOrder(this.payForm.orderNo).then(response => {
          if (response.code === 200) {
            const status = response.data;

            // 如果支付成功
            if (status === 'SUCCESS') {
              // 清除定时器
              clearInterval(this.wxPayTimer);
              this.wxPayTimer = null;

              // 关闭微信支付弹窗
              this.wxPayOpen = false;

              // 显示支付成功提示
              this.$modal.msgSuccess("支付成功");

              // 刷新订单列表
              this.getList();
            }
          }
        });
      }, 3000);
    },

    /** 导出按钮操作 */
    handleExport() {
      this.download('system/order/export', {
        ...this.queryParams
      }, `order_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
