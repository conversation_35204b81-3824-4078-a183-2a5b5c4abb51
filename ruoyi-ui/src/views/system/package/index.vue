<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="套餐名称" prop="packageName">
        <el-input
          v-model="queryParams.packageName"
          placeholder="请输入套餐名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="分类" prop="categoryId">
        <treeselect
          v-model="queryParams.categoryId"
          :options="categoryOptions"
          :normalizer="normalizer"
          placeholder="请选择分类"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="套餐状态" clearable>
          <el-option
            v-for="dict in dict.type.sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:package:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:package:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:package:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:package:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="packageList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="套餐ID" align="center" prop="packageId" />
      <el-table-column label="套餐名称" align="center" prop="packageName" :show-overflow-tooltip="true" />
      <el-table-column label="套餐分类" align="center" prop="categoryName" :show-overflow-tooltip="true" />
      <el-table-column label="封面图片" align="center" prop="materialType">
        <template slot-scope="scope">
          <el-image :src="scope.row.coverImage"/>
        </template>
      </el-table-column>
      <el-table-column label="价格(元)" align="center" prop="price" />
      <el-table-column label="有效期天数" align="center" prop="validityDays" />
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:package:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:package:remove']"
          >删除</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
          >查看题目</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改套餐对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="套餐分类" prop="categoryId">
          <treeselect
            v-model="form.categoryId"
            :options="categoryOptions"
            :normalizer="normalizer"
            :show-count="true"
            placeholder="选择套餐分类"
            noOptionsText="暂无数据"
            noResultsText="暂无数据"
          />
        </el-form-item>
        <el-form-item label="套餐名称" prop="packageName">
          <el-input v-model="form.packageName" placeholder="请输入套餐名称" />
        </el-form-item>
        <el-form-item label="套餐描述" prop="introduction">
          <el-input v-model="form.introduction" type="textarea" placeholder="请输入套餐描述" />
        </el-form-item>

        <el-form-item label="套餐封面" prop="coverImage">
          <ImageUpload
            v-model="form.coverImage"
            action="/common/uploadFile"
            :fileSize="5"
            :limit="1"
          />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="套餐价格" prop="price">
              <el-input-number v-model="form.price" :min="0" :precision="2" :step="0.1" controls-position="right" placeholder="请输入套餐价格" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="有效期天数" prop="validityDays">
              <el-input-number v-model="form.validityDays" :min="1" controls-position="right" placeholder="请输入有效期天数" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="套餐状态">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in dict.type.sys_normal_disable"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-row>
<!--          <el-col :span="12">-->
<!--            <el-form-item label="模拟考试题数" prop="simulateCount">-->
<!--              <el-input-number v-model="form.simulateCount" :min="0" controls-position="right" placeholder="请输入模拟考试题数" />-->
<!--            </el-form-item>-->
<!--          </el-col>-->
<!--          <el-col :span="12">-->
<!--            <el-form-item label="练习题目数量" prop="practiceCount">-->
<!--              <el-input-number v-model="form.practiceCount" :min="0" controls-position="right" placeholder="请输入练习题目数量" />-->
<!--            </el-form-item>-->
<!--          </el-col>-->
        </el-row>
        <el-form-item label="备注">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 查看套餐题目对话框 -->
    <el-dialog title="套餐题目" :visible.sync="viewOpen" width="800px" append-to-body>
      <el-tabs v-model="activeTab">
        <el-tab-pane label="模拟考试题" name="simulate">
          <el-table v-loading="viewLoading" :data="simulateQuestions">
            <el-table-column label="题目ID" align="center" prop="questionId" width="80" />
            <el-table-column label="题目内容" align="center" prop="content" :show-overflow-tooltip="true" />
            <el-table-column label="题目类型" align="center" prop="questionType" width="100">
              <template slot-scope="scope">
                <span v-if="scope.row.questionType === '1'">单选题</span>
                <span v-else-if="scope.row.questionType === '2'">多选题</span>
                <span v-else-if="scope.row.questionType === '3'">判断题</span>
                <span v-else>未知类型</span>
              </template>
            </el-table-column>
            <el-table-column label="难度" align="center" prop="difficulty" width="80">
              <template slot-scope="scope">
                <span v-if="scope.row.difficulty === '1'">简单</span>
                <span v-else-if="scope.row.difficulty === '2'">中等</span>
                <span v-else-if="scope.row.difficulty === '3'">困难</span>
                <span v-else>未知</span>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="simulateTotal>0"
            :total="simulateTotal"
            :page.sync="simulateQuery.pageNum"
            :limit.sync="simulateQuery.pageSize"
            @pagination="getSimulateQuestions"
          />
        </el-tab-pane>
        <el-tab-pane label="练习题" name="practice">
          <el-table v-loading="viewLoading" :data="practiceQuestions">
            <el-table-column label="题目ID" align="center" prop="questionId" width="80" />
            <el-table-column label="题目内容" align="center" prop="content" :show-overflow-tooltip="true" />
            <el-table-column label="题目类型" align="center" prop="questionType" width="100">
              <template slot-scope="scope">
                <span v-if="scope.row.questionType === '1'">单选题</span>
                <span v-else-if="scope.row.questionType === '2'">多选题</span>
                <span v-else-if="scope.row.questionType === '3'">判断题</span>
                <span v-else>未知类型</span>
              </template>
            </el-table-column>
            <el-table-column label="难度" align="center" prop="difficulty" width="80">
              <template slot-scope="scope">
                <span v-if="scope.row.difficulty === '1'">简单</span>
                <span v-else-if="scope.row.difficulty === '2'">中等</span>
                <span v-else-if="scope.row.difficulty === '3'">困难</span>
                <span v-else>未知</span>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="practiceTotal>0"
            :total="practiceTotal"
            :page.sync="practiceQuery.pageNum"
            :limit.sync="practiceQuery.pageSize"
            @pagination="getPracticeQuestions"
          />
        </el-tab-pane>
      </el-tabs>
    </el-dialog>
  </div>
</template>

<script>
import { listPackage, getPackage, delPackage, addPackage, updatePackage, exportPackage } from "@/api/system/package";
import { listCategory } from "@/api/system/category";
import { listQuestionByPackage } from "@/api/system/question";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import ImageUpload from "@/components/ImageUpload";

export default {
  name: "Package",
  dicts: ['sys_normal_disable'],
  components: { Treeselect, ImageUpload },
  data() {
    return {
      // 遮罩层
      loading: true,
      viewLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 题库套餐表格数据
      packageList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示查看题目弹出层
      viewOpen: false,
      // 当前查看的套餐ID
      currentPackageId: null,
      // 查看题目标签页
      activeTab: 'simulate',
      // 模拟考试题列表
      simulateQuestions: [],
      // 模拟考试题总数
      simulateTotal: 0,
      // 模拟考试题查询参数
      simulateQuery: {
        pageNum: 1,
        pageSize: 10
      },
      // 练习题列表
      practiceQuestions: [],
      // 练习题总数
      practiceTotal: 0,
      // 练习题查询参数
      practiceQuery: {
        pageNum: 1,
        pageSize: 10
      },
      // 分类树选项
      categoryOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        packageName: undefined,
        categoryId: undefined,
        status: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        packageName: [
          { required: true, message: "套餐名称不能为空", trigger: "blur" }
        ],
        categoryId: [
          { required: true, message: "套餐分类不能为空", trigger: "change" }
        ],
        price: [
          { required: true, message: "套餐价格不能为空", trigger: "blur" }
        ],
        validityDays: [
          { required: true, message: "有效期天数不能为空", trigger: "blur" }
        ],
        // simulateCount: [
        //   { required: true, message: "模拟考试题数不能为空", trigger: "blur" }
        // ],
        // practiceCount: [
        //   { required: true, message: "练习题目数量不能为空", trigger: "blur" }
        // ]
      }
    };
  },
  created() {
    this.getList();
    this.getTreeselect();
  },
  methods: {
    /** 查询套餐列表 */
    getList() {
      this.loading = true;
      listPackage(this.queryParams).then(response => {
        this.packageList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 转换分类数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.categoryId,
        label: node.categoryName,
        children: node.children
      };
    },
    /** 查询分类下拉树结构 */
    getTreeselect() {
      listCategory().then(response => {
        this.categoryOptions = [];
        const data = { categoryId: 0, categoryName: '主类目', children: [] };
        data.children = this.handleTree(response.rows, "categoryId", "parentId");
        this.categoryOptions.push(data);
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        packageId: undefined,
        categoryId: undefined,
        packageName: undefined,
        introduction: undefined,
        coverImage: undefined,
        price: 0,
        validityDays: 30,
        simulateCount: 0,
        practiceCount: 0,
        status: "0",
        remark: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 选择条目触发事件 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.packageId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加题库套餐";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const packageId = row.packageId || this.ids[0]
      getPackage(packageId).then(response => {
        // 打印响应结构，了解数据结构
        console.log('Package API response:', response);

        // 从正确的响应属性中获取数据（根据后端API设计可能是data或rows）
        const packageData = response.data;

        // 确保数据完整性
        if (packageData) {
          // 设置默认值，避免未定义错误
          this.form = {
            ...this.form,  // 保留默认值
            ...packageData, // 覆盖API返回值
            categoryId: packageData.categoryId || undefined // 确保categoryId有值
          };
          this.open = true;
          this.title = "修改题库套餐";
        } else {
          this.$modal.msgError('获取套餐数据失败');
        }
      });
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.packageId != undefined) {
            updatePackage(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPackage(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const packageIds = row.packageId || this.ids;
      this.$modal.confirm('是否确认删除套餐编号为"' + packageIds + '"的数据项?').then(function() {
        return delPackage(packageIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 查看题目按钮操作 */
    handleView(row) {
      this.currentPackageId = row.packageId;
      this.viewOpen = true;
      this.activeTab = 'simulate';
      this.simulateQuery = {
        pageNum: 1,
        pageSize: 10
      };
      this.practiceQuery = {
        pageNum: 1,
        pageSize: 10
      };
      this.getSimulateQuestions();
    },
    /** 获取模拟考试题目列表 */
    getSimulateQuestions() {
      this.viewLoading = true;
      listQuestionByPackage(this.currentPackageId, '1').then(response => {
        this.simulateQuestions = response.rows;
        this.simulateTotal = response.total;
        this.viewLoading = false;
      });
    },
    /** 获取练习题目列表 */
    getPracticeQuestions() {
      this.viewLoading = true;
      listQuestionByPackage(this.currentPackageId, '2').then(response => {
        this.practiceQuestions = response.rows;
        this.practiceTotal = response.total;
        this.viewLoading = false;
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/package/export', {
        ...this.queryParams
      }, `套餐数据_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
