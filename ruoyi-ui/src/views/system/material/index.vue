<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="资料名称" prop="materialName">
        <el-input
          v-model="queryParams.materialName"
          placeholder="请输入资料名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="资料类型" prop="materialType">
        <el-select v-model="queryParams.materialType" placeholder="资料类型" clearable>
          <el-option label="电子书" value="0" />
          <el-option label="视频" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item label="所属套餐" prop="packageId">
        <el-select v-model="queryParams.packageId" placeholder="请选择套餐" clearable>
          <el-option
            v-for="item in packageOptions"
            :key="item.packageId"
            :label="item.packageName"
            :value="item.packageId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="资源主题" prop="theme">
        <el-input
          v-model="queryParams.theme"
          placeholder="请输入资源主题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="状态" clearable>
          <el-option
            v-for="dict in dict.type.sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:material:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:material:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:material:remove']"
        >删除</el-button>
      </el-col>
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="warning"-->
<!--          plain-->
<!--          icon="el-icon-download"-->
<!--          size="mini"-->
<!--          @click="handleExport"-->
<!--          v-hasPermi="['system:material:export']"-->
<!--        >导出</el-button>-->
<!--      </el-col>-->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="materialList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="资料ID" align="center" prop="materialId" />
      <el-table-column sortable label="排序号" align="center" prop="sortNum" width="80" />
      <el-table-column label="资料名称" align="center" prop="materialName" :show-overflow-tooltip="true" />
      <el-table-column label="资料类型" align="center" prop="materialType">
        <template slot-scope="scope">
          <el-tag :type="scope.row.materialType === '0' ? 'primary' : 'success'">
            {{ scope.row.materialType === '0' ? '电子书' : '视频' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="资源主题" align="center" prop="theme" :show-overflow-tooltip="true" />
      <el-table-column label="所属套餐" align="center" prop="packageName" :show-overflow-tooltip="true" />
      <el-table-column label="文件大小" align="center" prop="fileSize">
        <template slot-scope="scope">
          {{ formatFileSize(scope.row.fileSize) }}
        </template>
      </el-table-column>
<!--      <el-table-column label="浏览次数" align="center" prop="viewCount" />-->
<!--      <el-table-column label="下载次数" align="center" prop="downloadCount" />-->
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['system:material:query']"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:material:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:material:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改学习资料对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="资料名称" prop="materialName">
          <el-input v-model="form.materialName" placeholder="请输入资料名称" />
        </el-form-item>
        <el-form-item label="资料类型" prop="materialType">
          <el-radio-group v-model="form.materialType" @change="handleMaterialTypeChange">
            <el-radio label="0">电子书</el-radio>
            <el-radio label="1">视频</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="资源链接" prop="resourceUrl">
          <FileUpload
            v-model="form.resourceUrl"
            :fileType="form.materialType === '0' ? ['pdf', 'epub', 'txt', 'doc', 'docx'] : ['mp4', 'avi', 'mov', 'flv']"
            :fileSize="150"
            action="/common/uploadFile"
          />
        </el-form-item>
        <el-form-item label="封面图片" prop="coverImg">
          <ImageUpload
            v-model="form.coverImg"
            action="/common/uploadFile"
            :fileSize="5"
            :limit="1"
          />
        </el-form-item>
        <el-form-item label="资源主题" prop="theme">
          <el-input v-model="form.theme" placeholder="请输入资源主题" />
        </el-form-item>
        <el-form-item label="所属套餐" prop="packageId">
          <el-select v-model="form.packageId" placeholder="请选择所属套餐" @change="handlePackageChange">
            <el-option
              v-for="item in packageOptions"
              :key="item.packageId"
              :label="item.packageName"
              :value="item.packageId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="文件大小(KB)" prop="fileSize">
          <el-input-number v-model="form.fileSize" controls-position="right" :min="0" placeholder="请输入文件大小" />
        </el-form-item>
        <el-form-item label="排序号" prop="sortNum">
          <el-input-number v-model="form.sortNum" controls-position="right" :min="0" placeholder="请输入排序号" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in dict.type.sys_normal_disable"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="资料介绍" prop="materialDesc">
          <el-input v-model="form.materialDesc" type="textarea" :rows="5" placeholder="请输入资料介绍内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 资料查看对话框 -->
    <el-dialog title="资料详情" :visible.sync="viewOpen" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="资料名称">{{ viewForm.materialName }}</el-descriptions-item>
        <el-descriptions-item label="资料类型">
          <el-tag :type="viewForm.materialType === '0' ? 'primary' : 'success'">
            {{ viewForm.materialType === '0' ? '电子书' : '视频' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="资源主题">{{ viewForm.theme }}</el-descriptions-item>
        <el-descriptions-item label="所属套餐">{{ viewForm.packageName }}</el-descriptions-item>
        <el-descriptions-item label="文件大小">{{ formatFileSize(viewForm.fileSize) }}</el-descriptions-item>
        <el-descriptions-item label="排序号">{{ viewForm.sortNum }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <dict-tag :options="dict.type.sys_normal_disable" :value="viewForm.status"/>
        </el-descriptions-item>
        <el-descriptions-item label="浏览次数">{{ viewForm.viewCount }}</el-descriptions-item>
        <el-descriptions-item label="下载次数">{{ viewForm.downloadCount }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ parseTime(viewForm.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ parseTime(viewForm.updateTime) }}</el-descriptions-item>
        <el-descriptions-item label="封面图片" :span="2">
          <el-image
            v-if="viewForm.coverImg"
            style="width: 200px; height: 120px;"
            :src="viewForm.coverImg"
            fit="cover"
          ></el-image>
          <span v-else>无封面图片</span>
        </el-descriptions-item>
        <el-descriptions-item label="资源链接" :span="2">
          <div>
            <el-link type="primary" :href="viewForm.resourceUrl" target="_blank">{{ viewForm.resourceUrl }}</el-link>
            <el-button
              v-if="viewForm.resourceUrl"
              size="mini"
              type="success"
              @click="handleDownload(viewForm)"
              style="margin-left: 15px;"
            >
              <i class="el-icon-download"></i> 下载文件
            </el-button>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="资料介绍" :span="2">
          <div v-html="formatNewline(viewForm.materialDesc)"></div>
        </el-descriptions-item>
      </el-descriptions>
      <div slot="footer" class="dialog-footer">
        <el-button @click="viewOpen = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listMaterial, getMaterial, addMaterial, updateMaterial, delMaterial, exportMaterial, downloadMaterial } from "@/api/system/material";
import { listPackage } from "@/api/system/package";
import FileUpload from "@/components/FileUpload";
import ImageUpload from "@/components/ImageUpload";

export default {
  name: "Material",
  dicts: ['sys_normal_disable'],
  components: {
    FileUpload,
    ImageUpload
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 学习资料表格数据
      materialList: [],
      // 套餐选项
      packageOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示查看弹出层
      viewOpen: false,
      // 日期范围
      dateRange: [],
      // 文件类型限制列表
      fileTypeList: ['pdf', 'epub', 'txt', 'doc', 'docx'],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        materialName: null,
        materialType: null,
        theme: null,
        packageId: null,
        status: null,
        sortNum: null
      },
      // 表单参数
      form: {},
      // 查看表单参数
      viewForm: {},
      // 表单校验
      rules: {
        materialName: [
          { required: true, message: "资料名称不能为空", trigger: "blur" }
        ],
        materialType: [
          { required: true, message: "资料类型不能为空", trigger: "change" }
        ],
        resourceUrl: [
          { required: true, message: "资源链接不能为空", trigger: "blur" }
        ],
        packageId: [
          { required: true, message: "所属套餐不能为空", trigger: "change" }
        ],
        status: [
          { required: true, message: "状态不能为空", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getPackageOptions();
  },
  methods: {
    /** 查询学习资料列表 */
    getList() {
      this.loading = true;
      listMaterial(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.materialList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 获取套餐选项 */
    getPackageOptions() {
      listPackage().then(response => {
        this.packageOptions = response.rows;
      });
    },
    // 套餐选择事件
    handlePackageChange(value) {
      const selectedPackage = this.packageOptions.find(item => item.packageId === value);
      if (selectedPackage) {
        this.form.packageName = selectedPackage.packageName;
      }
    },
    // 资料类型变更事件
    handleMaterialTypeChange(value) {
      if (value === "0") { // 电子书
        this.fileTypeList = ['pdf', 'epub', 'txt', 'doc', 'docx'];
      } else { // 视频
        this.fileTypeList = ['mp4', 'avi', 'mov', 'flv'];
      }
    },
    // 文件大小格式化
    formatFileSize(size) {
      if (!size) return "0 KB";
      if (size < 1024) {
        return size + " KB";
      } else if (size < 1024 * 1024) {
        return (size / 1024).toFixed(2) + " MB";
      } else {
        return (size / (1024 * 1024)).toFixed(2) + " GB";
      }
    },
    // 格式化换行文本
    formatNewline(str) {
      if (!str) return '';
      return str.replace(/\n/g, '<br>');
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        materialId: null,
        materialName: null,
        materialType: "0",
        resourceUrl: null,
        coverImg: null,
        materialDesc: null,
        theme: null,
        packageId: null,
        packageName: null,
        fileSize: 0,
        viewCount: 0,
        downloadCount: 0,
        status: "0",
        sortNum: 0
      };
      this.resetForm("form");
      // 重置文件类型列表为默认（电子书）
      this.fileTypeList = ['pdf', 'epub', 'txt', 'doc', 'docx'];
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.materialId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加学习资料";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const materialId = row.materialId || this.ids[0]
      getMaterial(materialId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改学习资料";
      });
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.viewForm = {...row};
      this.viewOpen = true;
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.materialId != null) {
            updateMaterial(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addMaterial(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const materialIds = row.materialId || this.ids;
      this.$modal.confirm('是否确认删除学习资料编号为"' + materialIds + '"的数据项?').then(function() {
        return delMaterial(materialIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/material/export', {
        ...this.queryParams
      }, `material_${new Date().getTime()}.xlsx`)
    },
    // 下载文件操作
    handleDownload(row) {
      // 如果是直接链接下载
      if (row.resourceUrl && (row.resourceUrl.startsWith('http://') || row.resourceUrl.startsWith('https://'))) {
        window.open(row.resourceUrl);
      } else {
        // 使用后端API增加下载次数
        downloadMaterial(row.materialId).then(response => {
          // 触发浏览器下载
          window.open(row.resourceUrl);
          this.$modal.msgSuccess("下载成功");
          // 刷新列表数据，更新下载次数
          this.getList();
        }).catch(() => {
          this.$modal.msgError("下载失败");
        });
      }
    }
  }
};
</script>
