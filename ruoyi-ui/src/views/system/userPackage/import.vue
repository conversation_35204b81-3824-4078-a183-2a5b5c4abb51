<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>批量导入用户套餐</span>
      </div>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="套餐" prop="packageId">
          <el-select v-model="form.packageId" placeholder="请选择套餐" clearable filterable @input="updateUrl">
            <el-option
              v-for="pkg in packageOptions"
              :key="pkg.packageId"
              :label="pkg.packageName"
              :value="pkg.packageId"
            />
          </el-select>
          <el-tooltip class="item" effect="dark" content="请选择要导入的用户套餐，所有导入用户将使用此套餐" placement="top">
            <i class="el-icon-question" style="margin-left: 10px;"></i>
          </el-tooltip>
        </el-form-item>
        <el-form-item label="Excel文件" prop="file">
          <el-upload
            ref="upload"
            :limit="1"
            accept=".xlsx, .xls"
            :headers="upload.headers"
            :action="upload.url"
            :disabled="upload.isUploading"
            :on-progress="handleFileUploadProgress"
            :on-success="handleFileSuccess"
            :on-error="handleFileError"
            :auto-upload="false"
            drag
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">
              请上传Excel文件，文件大小不超过10MB。<br/>
              <el-button
                type="text"
                size="mini"
                icon="el-icon-download"
                @click="handleDownloadTemplate"
              >下载模板</el-button>
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitUpload" :loading="upload.isUploading">{{ upload.isUploading ? '导入中' : '开始导入' }}</el-button>
          <el-button @click="cancel">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 导入结果 -->
    <el-card class="box-card" style="margin-top: 20px;" v-if="importResult.show">
      <div slot="header" class="clearfix">
        <span>导入结果</span>
      </div>
      <div class="result-info">
        <el-row>
          <el-col :span="8">
            <div class="result-item">
              <div class="result-label">总记录数：</div>
              <div class="result-value">{{ importResult.total }}</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="result-item">
              <div class="result-label">成功导入：</div>
              <div class="result-value success">{{ importResult.success }}</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="result-item">
              <div class="result-label">失败记录：</div>
              <div class="result-value error">{{ importResult.fail }}</div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 失败记录表格 -->
      <div v-if="importResult.errors.length > 0" style="margin-top: 20px;">
        <div class="error-title">失败记录详情：</div>
        <el-table :data="importResult.errors" border style="width: 100%">
          <el-table-column prop="rowNum" label="行号" width="80" />
          <el-table-column prop="phone" label="手机号" width="120" />
          <el-table-column prop="errorMsg" label="错误原因" />
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script>
import { listPackage } from "@/api/system/package";
import { getToken } from "@/utils/auth";
import { importUserPackage } from "@/api/system/userPackage";
import { download } from "@/utils/request";

export default {
  name: "ImportUserPackage",
  data() {
    return {
      // 表单参数
      form: {
        packageId: 0,
      },
      // 表单校验
      rules: {
        packageId: [
          { required: true, message: "套餐不能为空", trigger: "change" }
        ],
      },
      // 套餐选项
      packageOptions: [],
      // 上传参数
      upload: {
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + `/system/userPackage/importData/${this.getPackageId}`,
      },
      // 下载模板路径
      templatePath: process.env.VUE_APP_BASE_API + "/system/userPackage/importTemplate",
      // 导入结果
      importResult: {
        show: false,
        total: 0,
        success: 0,
        fail: 0,
        errors: []
      }
    };
  },
  created() {
    this.getPackages();
  },
  computed:{
    getPackageId(){
      return Number.parseInt(this.form.packageId?
        this.form.packageId:0)
    }
  },
  methods: {
    updateUrl(){
      this.upload.url = process.env.VUE_APP_BASE_API + `/system/userPackage/importData/${this.form.packageId}`;
    },
    /** 查询套餐列表 */
    getPackages() {
      listPackage().then(response => {
        this.packageOptions = response.rows;
      });
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();

      // 显示导入结果
      this.importResult.show = true;
      this.importResult.total = response.data.total;
      this.importResult.success = response.data.success;
      this.importResult.fail = response.data.fail;
      this.importResult.errors = response.data.errors || [];
      this.upload.url = process.env.VUE_APP_BASE_API + `/system/userPackage/importData/${this.form.packageId}`;
      this.$modal.msgSuccess(
        `导入成功${response.data.success}条，失败${response.data.fail}条`
      );
    },
    // 文件上传失败处理
    handleFileError(err) {
      this.upload.isUploading = false;
      this.$modal.msgError("导入失败，请重试");
    },
    // 提交上传
    submitUpload() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.packageId) {
            // 将packageId作为查询参数添加到URL中
            this.$refs.upload.action = this.upload.url + "?packageId=" + this.form.packageId;
          } else {
            this.$refs.upload.action = this.upload.url;
          }

          this.$refs.upload.submit();
        }
      });
    },
    // 取消按钮
    cancel() {
      this.$router.push({ path: "/system/userPackage" });
    },
    // 下载模板
    handleDownloadTemplate() {
      // 使用download方法下载模板
      this.download('/system/userPackage/importTemplate', {}, '用户套餐导入模板.xlsx', 'post');
    }
  }
};
</script>

<style scoped>
.result-info {
  margin-bottom: 20px;
}
.result-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
.result-label {
  font-weight: bold;
  margin-right: 10px;
}
.result-value {
  font-size: 18px;
  font-weight: bold;
}
.success {
  color: #67C23A;
}
.error {
  color: #F56C6C;
}
.error-title {
  font-weight: bold;
  margin-bottom: 10px;
  color: #F56C6C;
}
</style>
