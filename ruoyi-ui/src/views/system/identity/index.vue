<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="配置名称" prop="configName">
        <el-input
          v-model="queryParams.configName"
          placeholder="请输入配置名称"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:identity:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-refresh"
          size="mini"
          @click="handleRefreshCache"
          v-hasPermi="['system:identity:remove']"
        >刷新缓存</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-card v-loading="loading" shadow="never" class="identity-card">
      <div slot="header" class="clearfix">
        <span>系统标识配置</span>
      </div>
      <el-form label-width="120px" label-position="left">
        <el-row>
          <el-col :span="12">
            <!-- 系统LOGO配置 -->
            <el-form-item label="系统LOGO">
              <div class="config-item">
                <div class="config-value">
                  <el-image 
                    v-if="identityConfigs['sys.index.logo']" 
                    :src="identityConfigs['sys.index.logo']" 
                    fit="contain"
                    style="height: 60px; max-width: 200px;"
                  >
                    <div slot="error" class="image-slot">
                      <i class="el-icon-picture-outline"></i> 图片无法显示
                    </div>
                  </el-image>
                  <span v-else class="empty-value">未设置</span>
                </div>
                <div class="config-action">
                  <el-button 
                    type="text" 
                    icon="el-icon-edit" 
                    @click="handleEditConfig('系统Logo', 'sys.index.logo')"
                    v-hasPermi="['system:identity:edit']"
                  >修改</el-button>
                </div>
              </div>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <!-- 联系人姓名配置 -->
            <el-form-item label="联系人姓名">
              <div class="config-item">
                <div class="config-value">
                  <span>{{ identityConfigs['sys.contact.name'] || '未设置' }}</span>
                </div>
                <div class="config-action">
                  <el-button 
                    type="text" 
                    icon="el-icon-edit" 
                    @click="handleEditConfig('联系人姓名', 'sys.contact.name')"
                    v-hasPermi="['system:identity:edit']"
                  >修改</el-button>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <!-- 联系人电话配置 -->
            <el-form-item label="联系人电话">
              <div class="config-item">
                <div class="config-value">
                  <span>{{ identityConfigs['sys.contact.phone'] || '未设置' }}</span>
                </div>
                <div class="config-action">
                  <el-button 
                    type="text" 
                    icon="el-icon-edit" 
                    @click="handleEditConfig('联系人电话', 'sys.contact.phone')"
                    v-hasPermi="['system:identity:edit']"
                  >修改</el-button>
                </div>
              </div>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <!-- 客服电话配置 -->
            <el-form-item label="客服电话">
              <div class="config-item">
                <div class="config-value">
                  <span>{{ identityConfigs['sys.service.phone'] || '未设置' }}</span>
                </div>
                <div class="config-action">
                  <el-button 
                    type="text" 
                    icon="el-icon-edit" 
                    @click="handleEditConfig('客服电话', 'sys.service.phone')"
                    v-hasPermi="['system:identity:edit']"
                  >修改</el-button>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 所有系统标识配置列表 -->
    <el-table v-loading="loading" :data="configList" style="margin-top: 20px;">
      <el-table-column label="配置名称" align="center" prop="configName" />
      <el-table-column label="配置键名" align="center" prop="configKey" />
      <el-table-column label="配置键值" align="center" prop="configValue" :show-overflow-tooltip="true" />
      <el-table-column label="备注" align="center" prop="remark" :show-overflow-tooltip="true" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:identity:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:identity:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改参数配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="配置名称" prop="configName">
          <el-input v-model="form.configName" placeholder="请输入配置名称" />
        </el-form-item>
        <el-form-item label="配置键名" prop="configKey">
          <el-input v-model="form.configKey" placeholder="请输入配置键名" />
        </el-form-item>
        <el-form-item label="配置键值" prop="configValue">
          <el-input v-model="form.configValue" type="textarea" placeholder="请输入配置键值" />
        </el-form-item>
        <el-form-item label="系统内置" prop="configType">
          <el-radio-group v-model="form.configType">
            <el-radio
              v-for="dict in dict.type.sys_yes_no"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listIdentity, getIdentity, delIdentity, addIdentity, updateIdentity, refreshIdentityCache } from "@/api/system/identity"

export default {
  name: "Identity",
  dicts: ['sys_yes_no'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 系统标识配置表格数据
      configList: [],
      // 系统标识配置映射
      identityConfigs: {},
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        configName: undefined,
        configKey: "sys."
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        configName: [
          { required: true, message: "配置名称不能为空", trigger: "blur" }
        ],
        configKey: [
          { required: true, message: "配置键名不能为空", trigger: "blur" }
        ],
        configValue: [
          { required: true, message: "配置键值不能为空", trigger: "blur" }
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      this.loading = true
      listIdentity(this.queryParams).then(response => {
        this.configList = response.rows.filter(item => {
          return item.configKey.indexOf('sys.') === 0
        })
        this.total = this.configList.length
        
        // 处理系统标识配置
        this.identityConfigs = {}
        const identityKeys = ['sys.index.logo', 'sys.contact.phone', 'sys.service.phone', 'sys.contact.name']
        this.configList.forEach(item => {
          if (identityKeys.includes(item.configKey)) {
            this.identityConfigs[item.configKey] = item.configValue
          }
        })
        
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        configId: undefined,
        configName: undefined,
        configKey: undefined,
        configValue: undefined,
        configType: "N",
        remark: undefined
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.queryParams.configKey = "sys."
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.form.configKey = "sys."
      this.open = true
      this.title = "添加系统标识配置"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const configId = row.configId
      getIdentity(configId).then(response => {
        this.form = response.data
        this.open = true
        this.title = "修改系统标识配置"
      })
    },
    /** 修改指定配置 */
    handleEditConfig(configName, configKey) {
      this.reset()
      // 查找已有配置
      const config = this.configList.find(item => item.configKey === configKey)
      if (config) {
        getIdentity(config.configId).then(response => {
          this.form = response.data
          this.open = true
          this.title = "修改" + configName
        })
      } else {
        // 新建配置
        this.form = {
          configName: configName,
          configKey: configKey,
          configValue: '',
          configType: 'N',
          remark: configName + '配置'
        }
        this.open = true
        this.title = "添加" + configName
      }
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.configId != undefined) {
            updateIdentity(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            addIdentity(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const configIds = row.configId
      this.$modal.confirm('是否确认删除配置名称为"' + row.configName + '"的数据项？').then(function() {
        return delIdentity(configIds)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 刷新缓存按钮操作 */
    handleRefreshCache() {
      refreshIdentityCache().then(() => {
        this.$modal.msgSuccess("刷新成功")
      })
    }
  }
}
</script>

<style scoped>
.identity-card {
  margin-bottom: 20px;
}
.config-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
  padding: 8px 0;
}
.config-value {
  flex: 1;
}
.config-action {
  margin-left: 20px;
}
.empty-value {
  color: #999;
  font-style: italic;
}
</style> 