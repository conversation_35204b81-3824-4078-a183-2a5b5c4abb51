<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="用户昵称" prop="nickName">
        <el-input
          v-model="queryParams.nickName"
          placeholder="请输入用户昵称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="题目内容" prop="questionText">
        <el-input
          v-model="queryParams.questionText"
          placeholder="请输入题目内容"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
<!--      <el-form-item label="状态" prop="status">-->
<!--        <el-select v-model="queryParams.status" placeholder="复习状态" clearable>-->
<!--          <el-option label="未复习" value="0" />-->
<!--          <el-option label="已复习" value="1" />-->
<!--        </el-select>-->
<!--      </el-form-item>-->
      <el-form-item label="错题日期">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:mistake:remove']"
        >删除</el-button>
      </el-col>
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="warning"-->
<!--          plain-->
<!--          icon="el-icon-download"-->
<!--          size="mini"-->
<!--          @click="handleExport"-->
<!--          v-hasPermi="['system:mistake:export']"-->
<!--        >导出</el-button>-->
<!--      </el-col>-->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="mistakeList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="错题ID" align="center" prop="mistakeId" width="80" />
      <el-table-column label="用户头像" align="center" prop="user.avatarUrl">
        <template slot-scope="scope">
          <image-preview :src="scope.row.user.avatarUrl" :width="50" :height="50"/>
        </template>
      </el-table-column>
      <el-table-column label="用户昵称" align="center" prop="user.nickName" :show-overflow-tooltip="true" />
      <el-table-column label="用户手机号" align="center" prop="user.phone"></el-table-column>
      <el-table-column label="题目内容" align="center" prop="question.questionContent" :show-overflow-tooltip="true" />
      <el-table-column label="题目类型" align="center" prop="question.questionType">
        <template slot-scope="scope">
          <span v-if="scope.row.question && scope.row.question.questionType === '1'">单选题</span>
          <span v-else-if="scope.row.question && scope.row.question.questionType === '2'">多选题</span>
          <span v-else-if="scope.row.question && scope.row.question.questionType === '3'">判断题</span>
          <span v-else>未知</span>
        </template>
      </el-table-column>
      <el-table-column label="错误答案" align="center" prop="wrongAnswer" :show-overflow-tooltip="true" />
      <el-table-column label="正确答案" align="center" prop="question.correctAnswer" :show-overflow-tooltip="true" />
      <el-table-column label="错误次数" align="center" prop="mistakeCount" />
      <el-table-column label="最后错误时间" align="center" prop="lastMistakeTime" width="160">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.lastMistakeTime) }}</span>
        </template>
      </el-table-column>
<!--      <el-table-column label="状态" align="center" prop="status" width="100">-->
<!--        <template slot-scope="scope">-->
<!--          <el-switch-->
<!--            v-model="scope.row.status"-->
<!--            active-value="1"-->
<!--            inactive-value="0"-->
<!--            active-text="已复习"-->
<!--            inactive-text="未复习"-->
<!--            @change="handleStatusChange(scope.row)"-->
<!--          ></el-switch>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(scope.row)"
            v-hasPermi="['system:mistake:query']"
          >详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:mistake:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 错题详情对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" label-width="100px" v-if="form.question">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>题目信息</span>
          </div>
          <el-form-item label="题目内容">
            <div>{{ form.question.questionContent }}</div>
          </el-form-item>
          <el-form-item label="题目类型">
            <div>
              <span v-if="form.question.questionType === '1'">单选题</span>
              <span v-else-if="form.question.questionType === '2'">多选题</span>
              <span v-else-if="form.question.questionType === '3'">判断题</span>
              <span v-else>未知</span>
            </div>
          </el-form-item>
          <el-form-item label="选项" v-if="form.question.options">
            <div v-html="formatOptions(form.question.options)"></div>
          </el-form-item>
          <el-form-item label="正确答案">
            <div class="correct-answer">{{ form.question.correctAnswer }}</div>
          </el-form-item>
          <el-form-item label="答案解析" v-if="form.question.analysis">
            <div>{{ form.question.analysis }}</div>
          </el-form-item>
        </el-card>

        <el-card class="box-card mistake-info">
          <div slot="header" class="clearfix">
            <span>错题信息</span>
          </div>
          <el-form-item label="用户">
            <div>{{ form.user ? form.user.nickName : '' }}</div>
          </el-form-item>
          <el-form-item label="用户手机号">
            <div>{{ form.user?form.user.phone:'' }}</div>
          </el-form-item>
          <el-form-item label="错误答案">
            <div class="wrong-answer">{{ form.wrongAnswer }}</div>
          </el-form-item>
          <el-form-item label="错误次数">
            <div>{{ form.mistakeCount }}</div>
          </el-form-item>
          <el-form-item label="最后错误时间">
            <div>{{ parseTime(form.lastMistakeTime) }}</div>
          </el-form-item>
<!--          <el-form-item label="复习状态">-->
<!--            <el-radio-group v-model="form.status" @change="statusChange">-->
<!--              <el-radio label="0">未复习</el-radio>-->
<!--              <el-radio label="1">已复习</el-radio>-->
<!--            </el-radio-group>-->
<!--          </el-form-item>-->
        </el-card>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listMistake, getMistake, delMistake, exportMistake, updateMistakeStatus } from "@/api/system/mistake";

export default {
  name: "Mistake",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 错题表格数据
      mistakeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 日期范围
      dateRange: [],
      // 表单参数
      form: {},
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        nickName: undefined,
        questionText: undefined,
        status: undefined
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询错题列表 */
    getList() {
      this.loading = true;
      listMistake(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.mistakeList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        mistakeId: undefined,
        userId: undefined,
        questionId: undefined,
        wrongAnswer: undefined,
        mistakeCount: undefined,
        lastMistakeTime: undefined,
        status: "0",
        question: {},
        user: {}
      };
    },
    // 格式化选项
    formatOptions(options) {
      if (!options) return '';
      try {
        const optionsObj = JSON.parse(options);
        let result = '';
        for (const key in optionsObj) {
          result += `<div>${key}. ${optionsObj[key]}</div>`;
        }
        return result;
      } catch (e) {
        return options;
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.mistakeId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 详情按钮操作 */
    handleDetail(row) {
      this.reset();
      const mistakeId = row.mistakeId || this.ids[0];
      getMistake(mistakeId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "错题详情";
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const mistakeIds = row.mistakeId || this.ids;
      this.$modal.confirm('是否确认删除错题编号为"' + mistakeIds + '"的数据项?').then(function() {
        return delMistake(mistakeIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.$modal.confirm('是否确认导出所有错题数据项?').then(() => {
        this.exportLoading = true;
        return exportMistake(this.queryParams);
      }).then(response => {
        this.download(response.msg);
        this.exportLoading = false;
      }).catch(() => {});
    },
    /** 状态修改 */
    handleStatusChange(row) {
      let text = row.status === "1" ? "已复习" : "未复习";
      this.$modal.confirm('确认将该错题标记为"' + text + '"状态吗?').then(function() {
        return updateMistakeStatus(row.mistakeId, row.status);
      }).then(() => {
        this.$modal.msgSuccess("状态更新成功");
      }).catch(function() {
        row.status = row.status === "1" ? "0" : "1";
      });
    },
    // 详情页状态修改
    statusChange(value) {
      updateMistakeStatus(this.form.mistakeId, this.form.status).then(() => {
        this.$modal.msgSuccess("状态更新成功");
        this.getList();
      })
    }
  }
};
</script>

<style scoped>
.mistake-info {
  margin-top: 20px;
}
.correct-answer {
  color: #67C23A;
  font-weight: bold;
}
.wrong-answer {
  color: #F56C6C;
  font-weight: bold;
}
</style>
