<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="用户姓名" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入用户姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户手机号" prop="phone">
        <el-input
          v-model="queryParams.phone"
          placeholder="请输入用户手机号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="套餐名称" prop="packageName">
        <el-input
          v-model="queryParams.packageName"
          placeholder="请输入套餐名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="考试时间" prop="examTimeRange">
        <el-date-picker
          v-model="examTimeRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>


      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:examRecord:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:examRecord:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:examRecord:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:examRecord:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="examRecordList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="记录ID" align="center" prop="recordId" />
      <el-table-column label="用户姓名" width="200" sortable align="center" prop="user.nickName" :show-overflow-tooltip="true" />
      <el-table-column label="用户手机号" align="center" prop="user.phone" width="120">
        <template slot-scope="scope">
          {{ scope.row.user ? scope.row.user.phone : '-' }}
        </template>
      </el-table-column>
      <el-table-column label="套餐名称" align="center" prop="examPackage.packageName" :show-overflow-tooltip="true" />
      <el-table-column label="考试开始时间" align="center" prop="examStartTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.examStartTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="考试结束时间" align="center" prop="examEndTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.examEndTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="分数" align="center" prop="score" />
<!--      <el-table-column label="正确数" align="center" prop="correctCount" />-->
<!--      <el-table-column label="错误数" align="center" prop="wrongCount" />-->
      <el-table-column label="空题数" align="center" prop="emptyCount" />
      <el-table-column label="完成率" align="center" prop="finishRate" />
      <el-table-column label="是否合格" align="center" prop="isPass">
        <template slot-scope="scope">
          <el-tag>{{ scope.row.isPass}}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="是否交卷" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <el-tag type="success" v-if="scope.row.state==1">
            已交卷
          </el-tag>
          <el-tag type="error" v-if="scope.row.state==0">
            未交卷
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:examRecord:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:examRecord:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改考试记录对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="用户" prop="userId">
          <el-select v-model="form.userId" placeholder="请选择用户" clearable filterable>
            <el-option
              v-for="user in userOptions"
              :key="user.userId"
              :label="user.nickName"
              :value="user.userId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="套餐" prop="packageId">
          <el-select v-model="form.packageId" placeholder="请选择套餐" clearable filterable>
            <el-option
              v-for="pkg in packageOptions"
              :key="pkg.packageId"
              :label="pkg.packageName"
              :value="pkg.packageId"
            />
          </el-select>
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="考试开始时间" prop="examStartTime">
              <el-date-picker
                v-model="form.examStartTime"
                type="datetime"
                placeholder="选择考试开始时间"
                value-format="yyyy-MM-dd HH:mm:ss">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="考试结束时间" prop="examEndTime">
              <el-date-picker
                v-model="form.examEndTime"
                type="datetime"
                placeholder="选择考试结束时间"
                value-format="yyyy-MM-dd HH:mm:ss">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="分数" prop="score">
              <el-input-number v-model="form.score" :min="0" :max="100" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="正确数" prop="correctCount">
              <el-input-number v-model="form.correctCount" :min="0" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="错误数" prop="wrongCount">
              <el-input-number v-model="form.wrongCount" :min="0" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="空题数" prop="emptyCount">
              <el-input-number v-model="form.emptyCount" :min="0" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listExamRecord, getExamRecord, delExamRecord, addExamRecord, updateExamRecord } from "@/api/system/examRecord";
import { listWxUser } from "@/api/system/wxuser";
import { listPackage } from "@/api/system/package";

export default {
  name: "ExamRecord",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 考试记录表格数据
      examRecordList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 考试时间范围
      examTimeRange: [],
      // 用户选项
      userOptions: [],
      // 套餐选项
      packageOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userName: undefined,
        phone: undefined,
        packageName: undefined,
        score: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        userId: [
          { required: true, message: "用户不能为空", trigger: "change" }
        ],
        packageId: [
          { required: true, message: "套餐不能为空", trigger: "change" }
        ],
        examStartTime: [
          { required: true, message: "考试开始时间不能为空", trigger: "blur" }
        ],
        examEndTime: [
          { required: true, message: "考试结束时间不能为空", trigger: "blur" }
        ],
        score: [
          { required: true, message: "分数不能为空", trigger: "blur" }
        ],
        correctCount: [
          { required: true, message: "正确数不能为空", trigger: "blur" }
        ],
        wrongCount: [
          { required: true, message: "错误数不能为空", trigger: "blur" }
        ],
        emptyCount: [
          { required: true, message: "空题数不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getUsers();
    this.getPackages();
  },
  methods: {
    /** 查询考试记录列表 */
    getList() {
      this.loading = true;
      listExamRecord(this.addDateRange(this.queryParams, this.examTimeRange, 'examStartTime')).then(response => {
        this.examRecordList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 查询用户列表 */
    getUsers() {
      listWxUser().then(response => {
        this.userOptions = response.rows;
      });
    },
    /** 查询套餐列表 */
    getPackages() {
      listPackage().then(response => {
        this.packageOptions = response.rows;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        recordId: undefined,
        userId: undefined,
        packageId: undefined,
        examStartTime: undefined,
        examEndTime: undefined,
        score: undefined,
        correctCount: undefined,
        wrongCount: undefined,
        emptyCount: undefined,
        status: "0",
        state: "0",
        finishIds: undefined,
        finishAnswer: undefined,
        forgetIds: undefined,
        remark: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.examTimeRange = [];
      this.handleQuery();
    },
    /** 选择条目触发事件 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.recordId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加考试记录";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const recordId = row.recordId || this.ids[0]
      getExamRecord(recordId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改考试记录";
      });
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.recordId != undefined) {
            updateExamRecord(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addExamRecord(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const recordIds = row.recordId || this.ids;
      this.$modal.confirm('是否确认删除考试记录编号为"' + recordIds + '"的数据项?').then(function() {
        return delExamRecord(recordIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/examRecord/export', {
        ...this.queryParams
      }, `考试记录数据_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
