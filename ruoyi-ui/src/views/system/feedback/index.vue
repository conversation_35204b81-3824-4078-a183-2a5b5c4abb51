<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="反馈类型" prop="feedbackType">
        <el-select v-model="queryParams.feedbackType" placeholder="反馈类型" clearable>
          <el-option
            v-for="dict in dict.type.feedback_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="反馈状态" clearable>
          <el-option
            v-for="dict in dict.type.feedback_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="提交时间" prop="createTime">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:feedback:remove']"
        >删除</el-button>
      </el-col>
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="warning"-->
<!--          plain-->
<!--          icon="el-icon-download"-->
<!--          size="mini"-->
<!--          @click="handleExport"-->
<!--          v-hasPermi="['system:feedback:export']"-->
<!--        >导出</el-button>-->
<!--      </el-col>-->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="feedbackList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="feedbackId" width="80" />
      <el-table-column label="用户昵称" align="center" prop="user.nickName" :show-overflow-tooltip="true" />
      <el-table-column label="反馈内容" align="center" prop="content" :show-overflow-tooltip="true" />
      <el-table-column label="反馈类型" align="center" prop="feedbackType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.feedback_type" :value="scope.row.feedbackType"/>
        </template>
      </el-table-column>
      <el-table-column label="联系方式" align="center" prop="contact" :show-overflow-tooltip="true" />
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.feedback_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="提交时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['system:feedback:query']"
          >详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleReply(scope.row)"
            v-hasPermi="['system:feedback:reply']"
            v-if="scope.row.status === '0'"
          >回复</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:feedback:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 详情对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="用户昵称">
              <span>{{ form.user ? form.user.nickName : '' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系方式">
              <span>{{ form.contact }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="反馈类型">
              <dict-tag :options="dict.type.feedback_type" :value="form.feedbackType"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="提交时间">
              <span>{{ parseTime(form.createTime) }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="反馈内容">
          <el-input v-model="form.content" type="textarea" :rows="4" readonly />
        </el-form-item>
        <el-form-item label="附件图片" v-if="form.images">
          <div class="image-list">
            <el-image
              v-for="(url, index) in imageList"
              :key="index"
              :src="url"
              style="width: 100px; height: 100px; margin-right: 10px; margin-bottom: 10px;"
              :preview-src-list="imageList">
            </el-image>
          </div>
        </el-form-item>
        <el-divider content-position="center">回复信息</el-divider>
        <el-form-item label="回复内容" prop="reply">
          <el-input v-model="form.reply" type="textarea" :rows="4" placeholder="请输入回复内容" :readonly="readonly" />
        </el-form-item>
        <el-form-item label="回复人" v-if="form.replyBy">
          <span>{{ form.replyBy }}</span>
        </el-form-item>
        <el-form-item label="回复时间" v-if="form.replyTime">
          <span>{{ parseTime(form.replyTime) }}</span>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer" v-if="!readonly">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
      <div slot="footer" class="dialog-footer" v-else>
        <el-button @click="cancel">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listFeedback, getFeedback, delFeedback, replyFeedback, exportFeedback } from "@/api/system/feedback";

export default {
  name: "Feedback",
  dicts: ['feedback_type', 'feedback_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 反馈表格数据
      feedbackList: [],
      // 弹出层标题
      dialogTitle: "",
      // 是否显示弹出层
      open: false,
      // 是否只读
      readonly: false,
      // 日期范围
      dateRange: [],
      // 图片列表
      imageList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        feedbackType: undefined,
        status: undefined,
      },
      // 表单参数
      form: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询反馈列表 */
    getList() {
      this.loading = true;
      listFeedback(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.feedbackList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        feedbackId: undefined,
        userId: undefined,
        content: undefined,
        contact: undefined,
        feedbackType: undefined,
        images: undefined,
        status: '0',
        reply: undefined,
        replyTime: undefined,
        replyBy: undefined
      };
      this.imageList = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.dateRange = [];
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.feedbackId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 详情按钮操作 */
    handleView(row) {
      this.reset();
      const feedbackId = row.feedbackId || this.ids[0]
      getFeedback(feedbackId).then(response => {
        this.form = response.data;
        this.dialogTitle = "反馈详情";
        this.readonly = true;
        // 处理图片
        if (this.form.images) {
          this.imageList = this.form.images.split(',');
        }
        this.open = true;
      });
    },
    /** 回复按钮操作 */
    handleReply(row) {
      this.reset();
      const feedbackId = row.feedbackId || this.ids[0]
      getFeedback(feedbackId).then(response => {
        this.form = response.data;
        this.dialogTitle = "回复反馈";
        this.readonly = false;
        // 处理图片
        if (this.form.images) {
          this.imageList = this.form.images.split(',');
        }
        this.open = true;
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (!this.form.reply || this.form.reply.trim() === '') {
            this.$message.error("请输入回复内容");
            return;
          }
          replyFeedback(this.form).then(response => {
            this.$modal.msgSuccess("回复成功");
            this.open = false;
            this.getList();
          });
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const feedbackIds = row.feedbackId || this.ids;
      this.$modal.confirm('是否确认删除反馈编号为"' + feedbackIds + '"的数据项?').then(function() {
        return delFeedback(feedbackIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/feedback/export', {
        ...this.queryParams
      }, `反馈数据_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>

<style scoped>
.image-list {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10px;
}
</style>
