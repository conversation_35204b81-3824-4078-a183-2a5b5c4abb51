<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="用户姓名" prop="nickName">
        <el-input
          v-model="queryParams.nickName"
          placeholder="请输入用户姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户手机号" prop="phone">
        <el-input
          v-model="queryParams.phone"
          placeholder="请输入用户手机号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="套餐名" prop="packageName">
        <el-input
          v-model="queryParams.packageName"
          placeholder="请输入套餐名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="资料类型" prop="materialType">
        <el-select v-model="queryParams.materialType" placeholder="资料类型" clearable>
          <el-option label="电子书" value="0" />
          <el-option label="视频" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:record:remove']"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="recordList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="记录ID" align="center" prop="recordId" />
<!--      <el-table-column label="用户ID" align="center" prop="userId" />-->
      <el-table-column label="用户昵称" align="center" prop="nickName" :show-overflow-tooltip="true" />
      <el-table-column label="用户手机号" align="center" prop="phone" width="120" />
<!--      <el-table-column label="资料ID" align="center" prop="materialId" />-->
      <el-table-column label="资料名称" align="center" prop="materialName" :show-overflow-tooltip="true" />
      <el-table-column label="资料类型" align="center" prop="materialType">
        <template slot-scope="scope">
          <el-tag :type="scope.row.materialType === '0' ? 'primary' : 'success'">
            {{ scope.row.materialType === '0' ? '电子书' : '视频' }}
          </el-tag>
        </template>
      </el-table-column>
<!--      <el-table-column label="套餐ID" align="center" prop="packageId" />-->
      <el-table-column label="套餐名称" align="center" prop="packageName" :show-overflow-tooltip="true" />
<!--      <el-table-column label="学习进度" align="center" prop="progress">-->
<!--        <template slot-scope="scope">-->
<!--          <el-progress :percentage="scope.row.progress" :status="getProgressStatus(scope.row.progress)"></el-progress>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="学习位置" align="center" prop="lastPosition" />-->
      <el-table-column label="学习次数" align="center" prop="studyTime">
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" prop="updateTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['system:record:query']"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:record:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 学习记录详情对话框 -->
    <el-dialog title="学习记录详情" :visible.sync="open" width="700px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="记录ID">{{ form.recordId }}</el-descriptions-item>
        <el-descriptions-item label="用户ID">{{ form.userId }}</el-descriptions-item>
        <el-descriptions-item label="用户昵称">{{ form.nickName }}</el-descriptions-item>
        <el-descriptions-item label="资料ID">{{ form.materialId }}</el-descriptions-item>
        <el-descriptions-item label="资料名称">{{ form.materialName }}</el-descriptions-item>
        <el-descriptions-item label="资料类型">
          <el-tag :type="form.materialType === '0' ? 'primary' : 'success'">
            {{ form.materialType === '0' ? '电子书' : '视频' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="套餐ID">{{ form.packageId }}</el-descriptions-item>
        <el-descriptions-item label="套餐名称">{{ form.packageName }}</el-descriptions-item>
        <el-descriptions-item label="学习进度" :span="2">
          <el-progress :percentage="form.progress" :status="getProgressStatus(form.progress)"></el-progress>
        </el-descriptions-item>
        <el-descriptions-item label="学习位置">{{ form.lastPosition }}</el-descriptions-item>
        <el-descriptions-item label="学习时长">{{ formatStudyTime(form.studyTime) }}</el-descriptions-item>
        <el-descriptions-item label="首次学习时间">{{ parseTime(form.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="最近学习时间">{{ parseTime(form.updateTime) }}</el-descriptions-item>
      </el-descriptions>
      <div slot="footer" class="dialog-footer">
        <el-button @click="open = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listRecord, getRecord, delRecord, exportRecord } from "@/api/system/record";

export default {
  name: "Record",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 学习记录表格数据
      recordList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        nickName: null,
        phone: null,
        packageName: null,
        materialType: null
      },
      // 表单参数
      form: {}
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询学习记录列表 */
    getList() {
      this.loading = true;
      listRecord(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.recordList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 获取进度状态
    getProgressStatus(progress) {
      if (progress >= 100) {
        return 'success';
      } else if (progress >= 50) {
        return '';
      } else {
        return 'exception';
      }
    },
    // 格式化学习时长
    formatStudyTime(seconds) {
      if (!seconds) return '0秒';

      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const remainingSeconds = seconds % 60;

      let result = '';
      if (hours > 0) {
        result += hours + '小时';
      }
      if (minutes > 0) {
        result += minutes + '分钟';
      }
      if (remainingSeconds > 0 || (hours === 0 && minutes === 0)) {
        result += remainingSeconds + '秒';
      }

      return result;
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.recordId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.form = row;
      this.open = true;
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const recordIds = row.recordId || this.ids;
      this.$modal.confirm('是否确认删除学习记录编号为"' + recordIds + '"的数据项?').then(function() {
        return delRecord(recordIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/record/export', {
        ...this.queryParams
      }, `record_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
