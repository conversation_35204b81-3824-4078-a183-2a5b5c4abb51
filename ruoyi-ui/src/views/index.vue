<template>
  <div class="app-container home">
    <!-- 第一行：平台用户数、总线上营业额、总线下营业额 -->
    <el-row :gutter="20">
      <el-col :span="8">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>平台用户数</span>
          </div>
          <div class="card-panel">
            <div class="card-panel-icon-wrapper">
              <svg-icon icon-class="peoples" class-name="card-panel-icon" />
            </div>
            <div class="card-panel-description">
              <div class="card-panel-text">总用户数</div>
              <count-to :start-val="0" :end-val="userCount" :duration="2000" class="card-panel-num" />
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>总线上营业额</span>
          </div>
          <div class="card-panel">
            <div class="card-panel-icon-wrapper">
              <svg-icon icon-class="money" class-name="card-panel-icon" />
            </div>
            <div class="card-panel-description">
              <div class="card-panel-text">微信支付</div>
              <div class="card-panel-num">￥{{ totalOnlineRevenue.toFixed(2) }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>线下用户数</span>
          </div>
          <div class="card-panel">
            <div class="card-panel-icon-wrapper">
              <svg-icon icon-class="peoples" class-name="card-panel-icon" />
            </div>
            <div class="card-panel-description">
              <div class="card-panel-text">线下用户</div>
              <count-to :start-val="0" :end-val="totalOfflineRevenue" :duration="2000" class="card-panel-num" />
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 第二行：今日线上订单数、今日线下订单数、今日线上营业额、今日线下营业额 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>本月线下用户数</span>
          </div>
          <div class="card-panel">
            <div class="card-panel-icon-wrapper">
              <svg-icon icon-class="peoples" class-name="card-panel-icon" />
            </div>
            <div class="card-panel-description">
              <div class="card-panel-text">线下用户</div>
              <count-to :start-val="0" :end-val="todayOnlineOrderCount" :duration="2000" class="card-panel-num" />
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>本月线上用户数</span>
          </div>
          <div class="card-panel">
            <div class="card-panel-icon-wrapper">
              <svg-icon icon-class="peoples" class-name="card-panel-icon" />
            </div>
            <div class="card-panel-description">
              <div class="card-panel-text">线上用户</div>
              <count-to :start-val="0" :end-val="todayOfflineOrderCount" :duration="2000" class="card-panel-num" />
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>今日线上营业额</span>
          </div>
          <div class="card-panel">
            <div class="card-panel-icon-wrapper">
              <svg-icon icon-class="money" class-name="card-panel-icon" />
            </div>
            <div class="card-panel-description">
              <div class="card-panel-text">微信支付</div>
              <div class="card-panel-num">￥{{ todayOnlineRevenue.toFixed(2) }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>本月线上营业额</span>
          </div>
          <div class="card-panel">
            <div class="card-panel-icon-wrapper">
              <svg-icon icon-class="money" class-name="card-panel-icon" />
            </div>
            <div class="card-panel-description">
              <div class="card-panel-text">微信支付</div>
              <div class="card-panel-num">￥{{ todayOfflineRevenue.toFixed(2) }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
<!--      <el-col :span="12">-->
<!--        <el-card class="box-card">-->
<!--          <div slot="header" class="clearfix">-->
<!--            <span>线上/线下营业额趋势对比图（最近7天）</span>-->
<!--          </div>-->
<!--          <div class="chart-wrapper">-->
<!--            <div ref="revenueChart" style="height: 300px;"></div>-->
<!--          </div>-->
<!--        </el-card>-->
<!--      </el-col>-->
      <el-col :span="24">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>报考方向统计</span>
          </div>
          <div class="chart-wrapper">
            <div ref="packageChart" style="height: 300px;"></div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="24">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>指定时间段营业额统计</span>
            <div style="float: right;">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
                @change="handleDateRangeChange">
              </el-date-picker>
              <el-button type="primary" size="small" style="margin-left: 10px;" @click="getCustomRangeRevenue">查询</el-button>
            </div>
          </div>
          <div class="revenue-result">
            <div class="revenue-label">线上营业额（微信支付）：</div>
            <div class="revenue-value">
              <div class="revenue-number">￥{{ customRangeOnlineRevenue.toFixed(2) }}</div>
            </div>
            <div class="revenue-label">线上订单数量：</div>
            <div class="revenue-value">
              <count-to :start-val="0" :end-val="customRangeOnlineOrders" :duration="2000" />
            </div>
            <div class="revenue-label">线下营业额（后台新增）：</div>
            <div class="revenue-value">
              <div class="revenue-number">￥{{ customRangeOfflineRevenue.toFixed(2) }}</div>
            </div>
            <div class="revenue-label">线下订单数量：</div>
            <div class="revenue-value">
              <count-to :start-val="0" :end-val="customRangeOfflineOrders" :duration="2000" />
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { getDashboardStatistics, getRevenueTrend, getCustomRangeRevenue, getDashboardChannelStatistics, getCustomRangeChannelRevenue, getChannelRevenueTrend, getPackageOrderStatistics } from "@/api/system/statistics";
import CountTo from 'vue-count-to'
import * as echarts from 'echarts'

export default {
  name: "Index",
  components: {
    CountTo
  },
  data() {
    return {
      // 版本号
      version: "3.8.9",
      // 统计数据
      userCount: 0,
      todayOnlineOrderCount: 0, // 本月线下用户数
      todayOfflineOrderCount: 0, // 本月线上用户数
      todayOnlineRevenue: 0,
      todayOfflineRevenue: 0, // 本月线上营业额
      totalOnlineRevenue: 0,
      totalOfflineRevenue: 0, // 线下用户数
      // 图表实例
      revenueChart: null,
      packageChart: null,
      // 图表数据
      chartData: {
        dateList: [],
        onlineRevenueList: [],
        offlineRevenueList: []
      },
      // 套餐统计数据
      packageData: {
        packageNames: [],
        orderCounts: []
      },
      dateRange: [],
      customRangeOnlineRevenue: 0,
      customRangeOfflineRevenue: 0,
      customRangeOnlineOrders: 0,
      customRangeOfflineOrders: 0
    }
  },
  mounted() {
    this.getChannelStatisticsData();
    this.getChannelRevenueTrendData();
    this.getPackageOrderStatisticsData();
    window.addEventListener('resize', this.resizeChart);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resizeChart);
    if (this.revenueChart) {
      this.revenueChart.dispose();
    }
    if (this.packageChart) {
      this.packageChart.dispose();
    }
  },
  methods: {
    // 获取按渠道统计数据
    getChannelStatisticsData() {
      getDashboardChannelStatistics().then(response => {
        const data = response.data;
        this.userCount = data.userCount;
        this.todayOnlineOrderCount = data.todayOnlineOrderCount; // 本月线下用户数
        this.todayOfflineOrderCount = data.todayOfflineOrderCount; // 本月线上用户数
        this.todayOnlineRevenue = parseFloat(data.todayOnlineRevenue);
        this.todayOfflineRevenue = parseFloat(data.todayOfflineRevenue); // 本月线上营业额
        this.totalOnlineRevenue = parseFloat(data.totalOnlineRevenue);
        this.totalOfflineRevenue = data.totalOfflineRevenue; // 线下用户数（不需要parseFloat，因为是整数）
      });
    },
    // 获取按渠道营业额趋势数据
    getChannelRevenueTrendData() {
      getChannelRevenueTrend().then(response => {
        const data = response.data;
        this.chartData.dateList = data.dateList;
        this.chartData.onlineRevenueList = data.onlineRevenueList.map(item => parseFloat(item));
        this.chartData.offlineRevenueList = data.offlineRevenueList.map(item => parseFloat(item));
        this.$nextTick(() => {
          this.initRevenueChart();
        });
      });
    },

    // 获取套餐订单统计数据
    getPackageOrderStatisticsData() {
      getPackageOrderStatistics().then(response => {
        const data = response.data;
        this.packageData.packageNames = data.packageNames;
        this.packageData.orderCounts = data.orderCounts;
        this.$nextTick(() => {
          this.initPackageChart();
        });
      });
    },
    // 初始化营业额趋势图表
    initRevenueChart() {
      this.revenueChart = echarts.init(this.$refs.revenueChart);
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            let result = params[0].name + '<br/>';
            params.forEach(function(item) {
              result += item.marker + item.seriesName + ': ￥' + item.value.toFixed(2) + '<br/>';
            });
            return result;
          }
        },
        legend: {
          data: ['线上营业额（微信支付）', '线下营业额（后台新增）'],
          top: 10
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '15%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            data: this.chartData.dateList,
            axisTick: {
              alignWithLabel: true
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            axisLabel: {
              formatter: '￥{value}'
            }
          }
        ],
        series: [
          {
            name: '线上营业额（微信支付）',
            type: 'bar',
            barWidth: '40%',
            data: this.chartData.onlineRevenueList,
            itemStyle: {
              color: '#409EFF'
            }
          },
          {
            name: '线下营业额（后台新增）',
            type: 'bar',
            barWidth: '40%',
            data: this.chartData.offlineRevenueList,
            itemStyle: {
              color: '#67C23A'
            }
          }
        ]
      };
      this.revenueChart.setOption(option);
    },

    // 初始化套餐订单统计图表
    initPackageChart() {
      this.packageChart = echarts.init(this.$refs.packageChart);
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            return params[0].name + '<br/>' +
                   params[0].marker + '订单数量: ' + params[0].value + '单';
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '10%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            data: this.packageData.packageNames,
            axisTick: {
              alignWithLabel: true
            },
            axisLabel: {
              interval: 0,
              rotate: 0
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            axisLabel: {
              formatter: '{value}单'
            }
          }
        ],
        series: [
          {
            name: '订单数量',
            type: 'bar',
            barWidth: '60%',
            data: this.packageData.orderCounts,
            itemStyle: {
              color: function(params) {
                // 为不同套餐设置不同颜色
                const colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666'];
                return colors[params.dataIndex % colors.length];
              }
            },
            label: {
              show: true,
              position: 'top',
              formatter: '{c}单'
            }
          }
        ]
      };
      this.packageChart.setOption(option);
    },
    // 调整图表大小
    resizeChart() {
      if (this.revenueChart) {
        this.revenueChart.resize();
      }
      if (this.packageChart) {
        this.packageChart.resize();
      }
    },
    goTarget(href) {
      window.open(href, "_blank")
    },
    handleDateRangeChange(value) {
      if (value) {
        this.dateRange = value;
      } else {
        this.dateRange = [];
      }
    },
    getCustomRangeRevenue() {
      if (this.dateRange && this.dateRange.length === 2) {
        const startDate = this.dateRange[0];
        const endDate = this.dateRange[1];
        getCustomRangeChannelRevenue(startDate, endDate).then(response => {
          const data = response.data;
          console.log("Channel revenue data:", data);

          // 设置线上数据
          this.customRangeOnlineRevenue = parseFloat(data.onlineRevenue);
          this.customRangeOnlineOrders = data.onlineOrderCount;

          // 设置线下数据
          this.customRangeOfflineRevenue = parseFloat(data.offlineRevenue);
          this.customRangeOfflineOrders = data.offlineOrderCount;

          console.log(`Online Revenue: ${this.customRangeOnlineRevenue.toFixed(2)}, Orders: ${this.customRangeOnlineOrders}`);
          console.log(`Offline Revenue: ${this.customRangeOfflineRevenue.toFixed(2)}, Orders: ${this.customRangeOfflineOrders}`);
        });
      } else {
        this.$message({
          message: '请选择日期范围',
          type: 'warning'
        });
      }
    }
  }
}
</script>

<style scoped lang="scss">
.home {
  blockquote {
    padding: 10px 20px;
    margin: 0 0 20px;
    font-size: 17.5px;
    border-left: 5px solid #eee;
  }
  hr {
    margin-top: 20px;
    margin-bottom: 20px;
    border: 0;
    border-top: 1px solid #eee;
  }
  .col-item {
    margin-bottom: 20px;
  }

  ul {
    padding: 0;
    margin: 0;
  }

  font-family: "open sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 13px;
  color: #676a6c;
  overflow-x: hidden;

  ul {
    list-style-type: none;
  }

  h4 {
    margin-top: 0px;
  }

  h2 {
    margin-top: 10px;
    font-size: 26px;
    font-weight: 100;
  }

  p {
    margin-top: 10px;

    b {
      font-weight: 700;
    }
  }

  .update-log {
    ol {
      display: block;
      list-style-type: decimal;
      margin-block-start: 1em;
      margin-block-end: 1em;
      margin-inline-start: 0;
      margin-inline-end: 0;
      padding-inline-start: 40px;
    }
  }

  .card-panel {
    height: 108px;
    cursor: pointer;
    font-size: 12px;
    position: relative;
    overflow: hidden;
    color: #666;
    background: #fff;
    box-shadow: 4px 4px 40px rgba(0, 0, 0, .05);
    border-color: rgba(0, 0, 0, .05);
    display: flex;
    align-items: center;

    &:hover {
      .card-panel-icon-wrapper {
        background: #f2f6fc;
      }
    }

    .card-panel-icon-wrapper {
      float: left;
      margin: 14px 0 0 14px;
      padding: 16px;
      transition: all 0.38s ease-out;
      border-radius: 6px;
    }

    .card-panel-icon {
      float: left;
      font-size: 48px;
    }

    .card-panel-description {
      float: right;
      font-weight: bold;
      margin: 26px 20px 0 0;

      .card-panel-text {
        line-height: 18px;
        color: rgba(0, 0, 0, 0.45);
        font-size: 16px;
        margin-bottom: 12px;
      }

      .card-panel-num {
        font-size: 20px;
      }
    }
  }

  .chart-wrapper {
    background: #fff;
    padding: 16px 16px 0;
    margin-bottom: 32px;
  }

  .revenue-result {
    display: flex;
    flex-wrap: wrap;
    padding: 20px;

    .revenue-label {
      width: 180px;
      font-size: 16px;
      line-height: 32px;
      color: rgba(0, 0, 0, 0.65);
    }

    .revenue-value {
      width: calc(50% - 180px);
      font-size: 20px;
      font-weight: bold;
      line-height: 32px;
      color: #409EFF;

      .revenue-number {
        font-size: 24px;
        font-weight: bold;
      }
    }
  }
}
</style>

