<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.ExamPackageQuestionMapper">
    
    <resultMap type="ExamPackageQuestion" id="ExamPackageQuestionResult">
        <id     property="id"               column="id"              />
        <result property="packageId"        column="package_id"      />
        <result property="questionId"       column="question_id"     />
        <result property="questionType"     column="question_type"   />
    </resultMap>

    <sql id="selectExamPackageQuestionVo">
        select id, package_id, question_id, question_type from exam_package_question
    </sql>

    <select id="selectExamPackageQuestionList" parameterType="ExamPackageQuestion" resultMap="ExamPackageQuestionResult">
        <include refid="selectExamPackageQuestionVo"/>
        <where>  
            <if test="packageId != null "> and package_id = #{packageId}</if>
            <if test="questionId != null "> and question_id = #{questionId}</if>
            <if test="questionType != null  and questionType != ''"> and question_type = #{questionType}</if>
        </where>
    </select>
    
    <select id="selectExamPackageQuestionById" parameterType="Long" resultMap="ExamPackageQuestionResult">
        <include refid="selectExamPackageQuestionVo"/>
        where id = #{id}
    </select>

    <select id="selectByPackageIdAndQuestionId" resultMap="ExamPackageQuestionResult">
        <include refid="selectExamPackageQuestionVo"/>
        where package_id = #{packageId} and question_id = #{questionId}
        limit 1
    </select>
        
    <insert id="insertExamPackageQuestion" parameterType="ExamPackageQuestion" useGeneratedKeys="true" keyProperty="id">
        insert into exam_package_question
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="packageId != null">package_id,</if>
            <if test="questionId != null">question_id,</if>
            <if test="questionType != null">question_type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="packageId != null">#{packageId},</if>
            <if test="questionId != null">#{questionId},</if>
            <if test="questionType != null">#{questionType},</if>
         </trim>
    </insert>
    
    <insert id="batchInsertExamPackageQuestion" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert ignore into exam_package_question (package_id, question_id, question_type)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.packageId}, #{item.questionId}, #{item.questionType})
        </foreach>
    </insert>

    <update id="updateExamPackageQuestion" parameterType="ExamPackageQuestion">
        update exam_package_question
        <trim prefix="SET" suffixOverrides=",">
            <if test="packageId != null">package_id = #{packageId},</if>
            <if test="questionId != null">question_id = #{questionId},</if>
            <if test="questionType != null">question_type = #{questionType},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteExamPackageQuestionById" parameterType="Long">
        delete from exam_package_question where id = #{id}
    </delete>

    <delete id="deleteExamPackageQuestionByIds" parameterType="String">
        delete from exam_package_question where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <delete id="deleteExamPackageQuestionByPackageId" parameterType="Long">
        delete from exam_package_question where package_id = #{packageId}
    </delete>
    
    <delete id="deleteExamPackageQuestionByQuestionId" parameterType="Long">
        delete from exam_package_question where question_id = #{questionId}
    </delete>
    
    <delete id="deleteExamPackageQuestionByPackageIdAndQuestionId">
        delete from exam_package_question 
        where package_id = #{0} and question_id = #{1}
    </delete>
</mapper> 