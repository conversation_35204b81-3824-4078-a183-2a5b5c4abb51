<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.ExamRecordMapper">
    
    <resultMap type="ExamRecord" id="ExamRecordResult">
        <id     property="recordId"       column="record_id"        />
        <result property="userId"         column="user_id"          />
        <result property="packageId"      column="package_id"       />
        <result property="examStartTime"  column="exam_start_time"  />
        <result property="examEndTime"    column="exam_end_time"    />
        <result property="score"          column="score"            />
        <result property="correctCount"   column="correct_count"    />
        <result property="wrongCount"     column="wrong_count"      />
        <result property="emptyCount"     column="empty_count"      />
        <result property="status"         column="status"           />
        <result property="state"          column="state"            />
        <result property="finishIds"     column="finish_ids"      />
        <result property="finishAnswer"       column="finish_answer"        />
        <result property="forgetIds"      column="forget_ids"       />
        <result property="overTime"      column="over_time"       />
        <result property="createTime"     column="create_time"      />
        <result property="updateTime"     column="update_time"      />
        <result property="remark"         column="remark"           />
        <association property="user" javaType="WxUser">
            <id     property="userId"     column="user_id"          />
            <result property="nickName"   column="nick_name"        />
            <result property="avatarUrl"  column="avatar_url"       />
            <result property="phone"      column="phone"            />
        </association>
        <association property="examPackage" javaType="ExamPackage">
            <id     property="packageId"  column="package_id"       />
            <result property="packageName" column="package_name"    />
        </association>
    </resultMap>

    <sql id="selectExamRecordVo">
        select r.record_id, r.user_id, r.package_id, r.exam_start_time, r.exam_end_time, r.score,
        r.correct_count, r.wrong_count, r.empty_count, r.status, r.state, r.finish_ids, r.finish_answer, r.forget_ids, r.over_time,
        r.create_time, r.update_time, r.remark,
        u.nick_name, u.avatar_url, u.phone, p.package_name
        from exam_record r
        left join wx_user u on r.user_id = u.user_id
        left join exam_package p on r.package_id = p.package_id
    </sql>

    <select id="selectExamRecordList" parameterType="ExamRecord" resultMap="ExamRecordResult">
        <include refid="selectExamRecordVo"/>
        <where>
            <if test="userId != null "> and r.user_id = #{userId}</if>
            <if test="userName != null  and userName != ''"> and u.nick_name like concat('%', #{userName}, '%')</if>
            <if test="phone != null  and phone != ''"> and u.phone like concat('%', #{phone}, '%')</if>
            <if test="packageId != null "> and r.package_id = #{packageId}</if>
            <if test="packageName != null  and packageName != ''"> and p.package_name like concat('%', #{packageName}, '%')</if>
            <if test="examStartTime != null "> and r.exam_start_time = #{examStartTime}</if>
            <if test="examEndTime != null "> and r.exam_end_time = #{examEndTime}</if>
            <if test="score != null "> and r.score = #{score}</if>
            <if test="status != null  and status != ''"> and r.status = #{status}</if>
            <if test="state != null  and state != ''"> and r.state = #{state}</if>
            <if test="params.beginExamStartTime != null and params.beginExamStartTime != '' and params.endExamStartTime != null and params.endExamStartTime != ''"> and r.exam_start_time between #{params.beginExamStartTime} and #{params.endExamStartTime}</if>
            <if test="params.beginExamEndTime != null and params.beginExamEndTime != '' and params.endExamEndTime != null and params.endExamEndTime != ''"> and r.exam_end_time between #{params.beginExamEndTime} and #{params.endExamEndTime}</if>
            and r.status = '0'
        </where>
        order by r.create_time desc
    </select>
    
    <select id="selectExamRecordByRecordId" parameterType="Long" resultMap="ExamRecordResult">
        <include refid="selectExamRecordVo"/>
        where r.record_id = #{recordId}
    </select>
    
    <select id="selectExamRecordByUserId" parameterType="Long" resultMap="ExamRecordResult">
        <include refid="selectExamRecordVo"/>
        where r.user_id = #{userId} and r.status = '0'
        order by r.create_time desc
    </select>
        
    <insert id="insertExamRecord" parameterType="ExamRecord" useGeneratedKeys="true" keyProperty="recordId">
        insert into exam_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="packageId != null">package_id,</if>
            <if test="examStartTime != null">exam_start_time,</if>
            <if test="examEndTime != null">exam_end_time,</if>
            <if test="score != null">score,</if>
            <if test="correctCount != null">correct_count,</if>
            <if test="wrongCount != null">wrong_count,</if>
            <if test="emptyCount != null">empty_count,</if>
            <if test="status != null">status,</if>
            <if test="state != null">state,</if>
            <if test="finishIds != null">finish_ids,</if>
            <if test="finishAnswer != null">finish_answer,</if>
            <if test="forgetIds != null">forget_ids,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="overTime != null">over_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="packageId != null">#{packageId},</if>
            <if test="examStartTime != null">#{examStartTime},</if>
            <if test="examEndTime != null">#{examEndTime},</if>
            <if test="score != null">#{score},</if>
            <if test="correctCount != null">#{correctCount},</if>
            <if test="wrongCount != null">#{wrongCount},</if>
            <if test="emptyCount != null">#{emptyCount},</if>
            <if test="status != null">#{status},</if>
            <if test="state != null">#{state},</if>
            <if test="finishIds != null">#{finishIds},</if>
            <if test="finishAnswer != null">#{finishAnswer},</if>
            <if test="forgetIds != null">#{forgetIds},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="overTime != null">#{overTime},</if>
         </trim>
    </insert>

    <update id="updateExamRecord" parameterType="ExamRecord">
        update exam_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="packageId != null">package_id = #{packageId},</if>
            <if test="examStartTime != null">exam_start_time = #{examStartTime},</if>
            <if test="examEndTime != null">exam_end_time = #{examEndTime},</if>
            <if test="score != null">score = #{score},</if>
            <if test="correctCount != null">correct_count = #{correctCount},</if>
            <if test="wrongCount != null">wrong_count = #{wrongCount},</if>
            <if test="emptyCount != null">empty_count = #{emptyCount},</if>
            <if test="status != null">status = #{status},</if>
            <if test="state != null">state = #{state},</if>
            <if test="finishIds != null">finish_ids = #{finishIds},</if>
            <if test="finishAnswer != null">finish_answer = #{finishAnswer},</if>
            <if test="forgetIds != null">forget_ids = #{forgetIds},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="overTime != null">over_time = #{overTime},</if>
        </trim>
        where record_id = #{recordId}
    </update>

    <delete id="deleteExamRecordByRecordId" parameterType="Long">
        update exam_record set status = '1' where record_id = #{recordId}
    </delete>

    <delete id="deleteExamRecordByRecordIds" parameterType="String">
        update exam_record set status = '1' where record_id in 
        <foreach item="recordId" collection="array" open="(" separator="," close=")">
            #{recordId}
        </foreach>
    </delete>
</mapper> 