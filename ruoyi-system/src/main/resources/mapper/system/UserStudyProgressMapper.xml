<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.UserStudyProgressMapper">
    
    <resultMap type="UserStudyProgress" id="UserStudyProgressResult">
        <id     property="progressId"           column="progress_id"        />
        <result property="packageId"            column="package_id"         />
        <result property="userId"               column="user_id"            />
        <result property="sortNum"              column="sort_num"           />
        <result property="packageName"          column="package_name"       />
        <result property="nickName"             column="nick_name"          />
        <result property="currentMaterialName"  column="current_material_name" />
        <result property="createBy"             column="create_by"          />
        <result property="createTime"           column="create_time"        />
        <result property="updateBy"             column="update_by"          />
        <result property="updateTime"           column="update_time"        />
        <result property="remark"               column="remark"             />
    </resultMap>

    <sql id="selectUserStudyProgressVo">
        select p.progress_id, p.package_id, p.user_id, p.sort_num, 
               pkg.package_name, u.nick_name,
               sm.material_name as current_material_name,
               p.create_by, p.create_time, p.update_by, p.update_time
        from user_study_progress p
        left join exam_package pkg on p.package_id = pkg.package_id
        left join wx_user u on p.user_id = u.user_id
        left join study_material sm on p.package_id = sm.package_id and p.sort_num = sm.sort_num
    </sql>

    <select id="selectUserStudyProgressList" parameterType="UserStudyProgress" resultMap="UserStudyProgressResult">
        <include refid="selectUserStudyProgressVo"/>
        <where>  
            <if test="packageId != null "> and p.package_id = #{packageId}</if>
            <if test="userId != null "> and p.user_id = #{userId}</if>
            <if test="sortNum != null "> and p.sort_num = #{sortNum}</if>
        </where>
        order by p.update_time desc
    </select>
    
    <select id="selectUserStudyProgressByProgressId" parameterType="Long" resultMap="UserStudyProgressResult">
        <include refid="selectUserStudyProgressVo"/>
        where p.progress_id = #{progressId}
    </select>

    <select id="selectUserStudyProgressByUserAndPackage" resultMap="UserStudyProgressResult">
        <include refid="selectUserStudyProgressVo"/>
        where p.user_id = #{userId} and p.package_id = #{packageId}
    </select>

    <select id="selectUserStudyProgressListByUserId" parameterType="Long" resultMap="UserStudyProgressResult">
        <include refid="selectUserStudyProgressVo"/>
        where p.user_id = #{userId}
        order by p.update_time desc
    </select>
        
    <insert id="insertUserStudyProgress" parameterType="UserStudyProgress" useGeneratedKeys="true" keyProperty="progressId">
        insert into user_study_progress
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="packageId != null">package_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="sortNum != null">sort_num,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="packageId != null">#{packageId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="sortNum != null">#{sortNum},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateUserStudyProgress" parameterType="UserStudyProgress">
        update user_study_progress
        <trim prefix="SET" suffixOverrides=",">
            <if test="packageId != null">package_id = #{packageId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="sortNum != null">sort_num = #{sortNum},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where progress_id = #{progressId}
    </update>

    <delete id="deleteUserStudyProgressByProgressId" parameterType="Long">
        delete from user_study_progress where progress_id = #{progressId}
    </delete>

    <delete id="deleteUserStudyProgressByProgressIds" parameterType="Long">
        delete from user_study_progress where progress_id in 
        <foreach item="progressId" collection="array" open="(" separator="," close=")">
            #{progressId}
        </foreach>
    </delete>
    
</mapper>
