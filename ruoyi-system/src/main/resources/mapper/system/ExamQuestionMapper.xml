<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.ExamQuestionMapper">
    
    <resultMap type="ExamQuestion" id="ExamQuestionResult">
        <id     property="questionId"       column="question_id"     />
        <result property="subject"          column="subject"         />
        <result property="questionType"     column="question_type"   />
        <result property="questionContent"  column="question_content"/>
        <result property="optionA"          column="option_a"        />
        <result property="optionB"          column="option_b"        />
        <result property="optionC"          column="option_c"        />
        <result property="optionD"          column="option_d"        />
        <result property="optionE"          column="option_e"        />
        <result property="optionF"          column="option_f"        />
        <result property="correctAnswer"    column="correct_answer"  />
        <result property="analysis"         column="analysis"        />
        <result property="status"           column="status"          />
        <result property="delFlag"          column="del_flag"        />
        <result property="createBy"         column="create_by"       />
        <result property="createTime"       column="create_time"     />
        <result property="updateBy"         column="update_by"       />
        <result property="updateTime"       column="update_time"     />
        <result property="remark"           column="remark"          />
        <result property="categoryId"       column="category_id" />
        <result property="isMock"           column="is_mock"   />
        <result property="isStudy"           column="is_study"   />
    </resultMap>

    <sql id="selectExamQuestionVo">
        select question_id, subject, question_type, question_content, option_a, option_b, option_c, option_d,option_e, option_f, correct_answer, analysis, status, del_flag, create_by, create_time, update_by, update_time, remark,category_id,is_mock,is_study from exam_question
    </sql>

    <select id="selectExamQuestionList" parameterType="ExamQuestion" resultMap="ExamQuestionResult">
        <include refid="selectExamQuestionVo"/>
        <where>  
            <if test="subject != null and subject != ''"> and subject like concat('%', #{subject}, '%')</if>
            <if test="questionType != null  and questionType != ''"> and question_type = #{questionType}</if>
            <if test="questionContent != null  and questionContent != ''"> and question_content like concat('%', #{questionContent}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            and del_flag = '0'
        </where>
    </select>
    
    <select id="selectExamQuestionByQuestionId" parameterType="Long" resultMap="ExamQuestionResult">
        <include refid="selectExamQuestionVo"/>
        where question_id = #{questionId}
    </select>

    <select id="selectExamQuestionByContent" parameterType="String" resultMap="ExamQuestionResult">
        <include refid="selectExamQuestionVo"/>
        where question_content = #{questionContent} and del_flag = '0'
        limit 1
    </select>
    
    <select id="selectExamQuestionsByPackageId" resultMap="ExamQuestionResult">
        select q.question_id, q.subject, q.question_type, q.question_content, q.option_a, q.option_b, q.option_c, q.option_d,q.option_e, q.option_f, q.correct_answer, q.analysis, q.status, q.del_flag, q.create_by, q.create_time, q.update_by, q.update_time, q.remark, q.is_mock, q.is_study
        from exam_question q
        inner join exam_package_question pq on q.question_id = pq.question_id
        where pq.package_id = #{0} and pq.question_type = #{1} and q.del_flag = '0'
    </select>
        
    <insert id="insertExamQuestion" parameterType="ExamQuestion" useGeneratedKeys="true" keyProperty="questionId">
        insert into exam_question
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="subject != null and subject != ''">subject,</if>
            <if test="questionType != null and questionType != ''">question_type,</if>
            <if test="questionContent != null and questionContent != ''">question_content,</if>
            <if test="optionA != null">option_a,</if>
            <if test="optionB != null">option_b,</if>
            <if test="optionC != null">option_c,</if>
            <if test="optionD != null">option_d,</if>
            <if test="optionE != null">option_e,</if>
            <if test="optionF != null">option_f,</if>
            <if test="correctAnswer != null and correctAnswer != ''">correct_answer,</if>
            <if test="analysis != null">analysis,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="isMock != null">is_mock,</if>
            <if test="isStudy != null">is_study,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="subject != null and subject != ''">#{subject},</if>
            <if test="questionType != null and questionType != ''">#{questionType},</if>
            <if test="questionContent != null and questionContent != ''">#{questionContent},</if>
            <if test="optionA != null">#{optionA},</if>
            <if test="optionB != null">#{optionB},</if>
            <if test="optionC != null">#{optionC},</if>
            <if test="optionD != null">#{optionD},</if>
            <if test="optionE != null">#{optionE},</if>
            <if test="optionF != null">#{optionF},</if>
            <if test="correctAnswer != null and correctAnswer != ''">#{correctAnswer},</if>
            <if test="analysis != null">#{analysis},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="isMock != null">#{isMock},</if>
            <if test="isStudy != null">#{isStudy},</if>
         </trim>
    </insert>

    <insert id="batchInsertExamQuestion" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="questionId">
        insert into exam_question (subject, question_type, question_content, option_a, option_b, option_c, option_d,  option_e, option_f, correct_answer, analysis, status, del_flag, create_by, create_time, update_by, update_time, remark, category_id,is_mock,is_study)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.subject}, #{item.questionType}, #{item.questionContent}, #{item.optionA}, #{item.optionB}, #{item.optionC}, #{item.optionD},  #{item.optionE}, #{item.optionF}, #{item.correctAnswer}, #{item.analysis},
             #{item.status}, #{item.delFlag}, #{item.createBy}, sysdate(), #{item.updateBy}, sysdate(), #{item.remark}, #{item.categoryId}, #{item.isMock}, #{item.isStudy})
        </foreach>
    </insert>

    <select id="getSubjectsByPackageId" resultType="string">
        select subject from exam_question where category_id = #{packageId} GROUP BY `subject`
    </select>

    <update id="updateExamQuestion" parameterType="ExamQuestion">
        update exam_question
        <trim prefix="SET" suffixOverrides=",">
            <if test="subject != null and subject != ''">subject = #{subject},</if>
            <if test="questionType != null and questionType != ''">question_type = #{questionType},</if>
            <if test="questionContent != null and questionContent != ''">question_content = #{questionContent},</if>
            <if test="optionA != null">option_a = #{optionA},</if>
            <if test="optionB != null">option_b = #{optionB},</if>
            <if test="optionC != null">option_c = #{optionC},</if>
            <if test="optionD != null">option_d = #{optionD},</if>
            <if test="optionE != null">option_e = #{optionE},</if>
            <if test="optionF != null">option_f = #{optionF},</if>
            <if test="correctAnswer != null and correctAnswer != ''">correct_answer = #{correctAnswer},</if>
            <if test="analysis != null">analysis = #{analysis},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="isMock != null">is_mock = #{isMock},</if>
            <if test="isStudy != null">is_study = #{isStudy},</if>
        </trim>
        where question_id = #{questionId}
    </update>

    <delete id="deleteExamQuestionByQuestionId" parameterType="Long">
        update exam_question set del_flag = '2' where question_id = #{questionId}
    </delete>

    <delete id="deleteExamQuestionByQuestionIds" parameterType="String">
        update exam_question set del_flag = '2' where question_id in 
        <foreach item="questionId" collection="array" open="(" separator="," close=")">
            #{questionId}
        </foreach>
    </delete>
</mapper> 