<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.StudyRecordMapper">
    
    <resultMap type="StudyRecord" id="StudyRecordResult">
        <id     property="recordId"      column="record_id"      />
        <result property="userId"        column="user_id"        />
        <result property="nickName"      column="nick_name"      />
        <result property="phone"         column="phone"          />
        <result property="materialId"    column="material_id"    />
        <result property="materialName"  column="material_name"  />
        <result property="packageId"     column="package_id"     />
        <result property="packageName"   column="package_name"   />
        <result property="materialType"  column="material_type"  />
        <result property="progress"      column="progress"       />
        <result property="lastPosition"  column="last_position"  />
        <result property="studyTime"     column="study_time"     />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectStudyRecordVo">
        select r.record_id, r.user_id, w.nick_name, w.phone, r.material_id, m.material_name,
               r.package_id, p.package_name, r.material_type, r.progress, r.last_position,
               r.study_time, r.create_time, r.update_time
        from study_record r
        left join wx_user w on r.user_id = w.user_id
        left join study_material m on r.material_id = m.material_id
        left join exam_package p on r.package_id = p.package_id
    </sql>
    
    <sql id="selectStudyRecordSimpleVo">
        select record_id, user_id, material_id, package_id, material_type, 
               progress, last_position, study_time, create_time, update_time
        from study_record
    </sql>

    <select id="selectStudyRecordList" parameterType="StudyRecord" resultMap="StudyRecordResult">
        <include refid="selectStudyRecordVo"/>
        <where>
            <if test="userId != null "> and r.user_id = #{userId}</if>
            <if test="nickName != null  and nickName != ''"> and w.nick_name like concat('%', #{nickName}, '%')</if>
            <if test="phone != null  and phone != ''"> and w.phone like concat('%', #{phone}, '%')</if>
            <if test="materialId != null "> and r.material_id = #{materialId}</if>
            <if test="materialName != null  and materialName != ''"> and m.material_name like concat('%', #{materialName}, '%')</if>
            <if test="packageId != null "> and r.package_id = #{packageId}</if>
            <if test="packageName != null  and packageName != ''"> and p.package_name like concat('%', #{packageName}, '%')</if>
            <if test="materialType != null  and materialType != ''"> and r.material_type = #{materialType}</if>
        </where>
        order by r.update_time desc
    </select>
    
    <select id="selectStudyRecordByRecordId" parameterType="Long" resultMap="StudyRecordResult">
        <include refid="selectStudyRecordVo"/>
        where r.record_id = #{recordId}
    </select>
    
    <select id="selectStudyRecordByUserIdAndMaterialId" resultMap="StudyRecordResult">
        <include refid="selectStudyRecordVo"/>
        where r.user_id = #{userId} and r.material_id = #{materialId}
    </select>
    
    <select id="selectStudyRecordListByUserId" parameterType="Long" resultMap="StudyRecordResult">
        <include refid="selectStudyRecordVo"/>
        where r.user_id = #{userId}
        order by r.update_time desc
    </select>
    
    <select id="selectStudyRecordListByPackageId" resultMap="StudyRecordResult">
        <include refid="selectStudyRecordVo"/>
        where r.user_id = #{userId} and r.package_id = #{packageId}
        order by r.update_time desc
    </select>
    
    <select id="selectStudyRecordListByMaterialId" parameterType="Long" resultMap="StudyRecordResult">
        <include refid="selectStudyRecordVo"/>
        where r.material_id = #{materialId}
        order by r.update_time desc
    </select>
    
    <insert id="insertStudyRecord" parameterType="StudyRecord" useGeneratedKeys="true" keyProperty="recordId">
        insert into study_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="materialId != null">material_id,</if>
            <if test="packageId != null">package_id,</if>
            <if test="materialType != null">material_type,</if>
            <if test="progress != null">progress,</if>
            <if test="lastPosition != null">last_position,</if>
            <if test="studyTime != null">study_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="materialId != null">#{materialId},</if>
            <if test="packageId != null">#{packageId},</if>
            <if test="materialType != null">#{materialType},</if>
            <if test="progress != null">#{progress},</if>
            <if test="lastPosition != null">#{lastPosition},</if>
            <if test="studyTime != null">#{studyTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>
    
    <update id="updateStudyRecord" parameterType="StudyRecord">
        update study_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="progress != null">progress = #{progress},</if>
            <if test="lastPosition != null">last_position = #{lastPosition},</if>
            <if test="studyTime != null">study_time = #{studyTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where record_id = #{recordId}
    </update>
    
    <update id="updateStudyProgress" parameterType="StudyRecord">
        update study_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="progress != null">progress = #{progress},</if>
            <if test="lastPosition != null">last_position = #{lastPosition},</if>
            <if test="studyTime != null">study_time = study_time + #{studyTime},</if>
            update_time = sysdate()
        </trim>
        where user_id = #{userId} and material_id = #{materialId}
    </update>
    
    <delete id="deleteStudyRecordByRecordId" parameterType="Long">
        delete from study_record where record_id = #{recordId}
    </delete>
    
    <delete id="deleteStudyRecordByRecordIds" parameterType="Long">
        delete from study_record where record_id in 
        <foreach item="recordId" collection="array" open="(" separator="," close=")">
            #{recordId}
        </foreach>
    </delete>
    
    <delete id="deleteStudyRecordByUserId" parameterType="Long">
        delete from study_record where user_id = #{userId}
    </delete>
    
    <delete id="deleteStudyRecordByMaterialId" parameterType="Long">
        delete from study_record where material_id = #{materialId}
    </delete>
    
</mapper> 