<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.ExamPackageMapper">
    
    <resultMap type="ExamPackage" id="ExamPackageResult">
        <id     property="packageId"        column="package_id"      />
        <result property="packageName"      column="package_name"    />
        <result property="categoryId"       column="category_id"     />
        <result property="introduction"     column="introduction"    />
        <result property="price"            column="price"           />
        <result property="validityDays"     column="validity_days"   />
        <result property="status"           column="status"          />
        <result property="delFlag"          column="del_flag"        />
        <result property="createBy"         column="create_by"       />
        <result property="createTime"       column="create_time"     />
        <result property="updateBy"         column="update_by"       />
        <result property="updateTime"       column="update_time"     />
        <result property="remark"           column="remark"          />
        <result property="categoryName"     column="category_name"   />
        <result property="coverImage"     column="cover_image"   />

    </resultMap>

    <sql id="selectExamPackageVo">
        select p.package_id,p.cover_image, p.package_name, p.category_id, p.introduction, p.price, p.validity_days, p.status, p.del_flag, p.create_by, p.create_time, p.update_by, p.update_time, p.remark, c.category_name
        from exam_package p
        left join exam_category c on p.category_id = c.category_id
    </sql>

    <select id="selectExamPackageList" parameterType="ExamPackage" resultMap="ExamPackageResult">
        <include refid="selectExamPackageVo"/>
        <where>  
            <if test="packageName != null  and packageName != ''"> and p.package_name like concat('%', #{packageName}, '%')</if>
            <if test="categoryId != null "> and p.category_id = #{categoryId}</if>
            <if test="status != null  and status != ''"> and p.status = #{status}</if>
            <if test="categoryName != null  and categoryName != ''"> and c.category_name like concat('%', #{categoryName}, '%')</if>
            and p.del_flag = '0'
        </where>
    </select>
    
    <select id="selectExamPackageAll" resultMap="ExamPackageResult">
        <include refid="selectExamPackageVo"/>
        where p.status = '0' and p.del_flag = '0'
    </select>
    
    <select id="selectExamPackageByPackageId" parameterType="Long" resultMap="ExamPackageResult">
        <include refid="selectExamPackageVo"/>
        where p.package_id = #{packageId}
    </select>
        
    <insert id="insertExamPackage" parameterType="ExamPackage" useGeneratedKeys="true" keyProperty="packageId">
        insert into exam_package
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="packageName != null and packageName != ''">package_name,</if>
            <if test="categoryId != null">category_id,</if>
            <if test="introduction != null">introduction,</if>
            <if test="price != null">price,</if>
            <if test="validityDays != null">validity_days,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="coverImage != null">cover_image,</if>

         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="packageName != null and packageName != ''">#{packageName},</if>
            <if test="categoryId != null">#{categoryId},</if>
            <if test="introduction != null">#{introduction},</if>
            <if test="price != null">#{price},</if>
            <if test="validityDays != null">#{validityDays},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="coverImage != null">#{coverImage},</if>
        </trim>
    </insert>

    <update id="updateExamPackage" parameterType="ExamPackage">
        update exam_package
        <trim prefix="SET" suffixOverrides=",">
            <if test="packageName != null and packageName != ''">package_name = #{packageName},</if>
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="introduction != null">introduction = #{introduction},</if>
            <if test="price != null">price = #{price},</if>
            <if test="validityDays != null">validity_days = #{validityDays},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="coverImage != null">cover_image = #{coverImage},</if>
        </trim>
        where package_id = #{packageId}
    </update>

    <delete id="deleteExamPackageByPackageId" parameterType="Long">
        update exam_package set del_flag = '2' where package_id = #{packageId}
    </delete>

    <delete id="deleteExamPackageByPackageIds" parameterType="String">
        update exam_package set del_flag = '2' where package_id in 
        <foreach item="packageId" collection="array" open="(" separator="," close=")">
            #{packageId}
        </foreach>
    </delete>
</mapper> 