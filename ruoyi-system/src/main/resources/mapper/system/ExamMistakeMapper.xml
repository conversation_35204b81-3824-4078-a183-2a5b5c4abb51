<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.ExamMistakeMapper">
    
    <resultMap type="ExamMistake" id="ExamMistakeResult">
        <id     property="mistakeId"      column="mistake_id"       />
        <result property="userId"         column="user_id"          />
        <result property="questionId"     column="question_id"      />
        <result property="wrongAnswer"    column="wrong_answer"     />
        <result property="mistakeCount"   column="mistake_count"    />
        <result property="lastMistakeTime" column="last_mistake_time" />
        <result property="status"         column="status"           />
        <result property="createTime"     column="create_time"      />
        <result property="updateTime"     column="update_time"      />
        <association property="user" javaType="WxUser">
            <id     property="userId"      column="user_id"         />
            <result property="nickName"    column="nick_name"       />
            <result property="phone"    column="phone"       />
            <result property="avatarUrl"    column="avatar_url"       />
        </association>
        <association property="question" javaType="ExamQuestion">
            <id     property="questionId"   column="question_id"     />
            <result property="questionType" column="question_type"   />
            <result property="questionContent" column="question_content"   />
            <result property="analysis"     column="analysis"        />
            <result property="correctAnswer"     column="correct_answer"        />
        </association>
    </resultMap>

    <sql id="selectExamMistakeVo">
        select m.mistake_id, m.user_id, m.question_id, m.wrong_answer, m.mistake_count, 
               m.last_mistake_time, m.status, m.create_time, m.update_time,
               w.phone,w.avatar_url,w.nick_name, q.question_type, q.analysis,q.question_content,q.correct_answer
        from exam_mistake m
        left join wx_user w on m.user_id = w.user_id
        left join exam_question q on m.question_id = q.question_id
    </sql>
    
    <sql id="selectExamMistakeSimpleVo">
        select mistake_id, user_id, question_id, wrong_answer, mistake_count, 
               last_mistake_time, status, create_time, update_time
        from exam_mistake
    </sql>

    <select id="selectExamMistakeList" parameterType="ExamMistake" resultMap="ExamMistakeResult">
        <include refid="selectExamMistakeVo"/>
        <where>  
            <if test="userId != null "> and m.user_id = #{userId}</if>
            <if test="questionId != null "> and m.question_id = #{questionId}</if>
            <if test="wrongAnswer != null  and wrongAnswer != ''"> and m.wrong_answer = #{wrongAnswer}</if>
            <if test="status != null  and status != ''"> and m.status = #{status}</if>
        </where>
        order by m.last_mistake_time desc
    </select>
    
    <select id="selectExamMistakeByMistakeId" parameterType="Long" resultMap="ExamMistakeResult">
        <include refid="selectExamMistakeVo"/>
        where m.mistake_id = #{mistakeId}
    </select>
    
    <select id="selectExamMistakeListByUserId" parameterType="Long" resultMap="ExamMistakeResult">
        <include refid="selectExamMistakeVo"/>
        where m.user_id = #{userId}
        order by m.last_mistake_time desc
    </select>
    
    <select id="selectExamMistakeByUserIdAndQuestionId" resultMap="ExamMistakeResult">
        <include refid="selectExamMistakeVo"/>
        where m.user_id = #{userId} and m.question_id = #{questionId}
    </select>
    
    <insert id="insertExamMistake" parameterType="ExamMistake" useGeneratedKeys="true" keyProperty="mistakeId">
        insert into exam_mistake
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="questionId != null">question_id,</if>
            <if test="wrongAnswer != null">wrong_answer,</if>
            <if test="mistakeCount != null">mistake_count,</if>
            <if test="lastMistakeTime != null">last_mistake_time,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="questionId != null">#{questionId},</if>
            <if test="wrongAnswer != null">#{wrongAnswer},</if>
            <if test="mistakeCount != null">#{mistakeCount},</if>
            <if test="lastMistakeTime != null">#{lastMistakeTime},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>
    
    <update id="updateExamMistake" parameterType="ExamMistake">
        update exam_mistake
        <trim prefix="SET" suffixOverrides=",">
            <if test="wrongAnswer != null">wrong_answer = #{wrongAnswer},</if>
            <if test="mistakeCount != null">mistake_count = #{mistakeCount},</if>
            last_mistake_time = sysdate(),
            <if test="status != null">status = #{status},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where mistake_id = #{mistakeId}
    </update>
    
    <update id="updateExamMistakeStatus" parameterType="ExamMistake">
        update exam_mistake
        <trim prefix="SET" suffixOverrides=",">
            <if test="status != null">status = #{status},</if>
            update_time = sysdate(),
        </trim>
        where mistake_id = #{mistakeId}
    </update>
    
    <delete id="deleteExamMistakeByMistakeId" parameterType="Long">
        delete from exam_mistake where mistake_id = #{mistakeId}
    </delete>
    
    <delete id="deleteExamMistakeByMistakeIds" parameterType="Long">
        delete from exam_mistake where mistake_id in 
        <foreach item="mistakeId" collection="array" open="(" separator="," close=")">
            #{mistakeId}
        </foreach>
    </delete>
    
    <delete id="deleteExamMistakeByUserId" parameterType="Long">
        delete from exam_mistake where user_id = #{userId}
    </delete>
    
</mapper> 