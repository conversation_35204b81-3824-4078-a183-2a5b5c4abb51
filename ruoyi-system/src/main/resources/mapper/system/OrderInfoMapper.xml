<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.OrderInfoMapper">
    
    <resultMap type="OrderInfo" id="OrderInfoResult">
        <id     property="orderId"       column="order_id"       />
        <result property="orderNo"       column="order_no"       />
        <result property="userId"        column="user_id"        />
        <result property="packageId"     column="package_id"     />
        <result property="packageName"   column="package_name"   />
        <result property="amount"        column="amount"         />
        <result property="status"        column="status"         />
        <result property="payTime"       column="pay_time"       />
        <result property="expireTime"    column="expire_time"    />
        <result property="payChannel"    column="pay_channel"    />
        <result property="transactionId" column="transaction_id" />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"        column="remark"         />
        <association property="user" javaType="WxUser">
            <id     property="userId"    column="user_id"        />
            <result property="nickName"  column="nick_name"      />
            <result property="phone"     column="phone"          />
            <result property="openid"    column="openid"         />
        </association>
        <association property="examPackage" javaType="ExamPackage">
            <id     property="packageId"   column="package_id"     />
            <result property="packageName" column="package_name"   />
            <result property="price"       column="price"          />
            <result property="validityDays" column="validity_days" />
        </association>
    </resultMap>

    <sql id="selectOrderInfoVo">
        select o.order_id, o.order_no, o.user_id, o.package_id, o.package_name,
               o.amount, o.status, o.pay_time, o.expire_time, o.pay_channel,
               o.transaction_id, o.create_time, o.update_time, o.remark,
               w.nick_name, w.phone, w.openid, p.price, p.validity_days
        from order_info o
        left join wx_user w on o.user_id = w.user_id
        left join exam_package p on o.package_id = p.package_id
    </sql>
    
    <sql id="selectOrderInfoSimpleVo">
        select order_id, order_no, user_id, package_id, package_name, 
               amount, status, pay_time, expire_time, pay_channel, 
               transaction_id, create_time, update_time, remark
        from order_info
    </sql>

    <select id="selectOrderInfoList" parameterType="OrderInfo" resultMap="OrderInfoResult">
        <include refid="selectOrderInfoVo"/>
        <where>
            <if test="orderNo != null  and orderNo != ''"> and o.order_no like concat('%', #{orderNo}, '%')</if>
            <if test="userId != null "> and o.user_id = #{userId}</if>
            <if test="nickName != null  and nickName != ''"> and w.nick_name like concat('%', #{nickName}, '%')</if>
            <if test="phone != null  and phone != ''"> and w.phone like concat('%', #{phone}, '%')</if>
            <if test="packageId != null "> and o.package_id = #{packageId}</if>
            <if test="packageName != null  and packageName != ''"> and o.package_name like concat('%', #{packageName}, '%')</if>
            <if test="status != null  and status != ''"> and o.status = #{status}</if>
            <if test="payChannel != null  and payChannel != ''"> and o.pay_channel = #{payChannel}</if>
            <if test="transactionId != null  and transactionId != ''"> and o.transaction_id = #{transactionId}</if>
        </where>
        order by o.create_time desc
    </select>
    
    <select id="selectOrderInfoByOrderId" parameterType="Long" resultMap="OrderInfoResult">
        <include refid="selectOrderInfoVo"/>
        where o.order_id = #{orderId}
    </select>
    
    <select id="selectOrderInfoByOrderNo" parameterType="String" resultMap="OrderInfoResult">
        <include refid="selectOrderInfoVo"/>
        where o.order_no = #{orderNo}
    </select>
    
    <select id="selectOrderInfoListByUserId" parameterType="OrderInfo" resultMap="OrderInfoResult">
        <include refid="selectOrderInfoVo"/>
        <where>
            <if test="userId != null "> and o.user_id = #{userId}</if>
            <if test="status != null  and status != ''"> and o.status = #{status}</if>
        </where>
        order by o.create_time desc
    </select>
    
    <insert id="insertOrderInfo" parameterType="OrderInfo" useGeneratedKeys="true" keyProperty="orderId">
        insert into order_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderNo != null">order_no,</if>
            <if test="userId != null">user_id,</if>
            <if test="packageId != null">package_id,</if>
            <if test="packageName != null">package_name,</if>
            <if test="amount != null">amount,</if>
            <if test="status != null">status,</if>
            <if test="payTime != null">pay_time,</if>
            <if test="expireTime != null">expire_time,</if>
            <if test="payChannel != null">pay_channel,</if>
            <if test="transactionId != null">transaction_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderNo != null">#{orderNo},</if>
            <if test="userId != null">#{userId},</if>
            <if test="packageId != null">#{packageId},</if>
            <if test="packageName != null">#{packageName},</if>
            <if test="amount != null">#{amount},</if>
            <if test="status != null">#{status},</if>
            <if test="payTime != null">#{payTime},</if>
            <if test="expireTime != null">#{expireTime},</if>
            <if test="payChannel != null">#{payChannel},</if>
            <if test="transactionId != null">#{transactionId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>
    
    <update id="updateOrderInfo" parameterType="OrderInfo">
        update order_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderNo != null">order_no = #{orderNo},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="packageId != null">package_id = #{packageId},</if>
            <if test="packageName != null">package_name = #{packageName},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="status != null">status = #{status},</if>
            <if test="payTime != null">pay_time = #{payTime},</if>
            <if test="expireTime != null">expire_time = #{expireTime},</if>
            <if test="payChannel != null">pay_channel = #{payChannel},</if>
            <if test="transactionId != null">transaction_id = #{transactionId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where order_id = #{orderId}
    </update>
    
    <update id="updateOrderStatus" parameterType="OrderInfo">
        update order_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="status != null">status = #{status},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where order_id = #{orderId}
    </update>
    
    <update id="payOrder" parameterType="OrderInfo">
        update order_info
        <trim prefix="SET" suffixOverrides=",">
            status = '1',
            <if test="payTime != null">pay_time = #{payTime},</if>
            <if test="expireTime != null">expire_time = #{expireTime},</if>
            <if test="payChannel != null">pay_channel = #{payChannel},</if>
            <if test="transactionId != null">transaction_id = #{transactionId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where order_no = #{orderNo} and status = '0'
    </update>
    
    <delete id="deleteOrderInfoByOrderId" parameterType="Long">
        delete from order_info where order_id = #{orderId}
    </delete>
    
    <delete id="deleteOrderInfoByOrderIds" parameterType="Long">
        delete from order_info where order_id in 
        <foreach item="orderId" collection="array" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </delete>
    
    <select id="countTodayOrders" parameterType="String" resultType="int">
        select count(1) from order_info 
        where status = '1' 
        and DATE_FORMAT(pay_time, '%Y-%m-%d') = #{dateStr}
    </select>
    
    <select id="sumTodayRevenue" parameterType="String" resultType="java.math.BigDecimal">
        select COALESCE(sum(amount), 0) from order_info 
        where status = '1' 
        and DATE_FORMAT(pay_time, '%Y-%m-%d') = #{dateStr}
    </select>
    
    <select id="sumTotalRevenue" resultType="java.math.BigDecimal">
        select COALESCE(sum(amount), 0) from order_info 
        where status = '1'
    </select>
    
    <select id="sumDayRevenue" parameterType="String" resultType="java.math.BigDecimal">
        select COALESCE(sum(amount), 0) from order_info 
        where status = '1' 
        and DATE_FORMAT(pay_time, '%Y-%m-%d') = #{dateStr}
    </select>
    
    <select id="sumCustomRangeRevenue" resultType="java.math.BigDecimal">
        select COALESCE(sum(amount), 0) from order_info 
        where status = '1' 
        and DATE_FORMAT(pay_time, '%Y-%m-%d') >= #{startDate}
        and DATE_FORMAT(pay_time, '%Y-%m-%d') &lt;= #{endDate}
    </select>
    
    <select id="countCustomRangeOrders" resultType="int">
        select count(1) from order_info
        where status = '1'
        and DATE_FORMAT(pay_time, '%Y-%m-%d') >= #{startDate}
        and DATE_FORMAT(pay_time, '%Y-%m-%d') &lt;= #{endDate}
    </select>

    <select id="countTodayOnlineOrders" parameterType="String" resultType="int">
        select count(1) from order_info
        where status = '1'
        and pay_channel = 'wechat'
        and DATE_FORMAT(pay_time, '%Y-%m-%d') = #{dateStr}
    </select>

    <select id="countTodayOfflineOrders" parameterType="String" resultType="int">
        select count(1) from order_info
        where status = '1'
        and pay_channel = 'import'
        and DATE_FORMAT(pay_time, '%Y-%m-%d') = #{dateStr}
    </select>

    <select id="sumTodayOnlineRevenue" parameterType="String" resultType="java.math.BigDecimal">
        select COALESCE(sum(amount), 0) from order_info
        where status = '1'
        and pay_channel = 'wechat'
        and DATE_FORMAT(pay_time, '%Y-%m-%d') = #{dateStr}
    </select>

    <select id="sumTodayOfflineRevenue" parameterType="String" resultType="java.math.BigDecimal">
        select COALESCE(sum(amount), 0) from order_info
        where status = '1'
        and pay_channel = 'import'
        and DATE_FORMAT(pay_time, '%Y-%m-%d') = #{dateStr}
    </select>

    <select id="sumTotalOnlineRevenue" resultType="java.math.BigDecimal">
        select COALESCE(sum(amount), 0) from order_info
        where status = '1'
        and pay_channel = 'wechat'
    </select>

    <select id="sumTotalOfflineRevenue" resultType="java.math.BigDecimal">
        select COALESCE(sum(amount), 0) from order_info
        where status = '1'
        and pay_channel = 'import'
    </select>

    <select id="sumCustomRangeOnlineRevenue" resultType="java.math.BigDecimal">
        select COALESCE(sum(amount), 0) from order_info
        where status = '1'
        and pay_channel = 'wechat'
        and DATE_FORMAT(pay_time, '%Y-%m-%d') >= #{startDate}
        and DATE_FORMAT(pay_time, '%Y-%m-%d') &lt;= #{endDate}
    </select>

    <select id="sumCustomRangeOfflineRevenue" resultType="java.math.BigDecimal">
        select COALESCE(sum(amount), 0) from order_info
        where status = '1'
        and pay_channel = 'import'
        and DATE_FORMAT(pay_time, '%Y-%m-%d') >= #{startDate}
        and DATE_FORMAT(pay_time, '%Y-%m-%d') &lt;= #{endDate}
    </select>

    <select id="countCustomRangeOnlineOrders" resultType="int">
        select count(1) from order_info
        where status = '1'
        and pay_channel = 'wechat'
        and DATE_FORMAT(pay_time, '%Y-%m-%d') >= #{startDate}
        and DATE_FORMAT(pay_time, '%Y-%m-%d') &lt;= #{endDate}
    </select>

    <select id="countCustomRangeOfflineOrders" resultType="int">
        select count(1) from order_info
        where status = '1'
        and pay_channel = 'import'
        and DATE_FORMAT(pay_time, '%Y-%m-%d') >= #{startDate}
        and DATE_FORMAT(pay_time, '%Y-%m-%d') &lt;= #{endDate}
    </select>

    <select id="sumDayOnlineRevenue" parameterType="String" resultType="java.math.BigDecimal">
        select COALESCE(sum(amount), 0) from order_info
        where status = '1'
        and pay_channel = 'wechat'
        and DATE_FORMAT(pay_time, '%Y-%m-%d') = #{dateStr}
    </select>

    <select id="sumDayOfflineRevenue" parameterType="String" resultType="java.math.BigDecimal">
        select COALESCE(sum(amount), 0) from order_info
        where status = '1'
        and pay_channel = 'import'
        and DATE_FORMAT(pay_time, '%Y-%m-%d') = #{dateStr}
    </select>

    <select id="countOrdersByPackage" resultType="java.util.Map">
        select
            package_name as packageName,
            count(1) as orderCount
        from order_info
        where status = '1'
        group by package_name
        order by orderCount desc
    </select>

</mapper>