<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.UserPackageMapper">
    
    <resultMap type="UserPackage" id="UserPackageResult">
        <id     property="userPackageId"   column="user_package_id"   />
        <result property="userId"          column="user_id"           />
        <result property="packageId"       column="package_id"        />
        <result property="orderId"         column="order_id"          />
        <result property="startTime"       column="start_time"        />
        <result property="endTime"         column="end_time"          />
        <result property="status"          column="status"            />
        <result property="createTime"      column="create_time"       />
        <result property="updateTime"      column="update_time"       />
        <result property="remark"          column="remark"            />
        <association property="user" javaType="WxUser">
            <id     property="userId"      column="user_id"           />
            <result property="nickName"    column="nick_name"         />
            <result property="avatarUrl"   column="avatar_url"        />
            <result property="phone"       column="phone"             />
        </association>
        <association property="examPackage" javaType="ExamPackage">
            <id     property="packageId"   column="package_id"        />
            <result property="packageName" column="package_name"      />
            <result property="price"       column="price"             />
            <result property="validityDays" column="validity_days"    />
            <result property="coverImage" column="cover_image"    />
        </association>
        <association property="orderInfo" javaType="OrderInfo">
            <id     property="orderId"     column="order_id"          />
            <result property="orderNo"     column="order_no"          />
            <result property="amount"      column="amount"            />
            <result property="payTime"     column="pay_time"          />
        </association>
    </resultMap>

    <sql id="selectUserPackageVo">
        select up.user_package_id, up.user_id, up.package_id, up.order_id, up.start_time, up.end_time, 
               up.status, up.create_time, up.update_time, up.remark,
               w.nick_name, w.avatar_url, w.phone,
               p.package_name, p.price, p.validity_days,p.cover_image,
               o.order_no, o.amount, o.pay_time
        from user_package up
        left join wx_user w on up.user_id = w.user_id
        left join exam_package p on up.package_id = p.package_id
        left join order_info o on up.order_id = o.order_id
    </sql>
    
    <sql id="selectUserPackageSimpleVo">
        select user_package_id, user_id, package_id, order_id, start_time, end_time, 
               status, create_time, update_time, remark
        from user_package
    </sql>

    <select id="selectUserPackageList" parameterType="UserPackage" resultMap="UserPackageResult">
        <include refid="selectUserPackageVo"/>
        <where>  
            <if test="userId != null "> and up.user_id = #{userId}</if>
            <if test="packageId != null "> and up.package_id = #{packageId}</if>
            <if test="orderId != null "> and up.order_id = #{orderId}</if>
            <if test="status != null  and status != ''"> and up.status = #{status}</if>
            <if test="params.beginStartTime != null and params.beginStartTime != ''"><!-- 开始时间检索 -->
                and up.start_time &gt;= #{params.beginStartTime}
            </if>
            <if test="params.endStartTime != null and params.endStartTime != ''"><!-- 结束时间检索 -->
                and up.start_time &lt;= #{params.endStartTime}
            </if>
            <if test="params.beginEndTime != null and params.beginEndTime != ''"><!-- 开始时间检索 -->
                and up.end_time &gt;= #{params.beginEndTime}
            </if>
            <if test="params.endEndTime != null and params.endEndTime != ''"><!-- 结束时间检索 -->
                and up.end_time &lt;= #{params.endEndTime}
            </if>
        </where>
        order by up.create_time desc
    </select>
    
    <select id="selectUserPackageByUserPackageId" parameterType="Long" resultMap="UserPackageResult">
        <include refid="selectUserPackageVo"/>
        where up.user_package_id = #{userPackageId}
    </select>
    
    <select id="selectUserPackageByUserId" parameterType="Long" resultMap="UserPackageResult">
        <include refid="selectUserPackageVo"/>
        where up.user_id = #{userId} and up.status = '0' and up.end_time > now()
        order by up.end_time desc
    </select>
    
    <select id="selectUserPackageByOrderId" parameterType="Long" resultMap="UserPackageResult">
        <include refid="selectUserPackageVo"/>
        where up.order_id = #{orderId}
    </select>
    
    <insert id="insertUserPackage" parameterType="UserPackage" useGeneratedKeys="true" keyProperty="userPackageId">
        insert into user_package
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="packageId != null">package_id,</if>
            <if test="orderId != null">order_id,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="packageId != null">#{packageId},</if>
            <if test="orderId != null">#{orderId},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>
    
    <update id="updateUserPackage" parameterType="UserPackage">
        update user_package
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="packageId != null">package_id = #{packageId},</if>
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where user_package_id = #{userPackageId}
    </update>
    
    <update id="updateExpiredStatus">
        update user_package set status = '1', update_time = now()
        where status = '0' and end_time &lt; now()
    </update>

    <delete id="deleteUserPackageByUserPackageId" parameterType="Long">
        delete from user_package where user_package_id = #{userPackageId}
    </delete>

    <delete id="deleteUserPackageByUserPackageIds" parameterType="String">
        delete from user_package where user_package_id in 
        <foreach item="userPackageId" collection="array" open="(" separator="," close=")">
            #{userPackageId}
        </foreach>
    </delete>
</mapper> 