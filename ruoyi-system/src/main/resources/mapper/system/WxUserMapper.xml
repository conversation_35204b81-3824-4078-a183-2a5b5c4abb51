<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.WxUserMapper">
    
    <resultMap type="WxUser" id="WxUserResult">
        <id     property="userId"          column="user_id"          />
        <result property="openid"          column="openid"           />
        <result property="unionid"         column="unionid"          />
        <result property="nickName"        column="nick_name"        />
        <result property="avatarUrl"       column="avatar_url"       />
        <result property="gender"          column="gender"           />
        <result property="country"         column="country"          />
        <result property="province"        column="province"         />
        <result property="city"            column="city"             />
        <result property="phone"           column="phone"            />
        <result property="sessionKey"      column="session_key"      />
        <result property="lastLoginTime"   column="last_login_time"  />
        <result property="status"          column="status"           />
        <result property="delFlag"         column="del_flag"         />
        <result property="createTime"      column="create_time"      />
        <result property="updateTime"      column="update_time"      />
        <result property="remark"          column="remark"           />
        <result property="isFree"          column="is_free"           />
        <result property="correctNum"          column="correct_num"           />
        <result property="userType"        column="user_type"        />
    </resultMap>

    <sql id="selectWxUserVo">
        select user_id, openid, unionid, nick_name, avatar_url, gender, country, province, city, phone, session_key, last_login_time, status, del_flag, create_time, update_time, remark,is_free,correct_num,user_type from wx_user
    </sql>

    <select id="selectWxUserList" parameterType="WxUser" resultMap="WxUserResult">
        <include refid="selectWxUserVo"/>
        <where>  
            <if test="openid != null  and openid != ''"> and openid = #{openid}</if>
            <if test="unionid != null  and unionid != ''"> and unionid = #{unionid}</if>
            <if test="nickName != null  and nickName != ''"> and nick_name like concat('%', #{nickName}, '%')</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="userType != null  and userType != ''"> and user_type = #{userType}</if>
            and del_flag = '0'
        </where>
    </select>
    
    <select id="selectWxUserByUserId" parameterType="Long" resultMap="WxUserResult">
        <include refid="selectWxUserVo"/>
        where user_id = #{userId}
    </select>
    
    <select id="selectWxUserByOpenid" parameterType="String" resultMap="WxUserResult">
        <include refid="selectWxUserVo"/>
        where openid = #{openid} and del_flag = '0'
    </select>
        
    <insert id="insertWxUser" parameterType="WxUser" useGeneratedKeys="true" keyProperty="userId">
        insert into wx_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="openid != null">openid,</if>
            <if test="unionid != null">unionid,</if>
            <if test="nickName != null">nick_name,</if>
            <if test="avatarUrl != null">avatar_url,</if>
            <if test="gender != null">gender,</if>
            <if test="country != null">country,</if>
            <if test="province != null">province,</if>
            <if test="city != null">city,</if>
            <if test="phone != null">phone,</if>
            <if test="sessionKey != null">session_key,</if>
            <if test="lastLoginTime != null">last_login_time,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="correctNum != null">correct_num,</if>
            <if test="isFree != null">is_free,</if>
            <if test="userType != null">user_type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="openid != null">#{openid},</if>
            <if test="unionid != null">#{unionid},</if>
            <if test="nickName != null">#{nickName},</if>
            <if test="avatarUrl != null">#{avatarUrl},</if>
            <if test="gender != null">#{gender},</if>
            <if test="country != null">#{country},</if>
            <if test="province != null">#{province},</if>
            <if test="city != null">#{city},</if>
            <if test="phone != null">#{phone},</if>
            <if test="sessionKey != null">#{sessionKey},</if>
            <if test="lastLoginTime != null">#{lastLoginTime},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="correctNum != null">#{correctNum},</if>
            <if test="isFree != null">#{isFree},</if>
            <if test="userType != null">#{userType},</if>
         </trim>
    </insert>

    <update id="updateWxUser" parameterType="WxUser">
        update wx_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="openid != null">openid = #{openid},</if>
            <if test="unionid != null">unionid = #{unionid},</if>
            <if test="nickName != null">nick_name = #{nickName},</if>
            <if test="avatarUrl != null">avatar_url = #{avatarUrl},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="country != null">country = #{country},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="sessionKey != null">session_key = #{sessionKey},</if>
            <if test="lastLoginTime != null">last_login_time = #{lastLoginTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="correctNum != null">correct_num = #{correctNum},</if>
            <if test="isFree != null">is_free = #{isFree},</if>
            <if test="userType != null">user_type = #{userType},</if>
        </trim>
        where user_id = #{userId}
    </update>
    
    <update id="updateWxUserLoginInfo" parameterType="WxUser">
        update wx_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="sessionKey != null">session_key = #{sessionKey},</if>
            <if test="lastLoginTime != null">last_login_time = #{lastLoginTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="openid != null">openid = #{openid},</if>
        </trim>
        where user_id = #{userId}
    </update>

    <delete id="deleteWxUserByUserId" parameterType="Long">
        update wx_user set del_flag = '2' where user_id = #{userId}
    </delete>

    <delete id="deleteWxUserByUserIds" parameterType="String">
        update wx_user set del_flag = '2' where user_id in 
        <foreach item="userId" collection="array" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>
    
    <select id="countTotalUsers" resultType="int">
        select count(1) from wx_user where del_flag = '0'
    </select>
    
    <select id="selectWxUserByPhone" parameterType="String" resultMap="WxUserResult">
        <include refid="selectWxUserVo"/>
        where phone = #{phone} and del_flag = '0'
    </select>

    <select id="countUsersByType" resultType="int">
        select count(1) from wx_user
        where user_type = #{userType} and del_flag = '0'
    </select>

    <select id="countMonthUsersByType" resultType="int">
        select count(1) from wx_user
        where user_type = #{userType}
        and del_flag = '0'
        and DATE_FORMAT(create_time, '%Y-%m') = #{yearMonth}
    </select>
</mapper>