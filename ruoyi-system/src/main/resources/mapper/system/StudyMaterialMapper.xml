<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.StudyMaterialMapper">
    
    <resultMap type="StudyMaterial" id="StudyMaterialResult">
        <id     property="materialId"     column="material_id"      />
        <result property="materialName"   column="material_name"    />
        <result property="materialType"   column="material_type"    />
        <result property="resourceUrl"    column="resource_url"     />
        <result property="coverImg"       column="cover_img"        />
        <result property="materialDesc"   column="material_desc"    />
        <result property="theme"          column="theme"            />
        <result property="packageId"      column="package_id"       />
        <result property="packageName"    column="package_name"     />
        <result property="fileSize"       column="file_size"        />
        <result property="viewCount"      column="view_count"       />
        <result property="downloadCount"  column="download_count"   />
        <result property="status"         column="status"           />
        <result property="sortNum"        column="sort_num"         />
        <result property="createBy"       column="create_by"        />
        <result property="createTime"     column="create_time"      />
        <result property="updateBy"       column="update_by"        />
        <result property="updateTime"     column="update_time"      />
        <result property="remark"         column="remark"           />
    </resultMap>

    <sql id="selectStudyMaterialVo">
        select m.material_id, m.material_name, m.material_type, m.resource_url, m.cover_img,
               m.material_desc, m.theme, m.package_id, p.package_name, m.file_size,
               m.view_count, m.download_count, m.status, m.sort_num, m.create_by, m.create_time,
               m.update_by, m.update_time, m.remark
        from study_material m
        left join exam_package p on m.package_id = p.package_id
    </sql>
    
    <sql id="selectStudyMaterialSimpleVo">
        select material_id, material_name, material_type, resource_url, cover_img,
               material_desc, theme, package_id, file_size, view_count, download_count,
               status, sort_num, create_by, create_time, update_by, update_time, remark
        from study_material
    </sql>

    <select id="selectStudyMaterialList" parameterType="StudyMaterial" resultMap="StudyMaterialResult">
        <include refid="selectStudyMaterialVo"/>
        <where>  
            <if test="materialName != null  and materialName != ''"> and m.material_name like concat('%', #{materialName}, '%')</if>
            <if test="materialType != null  and materialType != ''"> and m.material_type = #{materialType}</if>
            <if test="resourceUrl != null  and resourceUrl != ''"> and m.resource_url = #{resourceUrl}</if>
            <if test="theme != null  and theme != ''"> and m.theme like concat('%', #{theme}, '%')</if>
            <if test="packageId != null "> and m.package_id = #{packageId}</if>
            <if test="packageName != null and packageName != ''"> and p.package_name like concat('%', #{packageName}, '%')</if>
            <if test="status != null  and status != ''"> and m.status = #{status}</if>
            <if test="sortNum != null "> and m.sort_num = #{sortNum}</if>
        </where>
        order by m.sort_num asc, m.create_time desc
    </select>
    
    <select id="selectStudyMaterialByMaterialId" parameterType="Long" resultMap="StudyMaterialResult">
        <include refid="selectStudyMaterialVo"/>
        where m.material_id = #{materialId}
    </select>
    
    <select id="selectStudyMaterialListByPackageId" parameterType="Long" resultMap="StudyMaterialResult">
        <include refid="selectStudyMaterialVo"/>
        where m.package_id = #{packageId} and m.status = '0'
        order by m.sort_num asc, m.create_time desc
    </select>

    <select id="selectStudyMaterialListByType" parameterType="String" resultMap="StudyMaterialResult">
        <include refid="selectStudyMaterialVo"/>
        where m.material_type = #{materialType} and m.status = '0'
        order by m.sort_num asc, m.create_time desc
    </select>
    
    <insert id="insertStudyMaterial" parameterType="StudyMaterial" useGeneratedKeys="true" keyProperty="materialId">
        insert into study_material
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="materialName != null">material_name,</if>
            <if test="materialType != null">material_type,</if>
            <if test="resourceUrl != null">resource_url,</if>
            <if test="coverImg != null">cover_img,</if>
            <if test="materialDesc != null">material_desc,</if>
            <if test="theme != null">theme,</if>
            <if test="packageId != null">package_id,</if>
            <if test="fileSize != null">file_size,</if>
            <if test="viewCount != null">view_count,</if>
            <if test="downloadCount != null">download_count,</if>
            <if test="status != null">status,</if>
            <if test="sortNum != null">sort_num,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="materialName != null">#{materialName},</if>
            <if test="materialType != null">#{materialType},</if>
            <if test="resourceUrl != null">#{resourceUrl},</if>
            <if test="coverImg != null">#{coverImg},</if>
            <if test="materialDesc != null">#{materialDesc},</if>
            <if test="theme != null">#{theme},</if>
            <if test="packageId != null">#{packageId},</if>
            <if test="fileSize != null">#{fileSize},</if>
            <if test="viewCount != null">#{viewCount},</if>
            <if test="downloadCount != null">#{downloadCount},</if>
            <if test="status != null">#{status},</if>
            <if test="sortNum != null">#{sortNum},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>
    
    <update id="updateStudyMaterial" parameterType="StudyMaterial">
        update study_material
        <trim prefix="SET" suffixOverrides=",">
            <if test="materialName != null">material_name = #{materialName},</if>
            <if test="materialType != null">material_type = #{materialType},</if>
            <if test="resourceUrl != null">resource_url = #{resourceUrl},</if>
            <if test="coverImg != null">cover_img = #{coverImg},</if>
            <if test="materialDesc != null">material_desc = #{materialDesc},</if>
            <if test="theme != null">theme = #{theme},</if>
            <if test="packageId != null">package_id = #{packageId},</if>
            <if test="fileSize != null">file_size = #{fileSize},</if>
            <if test="viewCount != null">view_count = #{viewCount},</if>
            <if test="downloadCount != null">download_count = #{downloadCount},</if>
            <if test="status != null">status = #{status},</if>
            <if test="sortNum != null">sort_num = #{sortNum},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where material_id = #{materialId}
    </update>
    
    <update id="updateViewCount" parameterType="Long">
        update study_material
        set view_count = view_count + 1
        where material_id = #{materialId}
    </update>
    
    <update id="updateDownloadCount" parameterType="Long">
        update study_material
        set download_count = download_count + 1
        where material_id = #{materialId}
    </update>
    
    <delete id="deleteStudyMaterialByMaterialId" parameterType="Long">
        delete from study_material where material_id = #{materialId}
    </delete>
    
    <delete id="deleteStudyMaterialByMaterialIds" parameterType="Long">
        delete from study_material where material_id in 
        <foreach item="materialId" collection="array" open="(" separator="," close=")">
            #{materialId}
        </foreach>
    </delete>
    
</mapper> 