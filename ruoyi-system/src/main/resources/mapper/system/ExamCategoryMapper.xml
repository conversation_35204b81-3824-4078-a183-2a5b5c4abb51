<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.ExamCategoryMapper">
    
    <resultMap type="ExamCategory" id="ExamCategoryResult">
        <id     property="categoryId"      column="category_id"     />
        <result property="categoryName"    column="category_name"   />
        <result property="parentId"        column="parent_id"       />
        <result property="orderNum"        column="order_num"       />
        <result property="status"          column="status"          />
        <result property="delFlag"         column="del_flag"        />
        <result property="createBy"        column="create_by"       />
        <result property="createTime"      column="create_time"     />
        <result property="updateBy"        column="update_by"       />
        <result property="updateTime"      column="update_time"     />
        <result property="remark"          column="remark"          />
    </resultMap>

    <sql id="selectExamCategoryVo">
        select category_id, category_name, parent_id, order_num, status, del_flag, create_by, create_time, update_by, update_time, remark from exam_category
    </sql>

    <select id="selectExamCategoryList" parameterType="ExamCategory" resultMap="ExamCategoryResult">
        <include refid="selectExamCategoryVo"/>
        <where>  
            <if test="categoryName != null  and categoryName != ''"> and category_name like concat('%', #{categoryName}, '%')</if>
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            and del_flag = '0'
        </where>
        order by parent_id, order_num
    </select>
    
    <select id="selectExamCategoryAll" resultMap="ExamCategoryResult">
        <include refid="selectExamCategoryVo"/>
        where status = '0' and del_flag = '0'
        order by parent_id, order_num
    </select>
    
    <select id="selectExamCategoryByCategoryId" parameterType="Integer" resultMap="ExamCategoryResult">
        <include refid="selectExamCategoryVo"/>
        where category_id = #{categoryId}
    </select>
        
    <insert id="insertExamCategory" parameterType="ExamCategory" useGeneratedKeys="true" keyProperty="categoryId">
        insert into exam_category
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="categoryName != null and categoryName != ''">category_name,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="orderNum != null">order_num,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="categoryName != null and categoryName != ''">#{categoryName},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="orderNum != null">#{orderNum},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateExamCategory" parameterType="ExamCategory">
        update exam_category
        <trim prefix="SET" suffixOverrides=",">
            <if test="categoryName != null and categoryName != ''">category_name = #{categoryName},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where category_id = #{categoryId}
    </update>

    <delete id="deleteExamCategoryByCategoryId" parameterType="Long">
        update exam_category set del_flag = '2' where category_id = #{categoryId}
    </delete>

    <delete id="deleteExamCategoryByCategoryIds" parameterType="String">
        update exam_category set del_flag = '2' where category_id in 
        <foreach item="categoryId" collection="array" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
    </delete>
</mapper> 