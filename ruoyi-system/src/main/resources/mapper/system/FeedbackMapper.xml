<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.FeedbackMapper">
    
    <resultMap type="Feedback" id="FeedbackResult">
        <id property="feedbackId" column="feedback_id" />
        <result property="userId" column="user_id" />
        <result property="content" column="content" />
        <result property="contact" column="contact" />
        <result property="feedbackType" column="feedback_type" />
        <result property="images" column="images" />
        <result property="status" column="status" />
        <result property="reply" column="reply" />
        <result property="replyTime" column="reply_time" />
        <result property="replyBy" column="reply_by" />
        <result property="createTime" column="create_time" />
        <result property="updateTime" column="update_time" />
        <result property="remark" column="remark" />
        <association property="user" javaType="WxUser">
            <id property="userId" column="user_id" />
            <result property="nickName" column="nick_name" />
            <result property="avatarUrl" column="avatar_url" />
            <result property="phone" column="phone" />
        </association>
    </resultMap>

    <sql id="selectFeedbackVo">
        select f.feedback_id, f.user_id, f.content, f.contact, f.feedback_type, f.images, f.status, f.reply, f.reply_time, f.reply_by, f.create_time, f.update_time, f.remark,
               u.nick_name, u.avatar_url, u.phone
        from feedback f
        left join wx_user u on f.user_id = u.user_id
    </sql>

    <select id="selectFeedbackList" parameterType="Feedback" resultMap="FeedbackResult">
        <include refid="selectFeedbackVo"/>
        <where>
            <if test="userId != null ">and f.user_id = #{userId}</if>
            <if test="feedbackType != null  and feedbackType != ''">and f.feedback_type = #{feedbackType}</if>
            <if test="status != null  and status != ''">and f.status = #{status}</if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''">
                and f.create_time between #{params.beginCreateTime} and #{params.endCreateTime}
            </if>
        </where>
        order by f.create_time desc
    </select>
    
    <select id="selectFeedbackListByUserId" parameterType="Long" resultMap="FeedbackResult">
        <include refid="selectFeedbackVo"/>
        where f.user_id = #{userId}
        order by f.create_time desc
    </select>
    
    <select id="selectFeedbackByFeedbackId" parameterType="Long" resultMap="FeedbackResult">
        <include refid="selectFeedbackVo"/>
        where f.feedback_id = #{feedbackId}
    </select>
        
    <insert id="insertFeedback" parameterType="Feedback" useGeneratedKeys="true" keyProperty="feedbackId">
        insert into feedback
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="content != null">content,</if>
            <if test="contact != null">contact,</if>
            <if test="feedbackType != null">feedback_type,</if>
            <if test="images != null">images,</if>
            <if test="status != null">status,</if>
            <if test="reply != null">reply,</if>
            <if test="replyTime != null">reply_time,</if>
            <if test="replyBy != null">reply_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="content != null">#{content},</if>
            <if test="contact != null">#{contact},</if>
            <if test="feedbackType != null">#{feedbackType},</if>
            <if test="images != null">#{images},</if>
            <if test="status != null">#{status},</if>
            <if test="reply != null">#{reply},</if>
            <if test="replyTime != null">#{replyTime},</if>
            <if test="replyBy != null">#{replyBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateFeedback" parameterType="Feedback">
        update feedback
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="content != null">content = #{content},</if>
            <if test="contact != null">contact = #{contact},</if>
            <if test="feedbackType != null">feedback_type = #{feedbackType},</if>
            <if test="images != null">images = #{images},</if>
            <if test="status != null">status = #{status},</if>
            <if test="reply != null">reply = #{reply},</if>
            <if test="replyTime != null">reply_time = #{replyTime},</if>
            <if test="replyBy != null">reply_by = #{replyBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where feedback_id = #{feedbackId}
    </update>

    <delete id="deleteFeedbackByFeedbackId" parameterType="Long">
        delete from feedback where feedback_id = #{feedbackId}
    </delete>

    <delete id="deleteFeedbackByFeedbackIds" parameterType="String">
        delete from feedback where feedback_id in 
        <foreach item="feedbackId" collection="array" open="(" separator="," close=")">
            #{feedbackId}
        </foreach>
    </delete>
</mapper> 