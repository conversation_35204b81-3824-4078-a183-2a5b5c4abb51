package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.StudyRecord;

/**
 * 学习记录Mapper接口
 * 
 * <AUTHOR>
 */
public interface StudyRecordMapper 
{
    /**
     * 查询学习记录
     * 
     * @param recordId 学习记录主键
     * @return 学习记录
     */
    public StudyRecord selectStudyRecordByRecordId(Long recordId);
    
    /**
     * 查询用户学习记录
     * 
     * @param userId 用户ID
     * @param materialId 资料ID
     * @return 学习记录
     */
    public StudyRecord selectStudyRecordByUserIdAndMaterialId(Long userId, Long materialId);

    /**
     * 查询学习记录列表
     * 
     * @param studyRecord 学习记录
     * @return 学习记录集合
     */
    public List<StudyRecord> selectStudyRecordList(StudyRecord studyRecord);
    
    /**
     * 查询用户的学习记录列表
     * 
     * @param userId 用户ID
     * @return 学习记录集合
     */
    public List<StudyRecord> selectStudyRecordListByUserId(Long userId);
    
    /**
     * 查询套餐下的学习记录列表
     * 
     * @param userId 用户ID
     * @param packageId 套餐ID
     * @return 学习记录集合
     */
    public List<StudyRecord> selectStudyRecordListByPackageId(Long userId, Long packageId);
    
    /**
     * 查询资料的学习记录列表
     * 
     * @param materialId 资料ID
     * @return 学习记录集合
     */
    public List<StudyRecord> selectStudyRecordListByMaterialId(Long materialId);

    /**
     * 新增学习记录
     * 
     * @param studyRecord 学习记录
     * @return 结果
     */
    public int insertStudyRecord(StudyRecord studyRecord);

    /**
     * 修改学习记录
     * 
     * @param studyRecord 学习记录
     * @return 结果
     */
    public int updateStudyRecord(StudyRecord studyRecord);
    
    /**
     * 更新学习进度
     * 
     * @param studyRecord 学习记录
     * @return 结果
     */
    public int updateStudyProgress(StudyRecord studyRecord);

    /**
     * 删除学习记录
     * 
     * @param recordId 学习记录主键
     * @return 结果
     */
    public int deleteStudyRecordByRecordId(Long recordId);

    /**
     * 批量删除学习记录
     * 
     * @param recordIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteStudyRecordByRecordIds(Long[] recordIds);
    
    /**
     * 删除用户的所有学习记录
     * 
     * @param userId 用户ID
     * @return 结果
     */
    public int deleteStudyRecordByUserId(Long userId);
    
    /**
     * 删除资料的所有学习记录
     * 
     * @param materialId 资料ID
     * @return 结果
     */
    public int deleteStudyRecordByMaterialId(Long materialId);
} 