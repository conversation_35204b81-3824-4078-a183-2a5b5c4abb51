package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.Feedback;

/**
 * 反馈信息Mapper接口
 * 
 * <AUTHOR>
 */
public interface FeedbackMapper 
{
    /**
     * 查询反馈信息
     * 
     * @param feedbackId 反馈信息主键
     * @return 反馈信息
     */
    public Feedback selectFeedbackByFeedbackId(Long feedbackId);

    /**
     * 查询反馈信息列表
     * 
     * @param feedback 反馈信息
     * @return 反馈信息集合
     */
    public List<Feedback> selectFeedbackList(Feedback feedback);
    
    /**
     * 查询用户反馈信息列表
     * 
     * @param userId 用户ID
     * @return 反馈信息集合
     */
    public List<Feedback> selectFeedbackListByUserId(Long userId);

    /**
     * 新增反馈信息
     * 
     * @param feedback 反馈信息
     * @return 结果
     */
    public int insertFeedback(Feedback feedback);

    /**
     * 修改反馈信息
     * 
     * @param feedback 反馈信息
     * @return 结果
     */
    public int updateFeedback(Feedback feedback);

    /**
     * 删除反馈信息
     * 
     * @param feedbackId 反馈信息主键
     * @return 结果
     */
    public int deleteFeedbackByFeedbackId(Long feedbackId);

    /**
     * 批量删除反馈信息
     * 
     * @param feedbackIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFeedbackByFeedbackIds(Long[] feedbackIds);
} 