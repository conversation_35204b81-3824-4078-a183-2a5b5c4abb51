package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.ExamMistake;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * 错题Mapper接口
 * 
 * <AUTHOR>
 */
public interface ExamMistakeMapper extends BaseMapper<ExamMistake>
{
    /**
     * 查询错题
     * 
     * @param mistakeId 错题主键
     * @return 错题
     */
    public ExamMistake selectExamMistakeByMistakeId(Long mistakeId);

    /**
     * 查询错题列表
     * 
     * @param examMistake 错题
     * @return 错题集合
     */
    public List<ExamMistake> selectExamMistakeList(ExamMistake examMistake);
    
    /**
     * 查询用户的错题列表
     * 
     * @param userId 用户ID
     * @return 错题集合
     */
    public List<ExamMistake> selectExamMistakeListByUserId(Long userId);
    
    /**
     * 根据用户ID和题目ID查询错题
     * 
     * @param userId 用户ID
     * @param questionId 题目ID
     * @return 错题
     */
    public ExamMistake selectExamMistakeByUserIdAndQuestionId(@Param("userId") Long userId,@Param("questionId")  Long questionId);

    /**
     * 新增错题
     * 
     * @param examMistake 错题
     * @return 结果
     */
    public int insertExamMistake(ExamMistake examMistake);

    /**
     * 修改错题
     * 
     * @param examMistake 错题
     * @return 结果
     */
    public int updateExamMistake(ExamMistake examMistake);
    
    /**
     * 更新错题状态
     * 
     * @param examMistake 错题
     * @return 结果
     */
    public int updateExamMistakeStatus(ExamMistake examMistake);

    /**
     * 删除错题
     * 
     * @param mistakeId 错题主键
     * @return 结果
     */
    public int deleteExamMistakeByMistakeId(Long mistakeId);

    /**
     * 批量删除错题
     * 
     * @param mistakeIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteExamMistakeByMistakeIds(Long[] mistakeIds);
    
    /**
     * 删除用户的所有错题
     * 
     * @param userId 用户ID
     * @return 结果
     */
    public int deleteExamMistakeByUserId(Long userId);
} 