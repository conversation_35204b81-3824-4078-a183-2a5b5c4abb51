package com.ruoyi.system.mapper;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.ruoyi.system.domain.OrderInfo;

/**
 * 订单Mapper接口
 * 
 * <AUTHOR>
 */
public interface OrderInfoMapper 
{
    /**
     * 查询订单
     * 
     * @param orderId 订单主键
     * @return 订单
     */
    public OrderInfo selectOrderInfoByOrderId(Long orderId);
    
    /**
     * 查询订单
     * 
     * @param orderNo 订单编号
     * @return 订单
     */
    public OrderInfo selectOrderInfoByOrderNo(String orderNo);

    /**
     * 查询订单列表
     * 
     * @param orderInfo 订单
     * @return 订单集合
     */
    public List<OrderInfo> selectOrderInfoList(OrderInfo orderInfo);
    
    /**
     * 查询用户的订单列表
     * 
     * @return 订单集合
     */
    public List<OrderInfo> selectOrderInfoListByUserId(OrderInfo orderInfo);

    /**
     * 新增订单
     * 
     * @param orderInfo 订单
     * @return 结果
     */
    public int insertOrderInfo(OrderInfo orderInfo);

    /**
     * 修改订单
     * 
     * @param orderInfo 订单
     * @return 结果
     */
    public int updateOrderInfo(OrderInfo orderInfo);
    
    /**
     * 更新订单状态
     * 
     * @param orderInfo 订单
     * @return 结果
     */
    public int updateOrderStatus(OrderInfo orderInfo);
    
    /**
     * 支付订单
     * 
     * @param orderInfo 订单
     * @return 结果
     */
    public int payOrder(OrderInfo orderInfo);

    /**
     * 删除订单
     * 
     * @param orderId 订单主键
     * @return 结果
     */
    public int deleteOrderInfoByOrderId(Long orderId);

    /**
     * 批量删除订单
     * 
     * @param orderIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOrderInfoByOrderIds(Long[] orderIds);
    
    /**
     * 统计今日订单数
     * 
     * @param dateStr 日期字符串（格式：yyyy-MM-dd）
     * @return 今日订单数
     */
    public int countTodayOrders(String dateStr);
    
    /**
     * 统计今日营业额
     * 
     * @param dateStr 日期字符串（格式：yyyy-MM-dd）
     * @return 今日营业额
     */
    public BigDecimal sumTodayRevenue(String dateStr);
    
    /**
     * 统计总营业额
     * 
     * @return 总营业额
     */
    public BigDecimal sumTotalRevenue();
    
    /**
     * 统计指定日期的营业额
     * 
     * @param dateStr 日期字符串（格式：yyyy-MM-dd）
     * @return 指定日期的营业额
     */
    public BigDecimal sumDayRevenue(String dateStr);
    
    /**
     * 统计指定时间段的营业额
     * 
     * @param startDate 开始日期（格式：yyyy-MM-dd）
     * @param endDate 结束日期（格式：yyyy-MM-dd）
     * @return 指定时间段的营业额
     */
    public BigDecimal sumCustomRangeRevenue(@Param("startDate") String startDate, @Param("endDate") String endDate);
    
    /**
     * 统计指定时间段的订单数量
     *
     * @param startDate 开始日期（格式：yyyy-MM-dd）
     * @param endDate 结束日期（格式：yyyy-MM-dd）
     * @return 指定时间段的订单数量
     */
    public int countCustomRangeOrders(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 统计今日线上订单数（微信支付）
     *
     * @param dateStr 日期字符串（格式：yyyy-MM-dd）
     * @return 今日线上订单数
     */
    public int countTodayOnlineOrders(String dateStr);

    /**
     * 统计今日线下订单数（后台新增）
     *
     * @param dateStr 日期字符串（格式：yyyy-MM-dd）
     * @return 今日线下订单数
     */
    public int countTodayOfflineOrders(String dateStr);

    /**
     * 统计今日线上营业额（微信支付）
     *
     * @param dateStr 日期字符串（格式：yyyy-MM-dd）
     * @return 今日线上营业额
     */
    public BigDecimal sumTodayOnlineRevenue(String dateStr);

    /**
     * 统计今日线下营业额（后台新增）
     *
     * @param dateStr 日期字符串（格式：yyyy-MM-dd）
     * @return 今日线下营业额
     */
    public BigDecimal sumTodayOfflineRevenue(String dateStr);

    /**
     * 统计总线上营业额（微信支付）
     *
     * @return 总线上营业额
     */
    public BigDecimal sumTotalOnlineRevenue();

    /**
     * 统计总线下营业额（后台新增）
     *
     * @return 总线下营业额
     */
    public BigDecimal sumTotalOfflineRevenue();

    /**
     * 统计指定时间段的线上营业额（微信支付）
     *
     * @param startDate 开始日期（格式：yyyy-MM-dd）
     * @param endDate 结束日期（格式：yyyy-MM-dd）
     * @return 指定时间段的线上营业额
     */
    public BigDecimal sumCustomRangeOnlineRevenue(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 统计指定时间段的线下营业额（后台新增）
     *
     * @param startDate 开始日期（格式：yyyy-MM-dd）
     * @param endDate 结束日期（格式：yyyy-MM-dd）
     * @return 指定时间段的线下营业额
     */
    public BigDecimal sumCustomRangeOfflineRevenue(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 统计指定时间段的线上订单数量（微信支付）
     *
     * @param startDate 开始日期（格式：yyyy-MM-dd）
     * @param endDate 结束日期（格式：yyyy-MM-dd）
     * @return 指定时间段的线上订单数量
     */
    public int countCustomRangeOnlineOrders(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 统计指定时间段的线下订单数量（后台新增）
     *
     * @param startDate 开始日期（格式：yyyy-MM-dd）
     * @param endDate 结束日期（格式：yyyy-MM-dd）
     * @return 指定时间段的线下订单数量
     */
    public int countCustomRangeOfflineOrders(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 统计指定日期的线上营业额（微信支付）
     *
     * @param dateStr 日期字符串（格式：yyyy-MM-dd）
     * @return 指定日期的线上营业额
     */
    public BigDecimal sumDayOnlineRevenue(String dateStr);

    /**
     * 统计指定日期的线下营业额（后台新增）
     *
     * @param dateStr 日期字符串（格式：yyyy-MM-dd）
     * @return 指定日期的线下营业额
     */
    public BigDecimal sumDayOfflineRevenue(String dateStr);

    List<Map<String, Object>> countOrdersByPackage();
}