package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.ExamCategory;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 题库分类Mapper接口
 * 
 * <AUTHOR>
 */
public interface ExamCategoryMapper extends BaseMapper<ExamCategory>
{
    /**
     * 查询题库分类
     * 
     * @param categoryId 题库分类主键
     * @return 题库分类
     */
    public ExamCategory selectExamCategoryByCategoryId(Integer categoryId);

    /**
     * 查询题库分类列表
     * 
     * @param examCategory 题库分类
     * @return 题库分类集合
     */
    public List<ExamCategory> selectExamCategoryList(ExamCategory examCategory);

    /**
     * 新增题库分类
     * 
     * @param examCategory 题库分类
     * @return 结果
     */
    public int insertExamCategory(ExamCategory examCategory);

    /**
     * 修改题库分类
     * 
     * @param examCategory 题库分类
     * @return 结果
     */
    public int updateExamCategory(ExamCategory examCategory);

    /**
     * 删除题库分类
     * 
     * @param categoryId 题库分类主键
     * @return 结果
     */
    public int deleteExamCategoryByCategoryId(Long categoryId);

    /**
     * 批量删除题库分类
     * 
     * @param categoryIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteExamCategoryByCategoryIds(Long[] categoryIds);
    
    /**
     * 查询所有正常状态的题库分类
     * 
     * @return 题库分类集合
     */
    public List<ExamCategory> selectExamCategoryAll();
} 