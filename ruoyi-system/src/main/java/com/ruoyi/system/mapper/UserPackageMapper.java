package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.UserPackage;

/**
 * 用户套餐Mapper接口
 * 
 * <AUTHOR>
 */
public interface UserPackageMapper 
{
    /**
     * 查询用户套餐
     * 
     * @param userPackageId 用户套餐主键
     * @return 用户套餐
     */
    public UserPackage selectUserPackageByUserPackageId(Long userPackageId);

    /**
     * 查询用户套餐列表
     * 
     * @param userPackage 用户套餐
     * @return 用户套餐集合
     */
    public List<UserPackage> selectUserPackageList(UserPackage userPackage);
    
    /**
     * 查询用户有效套餐
     * 
     * @param userId 用户ID
     * @return 用户套餐
     */
    public List<UserPackage> selectUserPackageByUserId(Long userId);

    /**
     * 新增用户套餐
     * 
     * @param userPackage 用户套餐
     * @return 结果
     */
    public int insertUserPackage(UserPackage userPackage);

    /**
     * 修改用户套餐
     * 
     * @param userPackage 用户套餐
     * @return 结果
     */
    public int updateUserPackage(UserPackage userPackage);

    /**
     * 删除用户套餐
     * 
     * @param userPackageId 用户套餐主键
     * @return 结果
     */
    public int deleteUserPackageByUserPackageId(Long userPackageId);

    /**
     * 批量删除用户套餐
     * 
     * @param userPackageIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteUserPackageByUserPackageIds(Long[] userPackageIds);
    
    /**
     * 更新过期状态
     * 
     * @return 结果
     */
    public int updateExpiredStatus();
    
    /**
     * 根据订单ID查询用户套餐
     * 
     * @param orderId 订单ID
     * @return 用户套餐
     */
    public UserPackage selectUserPackageByOrderId(Long orderId);
} 