package com.ruoyi.system.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.system.domain.ExamPackageQuestion;

/**
 * 套餐题目关联Mapper接口
 * 
 * <AUTHOR>
 */
public interface ExamPackageQuestionMapper 
{
    /**
     * 查询套餐题目关联
     * 
     * @param id 套餐题目关联主键
     * @return 套餐题目关联
     */
    public ExamPackageQuestion selectExamPackageQuestionById(Long id);

    /**
     * 查询套餐题目关联列表
     *
     * @param examPackageQuestion 套餐题目关联
     * @return 套餐题目关联集合
     */
    public List<ExamPackageQuestion> selectExamPackageQuestionList(ExamPackageQuestion examPackageQuestion);

    /**
     * 根据套餐ID和题目ID查询关联关系
     *
     * @param packageId 套餐ID
     * @param questionId 题目ID
     * @return 套餐题目关联
     */
    public ExamPackageQuestion selectByPackageIdAndQuestionId(@Param("packageId") Long packageId, @Param("questionId") Long questionId);

    /**
     * 新增套餐题目关联
     * 
     * @param examPackageQuestion 套餐题目关联
     * @return 结果
     */
    public int insertExamPackageQuestion(ExamPackageQuestion examPackageQuestion);

    /**
     * 批量新增套餐题目关联
     * 
     * @param examPackageQuestionList 套餐题目关联列表
     * @return 结果
     */
    public int batchInsertExamPackageQuestion(List<ExamPackageQuestion> examPackageQuestionList);

    /**
     * 修改套餐题目关联
     * 
     * @param examPackageQuestion 套餐题目关联
     * @return 结果
     */
    public int updateExamPackageQuestion(ExamPackageQuestion examPackageQuestion);

    /**
     * 删除套餐题目关联
     * 
     * @param id 套餐题目关联主键
     * @return 结果
     */
    public int deleteExamPackageQuestionById(Long id);

    /**
     * 批量删除套餐题目关联
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteExamPackageQuestionByIds(Long[] ids);
    
    /**
     * 通过套餐ID删除套餐题目关联
     * 
     * @param packageId 套餐ID
     * @return 结果
     */
    public int deleteExamPackageQuestionByPackageId(Long packageId);
    
    /**
     * 通过题目ID删除套餐题目关联
     * 
     * @param questionId 题目ID
     * @return 结果
     */
    public int deleteExamPackageQuestionByQuestionId(Long questionId);
    
    /**
     * 通过套餐ID和题目ID删除套餐题目关联
     * 
     * @param packageId 套餐ID
     * @param questionId 题目ID
     * @return 结果
     */
    public int deleteExamPackageQuestionByPackageIdAndQuestionId(Long packageId, Long questionId);
} 