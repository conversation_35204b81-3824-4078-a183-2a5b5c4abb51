package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.WxUser;
import org.apache.ibatis.annotations.Param;

/**
 * 微信用户Mapper接口
 * 
 * <AUTHOR>
 */
public interface WxUserMapper 
{
    /**
     * 查询微信用户
     * 
     * @param userId 微信用户主键
     * @return 微信用户
     */
    public WxUser selectWxUserByUserId(Long userId);

    /**
     * 查询微信用户列表
     * 
     * @param wxUser 微信用户
     * @return 微信用户集合
     */
    public List<WxUser> selectWxUserList(WxUser wxUser);
    
    /**
     * 通过openid查询微信用户
     * 
     * @param openid 微信openid
     * @return 微信用户
     */
    public WxUser selectWxUserByOpenid(String openid);

    /**
     * 新增微信用户
     * 
     * @param wxUser 微信用户
     * @return 结果
     */
    public int insertWxUser(WxUser wxUser);

    /**
     * 修改微信用户
     * 
     * @param wxUser 微信用户
     * @return 结果
     */
    public int updateWxUser(WxUser wxUser);
    
    /**
     * 更新微信用户登录信息
     * 
     * @param wxUser 微信用户
     * @return 结果
     */
    public int updateWxUserLoginInfo(WxUser wxUser);

    /**
     * 删除微信用户
     * 
     * @param userId 微信用户主键
     * @return 结果
     */
    public int deleteWxUserByUserId(Long userId);

    /**
     * 批量删除微信用户
     * 
     * @param userIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWxUserByUserIds(Long[] userIds);
    
    /**
     * 统计平台总用户数
     * 
     * @return 用户总数
     */
    public int countTotalUsers();

    /**
     * 通过手机号查询微信用户
     *
     * @param phone 手机号
     * @return 微信用户
     */
    public WxUser selectWxUserByPhone(String phone);

    int countMonthUsersByType(@Param("userType") String userType, @Param("yearMonth")  String yearMonth);

    int countUsersByType(String userType);
}