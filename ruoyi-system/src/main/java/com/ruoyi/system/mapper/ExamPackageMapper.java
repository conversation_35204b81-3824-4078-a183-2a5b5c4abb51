package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.ExamPackage;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 题库套餐Mapper接口
 * 
 * <AUTHOR>
 */
public interface ExamPackageMapper extends BaseMapper<ExamPackage>
{
    /**
     * 查询题库套餐
     * 
     * @param packageId 题库套餐主键
     * @return 题库套餐
     */
    public ExamPackage selectExamPackageByPackageId(Long packageId);

    /**
     * 查询题库套餐列表
     * 
     * @param examPackage 题库套餐
     * @return 题库套餐集合
     */
    public List<ExamPackage> selectExamPackageList(ExamPackage examPackage);

    /**
     * 新增题库套餐
     * 
     * @param examPackage 题库套餐
     * @return 结果
     */
    public int insertExamPackage(ExamPackage examPackage);

    /**
     * 修改题库套餐
     * 
     * @param examPackage 题库套餐
     * @return 结果
     */
    public int updateExamPackage(ExamPackage examPackage);

    /**
     * 删除题库套餐
     * 
     * @param packageId 题库套餐主键
     * @return 结果
     */
    public int deleteExamPackageByPackageId(Long packageId);

    /**
     * 批量删除题库套餐
     * 
     * @param packageIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteExamPackageByPackageIds(Long[] packageIds);

    /**
     * 查询所有题库套餐
     * 
     * @return 题库套餐集合
     */
    public List<ExamPackage> selectExamPackageAll();
} 