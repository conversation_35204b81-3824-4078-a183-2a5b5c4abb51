package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.ExamQuestion;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 题目Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface ExamQuestionMapper extends BaseMapper<ExamQuestion>
{
    /**
     * 查询题目
     *
     * @param questionId 题目主键
     * @return 题目
     */
    public ExamQuestion selectExamQuestionByQuestionId(Long questionId);

    /**
     * 根据题目内容查询题目
     *
     * @param questionContent 题目内容
     * @return 题目
     */
    public ExamQuestion selectExamQuestionByContent(@Param("questionContent") String questionContent);

    /**
     * 查询题目列表
     * 
     * @param examQuestion 题目
     * @return 题目集合
     */
    public List<ExamQuestion> selectExamQuestionList(ExamQuestion examQuestion);

    /**
     * 新增题目
     * 
     * @param examQuestion 题目
     * @return 结果
     */
    public int insertExamQuestion(ExamQuestion examQuestion);

    /**
     * 修改题目
     * 
     * @param examQuestion 题目
     * @return 结果
     */
    public int updateExamQuestion(ExamQuestion examQuestion);

    /**
     * 删除题目
     * 
     * @param questionId 题目主键
     * @return 结果
     */
    public int deleteExamQuestionByQuestionId(Long questionId);

    /**
     * 批量删除题目
     * 
     * @param questionIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteExamQuestionByQuestionIds(Long[] questionIds);
    
    /**
     * 查询套餐下的题目列表
     * 
     * @param packageId 套餐ID
     * @param questionType 题库类型（1模拟考试题库 2练习题库）
     * @return 题目集合
     */
    public List<ExamQuestion> selectExamQuestionsByPackageId(Long packageId, String questionType);

    /**
     * 批量插入题目
     *
     * @param examQuestions 题目列表
     * @return 结果
     */
    public int batchInsertExamQuestion(List<ExamQuestion> examQuestions);

    List<String> getSubjectsByPackageId(Long packageId);
}