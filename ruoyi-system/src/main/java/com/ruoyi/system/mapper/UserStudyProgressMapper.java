package com.ruoyi.system.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.system.domain.UserStudyProgress;

/**
 * 用户学习进度Mapper接口
 * 
 * <AUTHOR>
 */
public interface UserStudyProgressMapper 
{
    /**
     * 查询用户学习进度
     * 
     * @param progressId 用户学习进度主键
     * @return 用户学习进度
     */
    public UserStudyProgress selectUserStudyProgressByProgressId(Long progressId);

    /**
     * 查询用户学习进度列表
     * 
     * @param userStudyProgress 用户学习进度
     * @return 用户学习进度集合
     */
    public List<UserStudyProgress> selectUserStudyProgressList(UserStudyProgress userStudyProgress);

    /**
     * 根据用户ID和套餐ID查询学习进度
     * 
     * @param userId 用户ID
     * @param packageId 套餐ID
     * @return 用户学习进度
     */
    public UserStudyProgress selectUserStudyProgressByUserAndPackage(@Param("userId") Long userId, @Param("packageId") Long packageId);

    /**
     * 根据用户ID查询学习进度列表
     * 
     * @param userId 用户ID
     * @return 用户学习进度集合
     */
    public List<UserStudyProgress> selectUserStudyProgressListByUserId(@Param("userId") Long userId);

    /**
     * 新增用户学习进度
     * 
     * @param userStudyProgress 用户学习进度
     * @return 结果
     */
    public int insertUserStudyProgress(UserStudyProgress userStudyProgress);

    /**
     * 修改用户学习进度
     * 
     * @param userStudyProgress 用户学习进度
     * @return 结果
     */
    public int updateUserStudyProgress(UserStudyProgress userStudyProgress);

    /**
     * 删除用户学习进度
     * 
     * @param progressId 用户学习进度主键
     * @return 结果
     */
    public int deleteUserStudyProgressByProgressId(Long progressId);

    /**
     * 批量删除用户学习进度
     * 
     * @param progressIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteUserStudyProgressByProgressIds(Long[] progressIds);
}
