package com.ruoyi.system.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.system.domain.StudyMaterial;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 学习资料Mapper接口
 * 
 * <AUTHOR>
 */
public interface StudyMaterialMapper extends BaseMapper<StudyMaterial>
{
    /**
     * 查询学习资料
     * 
     * @param materialId 学习资料主键
     * @return 学习资料
     */
    public StudyMaterial selectStudyMaterialByMaterialId(Long materialId);

    /**
     * 查询学习资料列表
     * 
     * @param studyMaterial 学习资料
     * @return 学习资料集合
     */
    public List<StudyMaterial> selectStudyMaterialList(StudyMaterial studyMaterial);
    
    /**
     * 查询套餐下的学习资料列表
     * 
     * @param packageId 套餐ID
     * @return 学习资料集合
     */
    public List<StudyMaterial> selectStudyMaterialListByPackageId(@Param("packageId") Long packageId);
    
    /**
     * 根据资料类型查询学习资料列表
     * 
     * @param materialType 资料类型（0电子书 1视频）
     * @return 学习资料集合
     */
    public List<StudyMaterial> selectStudyMaterialListByType(@Param("materialType") String materialType);

    /**
     * 新增学习资料
     * 
     * @param studyMaterial 学习资料
     * @return 结果
     */
    public int insertStudyMaterial(StudyMaterial studyMaterial);

    /**
     * 修改学习资料
     * 
     * @param studyMaterial 学习资料
     * @return 结果
     */
    public int updateStudyMaterial(StudyMaterial studyMaterial);
    
    /**
     * 更新学习资料浏览次数
     * 
     * @param materialId 学习资料ID
     * @return 结果
     */
    public int updateViewCount(Long materialId);
    
    /**
     * 更新学习资料下载次数
     * 
     * @param materialId 学习资料ID
     * @return 结果
     */
    public int updateDownloadCount(Long materialId);

    /**
     * 删除学习资料
     * 
     * @param materialId 学习资料主键
     * @return 结果
     */
    public int deleteStudyMaterialByMaterialId(Long materialId);

    /**
     * 批量删除学习资料
     * 
     * @param materialIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteStudyMaterialByMaterialIds(Long[] materialIds);
} 