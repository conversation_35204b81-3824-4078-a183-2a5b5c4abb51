package com.ruoyi.system.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import com.github.binarywang.wxpay.config.WxPayConfig;
import com.github.binarywang.wxpay.service.WxPayService;
import com.github.binarywang.wxpay.service.impl.WxPayServiceImpl;
import lombok.Data;
import org.springframework.boot.context.properties.EnableConfigurationProperties;

/**
 * 微信支付配置
 * 
 * <AUTHOR>
 */
@Configuration
@EnableConfigurationProperties(WxPayConfiguration.WxPayProperties.class)
public class WxPayConfiguration {

    @Autowired
    private WxPayProperties properties;

    @Bean
    public WxPayConfig wxPayConfig() {
        WxPayConfig payConfig = new WxPayConfig();
        payConfig.setAppId(properties.getAppId());
        payConfig.setMchId(properties.getMchId());
        payConfig.setMchKey(properties.getMchKey());
        payConfig.setKeyPath(properties.getKeyPath());
        payConfig.setNotifyUrl(properties.getNotifyUrl());
        payConfig.setTradeType(properties.getTradeType());
        payConfig.setSignType(properties.getSignType());
        
        // 可以指定是否使用沙箱环境
        payConfig.setUseSandboxEnv(properties.isSandbox());
        
        return payConfig;
    }

    @Bean
    public WxPayService wxPayService(WxPayConfig payConfig) {
        WxPayService wxPayService = new WxPayServiceImpl();
        wxPayService.setConfig(payConfig);
        return wxPayService;
    }

    @Data
    @ConfigurationProperties(prefix = "wx.pay")
    public static class WxPayProperties {
        /**
         * 设置微信公众号或者小程序等的appid
         */
        private String appId;

        /**
         * 微信支付商户号
         */
        private String mchId;

        /**
         * 微信支付商户密钥
         */
        private String mchKey;

        /**
         * 服务商模式下的子商户公众账号ID，普通模式请不要配置，请在配置文件中将对应项删除
         */
        private String subAppId;

        /**
         * 服务商模式下的子商户号，普通模式请不要配置，最好是请在配置文件中将对应项删除
         */
        private String subMchId;

        /**
         * 微信支付异步回调地址
         */
        private String notifyUrl;

        /**
         * apiclient_cert.p12文件的绝对路径，或者如果放在项目中，请以classpath:开头指定
         */
        private String keyPath;
        
        /**
         * 交易类型
         */
        private String tradeType;
        
        /**
         * 签名类型，默认MD5
         */
        private String signType;
        
        /**
         * 是否使用沙箱环境，默认为false
         */
        private boolean sandbox;
    }
} 