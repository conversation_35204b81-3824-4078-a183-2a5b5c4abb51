package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.WxUser;

/**
 * 微信用户服务接口
 * 
 * <AUTHOR>
 */
public interface IWxUserService 
{
    /**
     * 查询微信用户
     * 
     * @param userId 微信用户主键
     * @return 微信用户
     */
    public WxUser selectWxUserByUserId(Long userId);
    /**
     * 查询微信用户
     *
     * @param userId 微信用户主键
     * @return 微信用户
     */
    public WxUser selectWxUserByPhone(String phone);
    /**
     * 查询微信用户列表
     * 
     * @param wxUser 微信用户
     * @return 微信用户集合
     */
    public List<WxUser> selectWxUserList(WxUser wxUser);
    
    /**
     * 通过openid查询微信用户
     * 
     * @param openid 微信openid
     * @return 微信用户
     */
    public WxUser selectWxUserByOpenid(String openid);
    
    /**
     * 微信用户注册
     * 
     * @param wxUser 微信用户信息
     * @return 结果
     */
    public int registerWxUser(WxUser wxUser);
    
    /**
     * 微信用户登录
     * 
     * @param openid 微信openid
     * @param sessionKey 会话密钥
     * @return 微信用户
     */
    public WxUser loginWxUser(String openid, String sessionKey);
    
    /**
     * 根据Token获取微信用户信息
     * 
     * @param token 登录令牌
     * @return 微信用户
     */
    public WxUser getWxUserByToken(String token);

    /**
     * 新增微信用户
     * 
     * @param wxUser 微信用户
     * @return 结果
     */
    public int insertWxUser(WxUser wxUser);

    /**
     * 修改微信用户
     * 
     * @param wxUser 微信用户
     * @return 结果
     */
    public int updateWxUser(WxUser wxUser);
    
    /**
     * 更新微信用户登录信息
     * 
     * @param wxUser 微信用户
     * @return 结果
     */
    public int updateWxUserLoginInfo(WxUser wxUser);

    /**
     * 批量删除微信用户
     * 
     * @param userIds 需要删除的微信用户主键集合
     * @return 结果
     */
    public int deleteWxUserByUserIds(Long[] userIds);

    /**
     * 删除微信用户信息
     * 
     * @param userId 微信用户主键
     * @return 结果
     */
    public int deleteWxUserByUserId(Long userId);
} 