package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.Feedback;

/**
 * 意见反馈服务接口
 * 
 * <AUTHOR>
 */
public interface IFeedbackService
{
    /**
     * 查询反馈信息
     * 
     * @param feedbackId 反馈ID
     * @return 反馈信息
     */
    public Feedback selectFeedbackByFeedbackId(Long feedbackId);

    /**
     * 查询反馈列表
     * 
     * @param feedback 反馈信息
     * @return 反馈集合
     */
    public List<Feedback> selectFeedbackList(Feedback feedback);
    
    /**
     * 根据用户ID查询反馈列表
     * 
     * @param userId 用户ID
     * @return 反馈集合
     */
    public List<Feedback> selectFeedbackListByUserId(Long userId);

    /**
     * 新增反馈
     * 
     * @param feedback 反馈信息
     * @return 结果
     */
    public int insertFeedback(Feedback feedback);

    /**
     * 修改反馈
     * 
     * @param feedback 反馈信息
     * @return 结果
     */
    public int updateFeedback(Feedback feedback);
    
    /**
     * 回复反馈
     *
     * @param feedback 反馈信息
     * @return 结果
     */
    public int replyFeedback(Feedback feedback);

    /**
     * 批量删除反馈
     * 
     * @param feedbackIds 需要删除的反馈ID数组
     * @return 结果
     */
    public int deleteFeedbackByFeedbackIds(Long[] feedbackIds);

    /**
     * 删除反馈信息
     * 
     * @param feedbackId 反馈ID
     * @return 结果
     */
    public int deleteFeedbackByFeedbackId(Long feedbackId);
} 