package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.UserStudyProgress;

/**
 * 用户学习进度Service接口
 * 
 * <AUTHOR>
 */
public interface IUserStudyProgressService 
{
    /**
     * 查询用户学习进度
     * 
     * @param progressId 用户学习进度主键
     * @return 用户学习进度
     */
    public UserStudyProgress selectUserStudyProgressByProgressId(Long progressId);

    /**
     * 查询用户学习进度列表
     * 
     * @param userStudyProgress 用户学习进度
     * @return 用户学习进度集合
     */
    public List<UserStudyProgress> selectUserStudyProgressList(UserStudyProgress userStudyProgress);

    /**
     * 根据用户ID和套餐ID查询学习进度
     * 
     * @param userId 用户ID
     * @param packageId 套餐ID
     * @return 用户学习进度
     */
    public UserStudyProgress selectUserStudyProgressByUserAndPackage(Long userId, Long packageId);

    /**
     * 根据用户ID查询学习进度列表
     * 
     * @param userId 用户ID
     * @return 用户学习进度集合
     */
    public List<UserStudyProgress> selectUserStudyProgressListByUserId(Long userId);

    /**
     * 新增用户学习进度
     * 
     * @param userStudyProgress 用户学习进度
     * @return 结果
     */
    public int insertUserStudyProgress(UserStudyProgress userStudyProgress);

    /**
     * 修改用户学习进度
     * 
     * @param userStudyProgress 用户学习进度
     * @return 结果
     */
    public int updateUserStudyProgress(UserStudyProgress userStudyProgress);

    /**
     * 批量删除用户学习进度
     * 
     * @param progressIds 需要删除的用户学习进度主键集合
     * @return 结果
     */
    public int deleteUserStudyProgressByProgressIds(Long[] progressIds);

    /**
     * 删除用户学习进度信息
     * 
     * @param progressId 用户学习进度主键
     * @return 结果
     */
    public int deleteUserStudyProgressByProgressId(Long progressId);

    /**
     * 更新用户学习进度
     * 根据传入的sortNum查找下一个学习资料的sortNum并更新
     * 
     * @param userId 用户ID
     * @param packageId 套餐ID
     * @param currentSortNum 当前完成的资料排序号
     * @return 结果
     */
    public int updateUserStudyProgressBySortNum(Long userId, Long packageId, Integer currentSortNum);
}
