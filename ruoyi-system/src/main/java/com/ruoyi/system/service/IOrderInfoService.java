package com.ruoyi.system.service;

import java.util.List;
import java.util.Map;
import com.ruoyi.system.domain.OrderInfo;

/**
 * 订单服务接口
 * 
 * <AUTHOR>
 */
public interface IOrderInfoService 
{
    /**
     * 查询订单
     * 
     * @param orderId 订单主键
     * @return 订单
     */
    public OrderInfo selectOrderInfoByOrderId(Long orderId);
    
    /**
     * 查询订单
     * 
     * @param orderNo 订单编号
     * @return 订单
     */
    public OrderInfo selectOrderInfoByOrderNo(String orderNo);

    /**
     * 查询订单列表
     * 
     * @param orderInfo 订单
     * @return 订单集合
     */
    public List<OrderInfo> selectOrderInfoList(OrderInfo orderInfo);
    
    /**
     * 查询用户的订单列表
     * 
     * @param userId 用户ID
     * @return 订单集合
     */
    public List<OrderInfo> selectOrderInfoListByUserId(OrderInfo orderInfo);

    /**
     * 创建订单
     * 
     * @param userId 用户ID
     * @param packageId 套餐ID
     * @return 创建的订单
     */
    public OrderInfo createOrder(Long userId, Long packageId);

    /**
     * 支付订单
     * 
     * @param orderNo 订单编号
     * @param payChannel 支付渠道
     * @param transactionId 交易流水号
     * @return 结果
     */
    public int payOrder(String orderNo, String payChannel, String transactionId);
    
    /**
     * 创建微信支付订单
     * 
     * @param orderNo 订单编号
     * @param openid 用户openid
     * @return 微信支付参数
     */
    public Map<String, String> createWxPayOrder(String orderNo, String openid);
    
    /**
     * 处理微信支付通知
     * 
     * @param notifyData 通知数据
     * @return 处理结果
     */
    public String handleWxPayNotify(String notifyData);
    
    /**
     * 查询微信支付订单状态
     * 
     * @param orderNo 订单编号
     * @return 支付状态
     */
    public String queryWxPayOrderStatus(String orderNo);
    
    /**
     * 取消订单
     * 
     * @param orderNo 订单编号
     * @return 结果
     */
    public int cancelOrder(String orderNo);

    /**
     * 新增订单
     * 
     * @param orderInfo 订单
     * @return 结果
     */
    public int insertOrderInfo(OrderInfo orderInfo);

    /**
     * 修改订单
     * 
     * @param orderInfo 订单
     * @return 结果
     */
    public int updateOrderInfo(OrderInfo orderInfo);

    /**
     * 批量删除订单
     * 
     * @param orderIds 需要删除的订单主键集合
     * @return 结果
     */
    public int deleteOrderInfoByOrderIds(Long[] orderIds);

    /**
     * 删除订单信息
     * 
     * @param orderId 订单主键
     * @return 结果
     */
    public int deleteOrderInfoByOrderId(Long orderId);
} 