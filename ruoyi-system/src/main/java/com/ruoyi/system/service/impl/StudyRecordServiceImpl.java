package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.StudyRecordMapper;
import com.ruoyi.system.domain.StudyRecord;
import com.ruoyi.system.service.IStudyRecordService;
import com.ruoyi.common.utils.DateUtils;

/**
 * 学习记录Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class StudyRecordServiceImpl implements IStudyRecordService 
{
    @Autowired
    private StudyRecordMapper studyRecordMapper;

    /**
     * 查询学习记录
     * 
     * @param recordId 学习记录主键
     * @return 学习记录
     */
    @Override
    public StudyRecord selectStudyRecordByRecordId(Long recordId)
    {
        return studyRecordMapper.selectStudyRecordByRecordId(recordId);
    }
    
    /**
     * 查询用户学习记录
     * 
     * @param userId 用户ID
     * @param materialId 资料ID
     * @return 学习记录
     */
    @Override
    public StudyRecord selectStudyRecordByUserIdAndMaterialId(Long userId, Long materialId)
    {
        return studyRecordMapper.selectStudyRecordByUserIdAndMaterialId(userId, materialId);
    }

    /**
     * 查询学习记录列表
     * 
     * @param studyRecord 学习记录
     * @return 学习记录
     */
    @Override
    public List<StudyRecord> selectStudyRecordList(StudyRecord studyRecord)
    {
        return studyRecordMapper.selectStudyRecordList(studyRecord);
    }
    
    /**
     * 查询用户的学习记录列表
     * 
     * @param userId 用户ID
     * @return 学习记录集合
     */
    @Override
    public List<StudyRecord> selectStudyRecordListByUserId(Long userId)
    {
        return studyRecordMapper.selectStudyRecordListByUserId(userId);
    }
    
    /**
     * 查询套餐下的学习记录列表
     * 
     * @param userId 用户ID
     * @param packageId 套餐ID
     * @return 学习记录集合
     */
    @Override
    public List<StudyRecord> selectStudyRecordListByPackageId(Long userId, Long packageId)
    {
        return studyRecordMapper.selectStudyRecordListByPackageId(userId, packageId);
    }
    
    /**
     * 查询资料的学习记录列表
     * 
     * @param materialId 资料ID
     * @return 学习记录集合
     */
    @Override
    public List<StudyRecord> selectStudyRecordListByMaterialId(Long materialId)
    {
        return studyRecordMapper.selectStudyRecordListByMaterialId(materialId);
    }
    
    /**
     * 更新学习进度
     * 
     * @param userId 用户ID
     * @param materialId 资料ID
     * @param packageId 套餐ID
     * @param materialType 资料类型
     * @param progress 学习进度
     * @param lastPosition 学习位置
     * @param studyTime 学习时长
     * @return 结果
     */
    @Override
    public int updateProgress(Long userId, Long materialId, Long packageId, String materialType, 
            Integer progress, String lastPosition, Integer studyTime)
    {
        // 查询是否已有记录
        StudyRecord record = studyRecordMapper.selectStudyRecordByUserIdAndMaterialId(userId, materialId);
        
        if (record != null)
        {
            // 更新记录
            StudyRecord updateRecord = new StudyRecord();
            updateRecord.setRecordId(record.getRecordId());
            updateRecord.setProgress(progress);
            updateRecord.setLastPosition(lastPosition);
            updateRecord.setStudyTime(record.getStudyTime() + studyTime);
            updateRecord.setUpdateTime(DateUtils.getNowDate());
            
            return studyRecordMapper.updateStudyProgress(updateRecord);
        }
        else
        {
            // 新增记录
            StudyRecord newRecord = new StudyRecord();
            newRecord.setUserId(userId);
            newRecord.setMaterialId(materialId);
            newRecord.setPackageId(packageId);
            newRecord.setMaterialType(materialType);
            newRecord.setProgress(progress);
            newRecord.setLastPosition(lastPosition);
            newRecord.setStudyTime(studyTime);
            newRecord.setCreateTime(DateUtils.getNowDate());
            
            return studyRecordMapper.insertStudyRecord(newRecord);
        }
    }

    /**
     * 新增学习记录
     * 
     * @param studyRecord 学习记录
     * @return 结果
     */
    @Override
    public int insertStudyRecord(StudyRecord studyRecord)
    {
        studyRecord.setCreateTime(DateUtils.getNowDate());
        return studyRecordMapper.insertStudyRecord(studyRecord);
    }

    /**
     * 修改学习记录
     * 
     * @param studyRecord 学习记录
     * @return 结果
     */
    @Override
    public int updateStudyRecord(StudyRecord studyRecord)
    {
        studyRecord.setUpdateTime(DateUtils.getNowDate());
        return studyRecordMapper.updateStudyRecord(studyRecord);
    }

    /**
     * 批量删除学习记录
     * 
     * @param recordIds 需要删除的学习记录主键
     * @return 结果
     */
    @Override
    public int deleteStudyRecordByRecordIds(Long[] recordIds)
    {
        return studyRecordMapper.deleteStudyRecordByRecordIds(recordIds);
    }

    /**
     * 删除学习记录信息
     * 
     * @param recordId 学习记录主键
     * @return 结果
     */
    @Override
    public int deleteStudyRecordByRecordId(Long recordId)
    {
        return studyRecordMapper.deleteStudyRecordByRecordId(recordId);
    }
    
    /**
     * 删除用户的所有学习记录
     * 
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public int deleteStudyRecordByUserId(Long userId)
    {
        return studyRecordMapper.deleteStudyRecordByUserId(userId);
    }
    
    /**
     * 删除资料的所有学习记录
     * 
     * @param materialId 资料ID
     * @return 结果
     */
    @Override
    public int deleteStudyRecordByMaterialId(Long materialId)
    {
        return studyRecordMapper.deleteStudyRecordByMaterialId(materialId);
    }
} 