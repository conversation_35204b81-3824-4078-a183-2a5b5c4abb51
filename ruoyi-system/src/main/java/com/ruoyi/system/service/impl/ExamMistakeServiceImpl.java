package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.ExamMistakeMapper;
import com.ruoyi.system.domain.ExamMistake;
import com.ruoyi.system.service.IExamMistakeService;
import com.ruoyi.common.utils.DateUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * 错题Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class ExamMistakeServiceImpl extends ServiceImpl<ExamMistakeMapper, ExamMistake> implements IExamMistakeService 
{
    @Autowired
    private ExamMistakeMapper examMistakeMapper;

    /**
     * 查询错题
     * 
     * @param mistakeId 错题主键
     * @return 错题
     */
    @Override
    public ExamMistake selectExamMistakeByMistakeId(Long mistakeId)
    {
        return examMistakeMapper.selectExamMistakeByMistakeId(mistakeId);
    }

    /**
     * 查询错题列表
     * 
     * @param examMistake 错题
     * @return 错题
     */
    @Override
    public List<ExamMistake> selectExamMistakeList(ExamMistake examMistake)
    {
        return examMistakeMapper.selectExamMistakeList(examMistake);
    }
    
    /**
     * 查询用户的错题列表
     * 
     * @param userId 用户ID
     * @return 错题集合
     */
    @Override
    public List<ExamMistake> selectExamMistakeListByUserId(Long userId)
    {
        return examMistakeMapper.selectExamMistakeListByUserId(userId);
    }
    
    /**
     * 根据用户ID和题目ID查询错题
     * 
     * @param userId 用户ID
     * @param questionId 题目ID
     * @return 错题
     */
    @Override
    public ExamMistake selectExamMistakeByUserIdAndQuestionId(Long userId, Long questionId)
    {
        return examMistakeMapper.selectExamMistakeByUserIdAndQuestionId(userId, questionId);
    }

    /**
     * 新增错题
     * 
     * @param examMistake 错题
     * @return 结果
     */
    @Override
    public int insertExamMistake(ExamMistake examMistake)
    {
        examMistake.setCreateTime(DateUtils.getNowDate());
        examMistake.setLastMistakeTime(DateUtils.getNowDate());
        return examMistakeMapper.insertExamMistake(examMistake);
    }

    /**
     * 修改错题
     * 
     * @param examMistake 错题
     * @return 结果
     */
    @Override
    public int updateExamMistake(ExamMistake examMistake)
    {
        examMistake.setUpdateTime(DateUtils.getNowDate());
        return examMistakeMapper.updateExamMistake(examMistake);
    }
    
    /**
     * 更新错题状态
     * 
     * @param examMistake 错题
     * @return 结果
     */
    @Override
    public int updateExamMistakeStatus(ExamMistake examMistake)
    {
        examMistake.setUpdateTime(DateUtils.getNowDate());
        return examMistakeMapper.updateExamMistakeStatus(examMistake);
    }

    /**
     * 批量删除错题
     * 
     * @param mistakeIds 需要删除的错题主键
     * @return 结果
     */
    @Override
    public int deleteExamMistakeByMistakeIds(Long[] mistakeIds)
    {
        return examMistakeMapper.deleteExamMistakeByMistakeIds(mistakeIds);
    }

    /**
     * 删除错题信息
     * 
     * @param mistakeId 错题主键
     * @return 结果
     */
    @Override
    public int deleteExamMistakeByMistakeId(Long mistakeId)
    {
        return examMistakeMapper.deleteExamMistakeByMistakeId(mistakeId);
    }
    
    /**
     * 删除用户的所有错题
     * 
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public int deleteExamMistakeByUserId(Long userId)
    {
        return examMistakeMapper.deleteExamMistakeByUserId(userId);
    }
} 