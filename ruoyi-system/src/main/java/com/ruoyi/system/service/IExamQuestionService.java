package com.ruoyi.system.service;

import java.util.List;
import java.io.InputStream;
import com.ruoyi.system.domain.ExamQuestion;
import com.ruoyi.system.domain.vo.ExamQuestionImportVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 题目Service接口
 * 
 * <AUTHOR>
 * @date 2023-09-01
 */
public interface IExamQuestionService extends IService<ExamQuestion>
{
    /**
     * 查询题目
     * 
     * @param questionId 题目ID
     * @return 题目
     */
    public ExamQuestion selectExamQuestionByQuestionId(Long questionId);

    /**
     * 查询题目列表
     * 
     * @param examQuestion 题目
     * @return 题目集合
     */
    public List<ExamQuestion> selectExamQuestionList(ExamQuestion examQuestion);

    /**
     * 根据题库套餐ID和题目类型查询题目
     * 
     * @param packageId 题库套餐ID
     * @param questionType 题目类型
     * @return 题目集合
     */
    public List<ExamQuestion> selectExamQuestionsByPackageId(Long packageId, String questionType);

    /**
     * 新增题目
     * 
     * @param examQuestion 题目
     * @return 结果
     */
    public int insertExamQuestion(ExamQuestion examQuestion);

    /**
     * 批量新增题目
     * 
     * @param examQuestions 题目列表
     * @return 结果
     */
    public int batchInsertExamQuestion(List<ExamQuestion> examQuestions);

    /**
     * 修改题目
     * 
     * @param examQuestion 题目
     * @return 结果
     */
    public int updateExamQuestion(ExamQuestion examQuestion);

    /**
     * 批量删除题目
     * 
     * @param questionIds 需要删除的题目ID
     * @return 结果
     */
    public int deleteExamQuestionByQuestionIds(Long[] questionIds);

    /**
     * 删除题目信息
     * 
     * @param questionId 题目ID
     * @return 结果
     */
    public int deleteExamQuestionByQuestionId(Long questionId);

    /**
     * 导入题目数据
     * 
     * @param questionList 题目数据列表
     * @param packageId 套餐ID
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    public String importExamQuestion(List<ExamQuestionImportVo> questionList, Long packageId, Boolean isUpdateSupport, String operName);

    /**
     * 导入题目数据
     * 
     * @param file 题目数据文件
     * @param packageId 套餐ID
     * @param updateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    public String importExamQuestion(InputStream file, Long packageId, boolean updateSupport, String operName);

    /**
     * 导入题目数据
     * 
     * @param questionList 题目数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    public String importQuestion(List<ExamQuestion> questionList, Boolean isUpdateSupport, String operName);

    List<String> getSubjectsByPackageId(Long packageId);
} 