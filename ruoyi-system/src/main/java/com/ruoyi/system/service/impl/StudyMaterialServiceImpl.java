package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.system.mapper.StudyMaterialMapper;
import com.ruoyi.system.mapper.StudyRecordMapper;
import com.ruoyi.system.domain.StudyMaterial;
import com.ruoyi.system.service.IStudyMaterialService;
import com.ruoyi.common.utils.DateUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * 学习资料Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class StudyMaterialServiceImpl extends ServiceImpl<StudyMaterialMapper, StudyMaterial> implements IStudyMaterialService 
{
    @Autowired
    private StudyMaterialMapper studyMaterialMapper;
    
    @Autowired
    private StudyRecordMapper studyRecordMapper;

    /**
     * 查询学习资料
     * 
     * @param materialId 学习资料主键
     * @return 学习资料
     */
    @Override
    public StudyMaterial selectStudyMaterialByMaterialId(Long materialId)
    {
        return studyMaterialMapper.selectStudyMaterialByMaterialId(materialId);
    }

    /**
     * 查询学习资料列表
     * 
     * @param studyMaterial 学习资料
     * @return 学习资料
     */
    @Override
    public List<StudyMaterial> selectStudyMaterialList(StudyMaterial studyMaterial)
    {
        return studyMaterialMapper.selectStudyMaterialList(studyMaterial);
    }
    
    /**
     * 查询套餐下的学习资料列表
     * 
     * @param packageId 套餐ID
     * @return 学习资料集合
     */
    @Override
    public List<StudyMaterial> selectStudyMaterialListByPackageId(Long packageId)
    {
        return studyMaterialMapper.selectStudyMaterialListByPackageId(packageId);
    }
    
    /**
     * 根据资料类型查询学习资料列表
     * 
     * @param materialType 资料类型（0电子书 1视频）
     * @return 学习资料集合
     */
    @Override
    public List<StudyMaterial> selectStudyMaterialListByType(String materialType)
    {
        return studyMaterialMapper.selectStudyMaterialListByType(materialType);
    }
    
    /**
     * 增加资料浏览次数
     * 
     * @param materialId 资料ID
     */
    @Override
    public void incrementViewCount(Long materialId)
    {
        studyMaterialMapper.updateViewCount(materialId);
    }
    
    /**
     * 增加资料下载次数
     * 
     * @param materialId 资料ID
     */
    @Override
    public void incrementDownloadCount(Long materialId)
    {
        studyMaterialMapper.updateDownloadCount(materialId);
    }

    /**
     * 新增学习资料
     * 
     * @param studyMaterial 学习资料
     * @return 结果
     */
    @Override
    public int insertStudyMaterial(StudyMaterial studyMaterial)
    {
        studyMaterial.setCreateTime(DateUtils.getNowDate());
        return studyMaterialMapper.insertStudyMaterial(studyMaterial);
    }

    /**
     * 修改学习资料
     * 
     * @param studyMaterial 学习资料
     * @return 结果
     */
    @Override
    public int updateStudyMaterial(StudyMaterial studyMaterial)
    {
        studyMaterial.setUpdateTime(DateUtils.getNowDate());
        return studyMaterialMapper.updateStudyMaterial(studyMaterial);
    }

    /**
     * 批量删除学习资料
     * 
     * @param materialIds 需要删除的学习资料主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteStudyMaterialByMaterialIds(Long[] materialIds)
    {
        // 删除相关的学习记录
        for (Long materialId : materialIds)
        {
            studyRecordMapper.deleteStudyRecordByMaterialId(materialId);
        }
        
        return studyMaterialMapper.deleteStudyMaterialByMaterialIds(materialIds);
    }

    /**
     * 删除学习资料信息
     * 
     * @param materialId 学习资料主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteStudyMaterialByMaterialId(Long materialId)
    {
        // 删除相关的学习记录
        studyRecordMapper.deleteStudyRecordByMaterialId(materialId);
        
        return studyMaterialMapper.deleteStudyMaterialByMaterialId(materialId);
    }
} 