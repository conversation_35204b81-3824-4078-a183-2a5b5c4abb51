package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.ExamPackageMapper;
import com.ruoyi.system.mapper.ExamPackageQuestionMapper;
import com.ruoyi.system.domain.ExamPackage;
import com.ruoyi.system.service.IExamPackageService;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.DateUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * 题库套餐Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class ExamPackageServiceImpl extends ServiceImpl<ExamPackageMapper, ExamPackage> implements IExamPackageService 
{
    @Autowired
    private ExamPackageMapper examPackageMapper;
    
    @Autowired
    private ExamPackageQuestionMapper examPackageQuestionMapper;

    /**
     * 查询题库套餐
     * 
     * @param packageId 题库套餐主键
     * @return 题库套餐
     */
    @Override
    public ExamPackage selectExamPackageByPackageId(Long packageId)
    {
        return examPackageMapper.selectExamPackageByPackageId(packageId);
    }

    /**
     * 查询题库套餐列表
     * 
     * @param examPackage 题库套餐
     * @return 题库套餐
     */
    @Override
    public List<ExamPackage> selectExamPackageList(ExamPackage examPackage)
    {
        return examPackageMapper.selectExamPackageList(examPackage);
    }

    /**
     * 新增题库套餐
     * 
     * @param examPackage 题库套餐
     * @return 结果
     */
    @Override
    public int insertExamPackage(ExamPackage examPackage)
    {
        examPackage.setCreateTime(DateUtils.getNowDate());
        return examPackageMapper.insertExamPackage(examPackage);
    }

    /**
     * 修改题库套餐
     * 
     * @param examPackage 题库套餐
     * @return 结果
     */
    @Override
    public int updateExamPackage(ExamPackage examPackage)
    {
        examPackage.setUpdateTime(DateUtils.getNowDate());
        return examPackageMapper.updateExamPackage(examPackage);
    }

    /**
     * 批量删除题库套餐
     * 
     * @param packageIds 需要删除的题库套餐主键
     * @return 结果
     */
    @Override
    public int deleteExamPackageByPackageIds(Long[] packageIds)
    {
        // 删除套餐题目关联关系
        for (Long packageId : packageIds)
        {
            examPackageQuestionMapper.deleteExamPackageQuestionByPackageId(packageId);
        }
        return examPackageMapper.deleteExamPackageByPackageIds(packageIds);
    }

    /**
     * 删除题库套餐信息
     * 
     * @param packageId 题库套餐主键
     * @return 结果
     */
    @Override
    public int deleteExamPackageByPackageId(Long packageId)
    {
        // 删除套餐题目关联关系
        examPackageQuestionMapper.deleteExamPackageQuestionByPackageId(packageId);
        return examPackageMapper.deleteExamPackageByPackageId(packageId);
    }
    
    /**
     * 查询所有题库套餐
     *
     * @return 题库套餐列表
     */
    @Override
    public List<ExamPackage> selectExamPackageAll()
    {
        return examPackageMapper.selectExamPackageAll();
    }
} 