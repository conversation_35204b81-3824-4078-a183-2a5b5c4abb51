package com.ruoyi.system.service.impl;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.ExamCategoryMapper;
import com.ruoyi.system.domain.ExamCategory;
import com.ruoyi.system.service.IExamCategoryService;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.DateUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * 题库分类Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class ExamCategoryServiceImpl extends ServiceImpl<ExamCategoryMapper, ExamCategory> implements IExamCategoryService 
{
    @Autowired
    private ExamCategoryMapper examCategoryMapper;

    /**
     * 查询题库分类
     * 
     * @param categoryId 题库分类主键
     * @return 题库分类
     */
    @Override
    public ExamCategory selectExamCategoryByCategoryId(Integer categoryId)
    {
        return examCategoryMapper.selectExamCategoryByCategoryId(categoryId);
    }

    /**
     * 查询题库分类列表
     * 
     * @param examCategory 题库分类
     * @return 题库分类
     */
    @Override
    public List<ExamCategory> selectExamCategoryList(ExamCategory examCategory)
    {
        return examCategoryMapper.selectExamCategoryList(examCategory);
    }

    /**
     * 构建前端所需要树结构
     * 
     * @param categories 题库分类列表
     * @return 树结构列表
     */
    @Override
    public List<ExamCategory> buildExamCategoryTree(List<ExamCategory> categories)
    {
        List<ExamCategory> returnList = new ArrayList<ExamCategory>();
        List<Long> tempList = new ArrayList<Long>();
        for (ExamCategory category : categories)
        {
            tempList.add(category.getCategoryId());
        }
        for (Iterator<ExamCategory> iterator = categories.iterator(); iterator.hasNext();)
        {
            ExamCategory category = (ExamCategory) iterator.next();
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(category.getParentId()))
            {
                recursionFn(categories, category);
                returnList.add(category);
            }
        }
        if (returnList.isEmpty())
        {
            returnList = categories;
        }
        return returnList;
    }

    /**
     * 递归列表
     */
    private void recursionFn(List<ExamCategory> list, ExamCategory t)
    {
        // 得到子节点列表
        List<ExamCategory> childList = getChildList(list, t);
        t.setChildren(childList);
        for (ExamCategory tChild : childList)
        {
            if (hasChild(list, tChild))
            {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<ExamCategory> getChildList(List<ExamCategory> list, ExamCategory t)
    {
        List<ExamCategory> tlist = new ArrayList<ExamCategory>();
        Iterator<ExamCategory> it = list.iterator();
        while (it.hasNext())
        {
            ExamCategory n = (ExamCategory) it.next();
            if (n.getParentId() != null && n.getParentId().longValue() == t.getCategoryId().longValue())
            {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<ExamCategory> list, ExamCategory t)
    {
        return getChildList(list, t).size() > 0;
    }

    /**
     * 新增题库分类
     * 
     * @param examCategory 题库分类
     * @return 结果
     */
    @Override
    public int insertExamCategory(ExamCategory examCategory)
    {
        examCategory.setCreateTime(DateUtils.getNowDate());
        return examCategoryMapper.insertExamCategory(examCategory);
    }

    /**
     * 修改题库分类
     * 
     * @param examCategory 题库分类
     * @return 结果
     */
    @Override
    public int updateExamCategory(ExamCategory examCategory)
    {
        examCategory.setUpdateTime(DateUtils.getNowDate());
        return examCategoryMapper.updateExamCategory(examCategory);
    }

    /**
     * 删除题库分类信息
     * 
     * @param categoryId 题库分类主键
     * @return 结果
     */
    @Override
    public int deleteExamCategoryByCategoryId(Long categoryId)
    {
        return examCategoryMapper.deleteExamCategoryByCategoryId(categoryId);
    }

    /**
     * 批量删除题库分类
     * 
     * @param categoryIds 需要删除的题库分类主键
     * @return 结果
     */
    @Override
    public int deleteExamCategoryByCategoryIds(Long[] categoryIds)
    {
        return examCategoryMapper.deleteExamCategoryByCategoryIds(categoryIds);
    }
    
    /**
     * 查询所有题库分类
     *
     * @return 题库分类列表
     */
    @Override
    public List<ExamCategory> selectExamCategoryAll()
    {
        return examCategoryMapper.selectExamCategoryAll();
    }
} 