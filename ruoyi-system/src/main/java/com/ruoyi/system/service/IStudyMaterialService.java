package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.StudyMaterial;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 学习资料服务接口
 * 
 * <AUTHOR>
 */
public interface IStudyMaterialService extends IService<StudyMaterial>
{
    /**
     * 查询学习资料
     * 
     * @param materialId 学习资料主键
     * @return 学习资料
     */
    public StudyMaterial selectStudyMaterialByMaterialId(Long materialId);

    /**
     * 查询学习资料列表
     * 
     * @param studyMaterial 学习资料
     * @return 学习资料集合
     */
    public List<StudyMaterial> selectStudyMaterialList(StudyMaterial studyMaterial);
    
    /**
     * 查询套餐下的学习资料列表
     * 
     * @param packageId 套餐ID
     * @return 学习资料集合
     */
    public List<StudyMaterial> selectStudyMaterialListByPackageId(Long packageId);
    
    /**
     * 根据资料类型查询学习资料列表
     * 
     * @param materialType 资料类型（0电子书 1视频）
     * @return 学习资料集合
     */
    public List<StudyMaterial> selectStudyMaterialListByType(String materialType);
    
    /**
     * 增加资料浏览次数
     * 
     * @param materialId 资料ID
     */
    public void incrementViewCount(Long materialId);
    
    /**
     * 增加资料下载次数
     * 
     * @param materialId 资料ID
     */
    public void incrementDownloadCount(Long materialId);

    /**
     * 新增学习资料
     * 
     * @param studyMaterial 学习资料
     * @return 结果
     */
    public int insertStudyMaterial(StudyMaterial studyMaterial);

    /**
     * 修改学习资料
     * 
     * @param studyMaterial 学习资料
     * @return 结果
     */
    public int updateStudyMaterial(StudyMaterial studyMaterial);

    /**
     * 批量删除学习资料
     * 
     * @param materialIds 需要删除的学习资料主键集合
     * @return 结果
     */
    public int deleteStudyMaterialByMaterialIds(Long[] materialIds);

    /**
     * 删除学习资料信息
     * 
     * @param materialId 学习资料主键
     * @return 结果
     */
    public int deleteStudyMaterialByMaterialId(Long materialId);
} 