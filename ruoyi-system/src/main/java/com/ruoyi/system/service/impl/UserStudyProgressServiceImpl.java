package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.domain.StudyMaterial;
import com.ruoyi.system.mapper.StudyMaterialMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.UserStudyProgressMapper;
import com.ruoyi.system.domain.UserStudyProgress;
import com.ruoyi.system.service.IUserStudyProgressService;

/**
 * 用户学习进度Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class UserStudyProgressServiceImpl implements IUserStudyProgressService 
{
    @Autowired
    private UserStudyProgressMapper userStudyProgressMapper;

    @Autowired
    private StudyMaterialMapper studyMaterialMapper;

    /**
     * 查询用户学习进度
     * 
     * @param progressId 用户学习进度主键
     * @return 用户学习进度
     */
    @Override
    public UserStudyProgress selectUserStudyProgressByProgressId(Long progressId)
    {
        return userStudyProgressMapper.selectUserStudyProgressByProgressId(progressId);
    }

    /**
     * 查询用户学习进度列表
     * 
     * @param userStudyProgress 用户学习进度
     * @return 用户学习进度
     */
    @Override
    public List<UserStudyProgress> selectUserStudyProgressList(UserStudyProgress userStudyProgress)
    {
        return userStudyProgressMapper.selectUserStudyProgressList(userStudyProgress);
    }

    /**
     * 根据用户ID和套餐ID查询学习进度
     * 
     * @param userId 用户ID
     * @param packageId 套餐ID
     * @return 用户学习进度
     */
    @Override
    public UserStudyProgress selectUserStudyProgressByUserAndPackage(Long userId, Long packageId)
    {
        return userStudyProgressMapper.selectUserStudyProgressByUserAndPackage(userId, packageId);
    }

    /**
     * 根据用户ID查询学习进度列表
     * 
     * @param userId 用户ID
     * @return 用户学习进度集合
     */
    @Override
    public List<UserStudyProgress> selectUserStudyProgressListByUserId(Long userId)
    {
        return userStudyProgressMapper.selectUserStudyProgressListByUserId(userId);
    }

    /**
     * 新增用户学习进度
     * 
     * @param userStudyProgress 用户学习进度
     * @return 结果
     */
    @Override
    public int insertUserStudyProgress(UserStudyProgress userStudyProgress)
    {
        userStudyProgress.setCreateTime(DateUtils.getNowDate());
        return userStudyProgressMapper.insertUserStudyProgress(userStudyProgress);
    }

    /**
     * 修改用户学习进度
     * 
     * @param userStudyProgress 用户学习进度
     * @return 结果
     */
    @Override
    public int updateUserStudyProgress(UserStudyProgress userStudyProgress)
    {
        userStudyProgress.setUpdateTime(DateUtils.getNowDate());
        return userStudyProgressMapper.updateUserStudyProgress(userStudyProgress);
    }

    /**
     * 批量删除用户学习进度
     * 
     * @param progressIds 需要删除的用户学习进度主键
     * @return 结果
     */
    @Override
    public int deleteUserStudyProgressByProgressIds(Long[] progressIds)
    {
        return userStudyProgressMapper.deleteUserStudyProgressByProgressIds(progressIds);
    }

    /**
     * 删除用户学习进度信息
     * 
     * @param progressId 用户学习进度主键
     * @return 结果
     */
    @Override
    public int deleteUserStudyProgressByProgressId(Long progressId)
    {
        return userStudyProgressMapper.deleteUserStudyProgressByProgressId(progressId);
    }

    /**
     * 更新用户学习进度
     * 根据传入的sortNum查找下一个学习资料的sortNum并更新
     * 
     * @param userId 用户ID
     * @param packageId 套餐ID
     * @param currentSortNum 当前完成的资料排序号
     * @return 结果
     */
    @Override
    public int updateUserStudyProgressBySortNum(Long userId, Long packageId, Integer currentSortNum)
    {
        // 1. 查询该套餐下所有学习资料，按sortNum正序排序
        StudyMaterial queryMaterial = new StudyMaterial();
        queryMaterial.setPackageId(packageId);
        queryMaterial.setStatus("0"); // 只查询正常状态的资料
        List<StudyMaterial> materialList = studyMaterialMapper.selectStudyMaterialListByPackageId(packageId);


        // 3. 查询是否已存在该用户的学习进度记录
        UserStudyProgress existingProgress = userStudyProgressMapper.selectUserStudyProgressByUserAndPackage(userId, packageId);

        if (existingProgress != null) {
            if(existingProgress.getSortNum()>currentSortNum){
                return 0;
            }
            // 更新现有记录
            existingProgress.setSortNum(currentSortNum);
            existingProgress.setUpdateTime(DateUtils.getNowDate());
            return userStudyProgressMapper.updateUserStudyProgress(existingProgress);
        } else {
            // 创建新记录
            UserStudyProgress newProgress = new UserStudyProgress();
            newProgress.setUserId(userId);
            newProgress.setPackageId(packageId);
            newProgress.setSortNum(currentSortNum);
            newProgress.setCreateTime(DateUtils.getNowDate());
            return userStudyProgressMapper.insertUserStudyProgress(newProgress);
        }
    }
}
