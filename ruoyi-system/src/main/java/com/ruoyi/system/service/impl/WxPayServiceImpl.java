package com.ruoyi.system.service.impl;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.github.binarywang.wxpay.bean.notify.WxPayNotifyResponse;
import com.github.binarywang.wxpay.bean.notify.WxPayOrderNotifyResult;
import com.github.binarywang.wxpay.bean.order.WxPayMpOrderResult;
import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderRequest;
import com.github.binarywang.wxpay.bean.result.WxPayOrderCloseResult;
import com.github.binarywang.wxpay.bean.result.WxPayOrderQueryResult;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import com.ruoyi.system.service.IWxPayService;

/**
 * 微信支付服务实现
 * 
 * <AUTHOR>
 */
@Service
public class WxPayServiceImpl implements IWxPayService {
    private static final Logger log = LoggerFactory.getLogger(WxPayServiceImpl.class);

    @Autowired
    private WxPayService wxPayService;

    /**
     * 创建微信支付订单
     * 
     * @param orderNo 订单编号
     * @param amount 金额（元）
     * @param body 商品描述
     * @param openid 用户openid
     * @return 支付参数
     * @throws WxPayException 支付异常
     */
    @Override
    public Map<String, String> createOrder(String orderNo, BigDecimal amount, String body, String openid) throws WxPayException {
        log.info("创建微信支付订单，订单号：{}，金额：{}元，商品描述：{}", orderNo, amount, body);
        
        // 微信支付金额单位为分，需要将元转换为分
        int amountFen = amount.multiply(new BigDecimal("100")).intValue();
        
        // 构建统一下单请求参数
        WxPayUnifiedOrderRequest request = WxPayUnifiedOrderRequest.newBuilder()
                .outTradeNo(orderNo)
                .totalFee(amountFen)
                .body(body)
                .openid(openid)
                .build();
        
        try {
            // 调用微信支付接口，获取支付结果
            WxPayMpOrderResult result = wxPayService.createOrder(request);
            
            // 返回前端所需的支付参数
            Map<String, String> payInfo = new HashMap<>();
            payInfo.put("appId", result.getAppId());
            payInfo.put("timeStamp", result.getTimeStamp());
            payInfo.put("nonceStr", result.getNonceStr());
            payInfo.put("package", result.getPackageValue());
            payInfo.put("signType", result.getSignType());
            payInfo.put("paySign", result.getPaySign());
            
            return payInfo;
        } catch (WxPayException e) {
            log.error("创建微信支付订单失败：{}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 查询订单
     * 
     * @param orderNo 订单编号
     * @return 订单查询结果
     * @throws WxPayException 支付异常
     */
    @Override
    public WxPayOrderQueryResult queryOrder(String orderNo) throws WxPayException {
        log.info("查询微信支付订单，订单号：{}", orderNo);
        
        try {
            return wxPayService.queryOrder(null, orderNo);
        } catch (WxPayException e) {
            log.error("查询微信支付订单失败：{}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 关闭订单
     * 
     * @param orderNo 订单编号
     * @return 操作结果
     * @throws WxPayException 支付异常
     */
    @Override
    public boolean closeOrder(String orderNo) throws WxPayException {
        log.info("关闭微信支付订单，订单号：{}", orderNo);
        
        try {
            WxPayOrderCloseResult result = wxPayService.closeOrder(orderNo);
            return "SUCCESS".equals(result.getResultCode());
        } catch (WxPayException e) {
            log.error("关闭微信支付订单失败：{}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 处理支付结果通知
     * 
     * @param xmlData 微信支付结果通知XML数据
     * @return 处理结果
     * @throws WxPayException 支付异常
     */
    @Override
    public WxPayOrderNotifyResult parseOrderNotifyResult(String xmlData) throws WxPayException {
        log.info("处理微信支付结果通知");
        
        try {
            return wxPayService.parseOrderNotifyResult(xmlData);
        } catch (WxPayException e) {
            log.error("解析微信支付结果通知失败：{}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 处理支付结果通知并返回成功响应
     * 
     * @param notifyResult 通知结果
     * @return 给微信的响应
     */
    @Override
    public String getSuccessNotifyResponse(WxPayOrderNotifyResult notifyResult) {
        return WxPayNotifyResponse.success("成功");
    }

    /**
     * 处理支付结果通知并返回成功响应
     * 
     * @return 给微信的响应
     */
    @Override
    public String getSuccessNotifyResponse() {
        return WxPayNotifyResponse.success("成功");
    }

    /**
     * 处理支付结果通知并返回错误响应
     * 
     * @param msg 错误信息
     * @return 给微信的响应
     */
    @Override
    public String getFailNotifyResponse(String msg) {
        return WxPayNotifyResponse.fail(msg);
    }
} 