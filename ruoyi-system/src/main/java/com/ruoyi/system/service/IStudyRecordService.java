package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.StudyRecord;

/**
 * 学习记录服务接口
 * 
 * <AUTHOR>
 */
public interface IStudyRecordService 
{
    /**
     * 查询学习记录
     * 
     * @param recordId 学习记录主键
     * @return 学习记录
     */
    public StudyRecord selectStudyRecordByRecordId(Long recordId);
    
    /**
     * 查询用户学习记录
     * 
     * @param userId 用户ID
     * @param materialId 资料ID
     * @return 学习记录
     */
    public StudyRecord selectStudyRecordByUserIdAndMaterialId(Long userId, Long materialId);

    /**
     * 查询学习记录列表
     * 
     * @param studyRecord 学习记录
     * @return 学习记录集合
     */
    public List<StudyRecord> selectStudyRecordList(StudyRecord studyRecord);
    
    /**
     * 查询用户的学习记录列表
     * 
     * @param userId 用户ID
     * @return 学习记录集合
     */
    public List<StudyRecord> selectStudyRecordListByUserId(Long userId);
    
    /**
     * 查询套餐下的学习记录列表
     * 
     * @param userId 用户ID
     * @param packageId 套餐ID
     * @return 学习记录集合
     */
    public List<StudyRecord> selectStudyRecordListByPackageId(Long userId, Long packageId);
    
    /**
     * 查询资料的学习记录列表
     * 
     * @param materialId 资料ID
     * @return 学习记录集合
     */
    public List<StudyRecord> selectStudyRecordListByMaterialId(Long materialId);
    
    /**
     * 更新学习进度
     * 
     * @param userId 用户ID
     * @param materialId 资料ID
     * @param packageId 套餐ID
     * @param materialType 资料类型
     * @param progress 学习进度
     * @param lastPosition 学习位置
     * @param studyTime 学习时长
     * @return 结果
     */
    public int updateProgress(Long userId, Long materialId, Long packageId, String materialType, 
            Integer progress, String lastPosition, Integer studyTime);

    /**
     * 新增学习记录
     * 
     * @param studyRecord 学习记录
     * @return 结果
     */
    public int insertStudyRecord(StudyRecord studyRecord);

    /**
     * 修改学习记录
     * 
     * @param studyRecord 学习记录
     * @return 结果
     */
    public int updateStudyRecord(StudyRecord studyRecord);

    /**
     * 批量删除学习记录
     * 
     * @param recordIds 需要删除的学习记录主键集合
     * @return 结果
     */
    public int deleteStudyRecordByRecordIds(Long[] recordIds);

    /**
     * 删除学习记录信息
     * 
     * @param recordId 学习记录主键
     * @return 结果
     */
    public int deleteStudyRecordByRecordId(Long recordId);
    
    /**
     * 删除用户的所有学习记录
     * 
     * @param userId 用户ID
     * @return 结果
     */
    public int deleteStudyRecordByUserId(Long userId);
    
    /**
     * 删除资料的所有学习记录
     * 
     * @param materialId 资料ID
     * @return 结果
     */
    public int deleteStudyRecordByMaterialId(Long materialId);
} 