package com.ruoyi.system.service.impl;

import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.WxUserMapper;
import com.ruoyi.system.domain.WxUser;
import com.ruoyi.system.service.IWxUserService;
import com.ruoyi.common.utils.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 微信用户Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class WxUserServiceImpl implements IWxUserService
{
    private static final Logger log = LoggerFactory.getLogger(WxUserServiceImpl.class);
    
    @Autowired
    private WxUserMapper wxUserMapper;

    /**
     * 查询微信用户
     * 
     * @param userId 微信用户主键
     * @return 微信用户
     */
    @Override
    public WxUser selectWxUserByUserId(Long userId)
    {
        return wxUserMapper.selectWxUserByUserId(userId);
    }

    @Override
    public WxUser selectWxUserByPhone(String  phone) {
        return wxUserMapper.selectWxUserByPhone(phone);
    }

    /**
     * 查询微信用户列表
     * 
     * @param wxUser 微信用户
     * @return 微信用户
     */
    @Override
    public List<WxUser> selectWxUserList(WxUser wxUser)
    {
        return wxUserMapper.selectWxUserList(wxUser);
    }
    
    /**
     * 通过openid查询微信用户
     * 
     * @param openid 微信openid
     * @return 微信用户
     */
    @Override
    public WxUser selectWxUserByOpenid(String openid)
    {
        return wxUserMapper.selectWxUserByOpenid(openid);
    }
    
    /**
     * 微信用户注册
     * 
     * @param wxUser 微信用户信息
     * @return 结果
     */
    @Override
    public int registerWxUser(WxUser wxUser)
    {
        wxUser.setCreateTime(DateUtils.getNowDate());
        wxUser.setLastLoginTime(DateUtils.getNowDate());
        return wxUserMapper.insertWxUser(wxUser);
    }
    
    /**
     * 微信用户登录
     * 
     * @param openid 微信openid
     * @param sessionKey 会话密钥
     * @return 微信用户
     */
    @Override
    public WxUser loginWxUser(String openid, String sessionKey)
    {
        WxUser wxUser = wxUserMapper.selectWxUserByOpenid(openid);
        if (wxUser != null)
        {
            // 更新登录信息
            WxUser updateUser = new WxUser();
            updateUser.setUserId(wxUser.getUserId());
            updateUser.setSessionKey(sessionKey);
            updateUser.setLastLoginTime(DateUtils.getNowDate());
            wxUserMapper.updateWxUserLoginInfo(updateUser);
            
            // 重新查询最新用户信息
            wxUser = wxUserMapper.selectWxUserByUserId(wxUser.getUserId());
        }
        return wxUser;
    }
    
    /**
     * 根据Token获取微信用户信息
     * 
     * @param token 登录令牌
     * @return 微信用户
     */
    @Override
    public WxUser getWxUserByToken(String token)
    {
        try {
            // 从Token中提取openid（假设Token的格式是包含openid的）
            // 在实际生产系统中，这里应该解析JWT令牌或者从其他令牌存储中查询
            // 这里仅做示例实现，请根据实际的Token格式和生成机制修改
            if (token.startsWith("Bearer ")) {
                token = token.substring(7);
                System.out.println("token:"+token);
            }
            
            // 这里假设token就是openid，实际项目中应该使用JWT或其他Token机制
            // 应根据实际情况修改此处的实现
            WxUser wxUser = selectWxUserByOpenid(token);
            
            return wxUser;
        } catch (Exception e) {
            log.error("解析Token失败：{}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 新增微信用户
     * 
     * @param wxUser 微信用户
     * @return 结果
     */
    @Override
    public int insertWxUser(WxUser wxUser)
    {
        wxUser.setCreateTime(DateUtils.getNowDate());
        return wxUserMapper.insertWxUser(wxUser);
    }

    /**
     * 修改微信用户
     * 
     * @param wxUser 微信用户
     * @return 结果
     */
    @Override
    public int updateWxUser(WxUser wxUser)
    {
        wxUser.setUpdateTime(DateUtils.getNowDate());
        return wxUserMapper.updateWxUser(wxUser);
    }
    
    /**
     * 更新微信用户登录信息
     * 
     * @param wxUser 微信用户
     * @return 结果
     */
    @Override
    public int updateWxUserLoginInfo(WxUser wxUser)
    {
        wxUser.setUpdateTime(DateUtils.getNowDate());
        return wxUserMapper.updateWxUserLoginInfo(wxUser);
    }

    /**
     * 批量删除微信用户
     * 
     * @param userIds 需要删除的微信用户主键
     * @return 结果
     */
    @Override
    public int deleteWxUserByUserIds(Long[] userIds)
    {
        return wxUserMapper.deleteWxUserByUserIds(userIds);
    }

    /**
     * 删除微信用户信息
     * 
     * @param userId 微信用户主键
     * @return 结果
     */
    @Override
    public int deleteWxUserByUserId(Long userId)
    {
        return wxUserMapper.deleteWxUserByUserId(userId);
    }




} 