package com.ruoyi.system.service.impl;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.system.mapper.UserPackageMapper;
import com.ruoyi.system.mapper.OrderInfoMapper;
import com.ruoyi.system.mapper.ExamPackageMapper;
import com.ruoyi.system.mapper.WxUserMapper;
import com.ruoyi.system.domain.UserPackage;
import com.ruoyi.system.domain.OrderInfo;
import com.ruoyi.system.domain.ExamPackage;
import com.ruoyi.system.domain.WxUser;
import com.ruoyi.system.domain.UserPackageImport;
import com.ruoyi.system.service.IUserPackageService;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.poi.ExcelUtil;

/**
 * 用户套餐Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class UserPackageServiceImpl implements IUserPackageService 
{
    @Autowired
    private UserPackageMapper userPackageMapper;
    
    @Autowired
    private OrderInfoMapper orderInfoMapper;
    
    @Autowired
    private ExamPackageMapper examPackageMapper;
    
    @Autowired
    private WxUserMapper wxUserMapper;

    /**
     * 查询用户套餐
     * 
     * @param userPackageId 用户套餐主键
     * @return 用户套餐
     */
    @Override
    public UserPackage selectUserPackageByUserPackageId(Long userPackageId)
    {
        return userPackageMapper.selectUserPackageByUserPackageId(userPackageId);
    }

    /**
     * 查询用户套餐列表
     * 
     * @param userPackage 用户套餐
     * @return 用户套餐
     */
    @Override
    public List<UserPackage> selectUserPackageList(UserPackage userPackage)
    {
        return userPackageMapper.selectUserPackageList(userPackage);
    }
    
    /**
     * 查询用户有效套餐
     * 
     * @param userId 用户ID
     * @return 用户套餐集合
     */
    @Override
    public List<UserPackage> selectUserPackageByUserId(Long userId)
    {
        return userPackageMapper.selectUserPackageByUserId(userId);
    }

    /**
     * 新增用户套餐
     * 
     * @param userPackage 用户套餐
     * @return 结果
     */
    @Override
    @Transactional
    public int insertUserPackage(UserPackage userPackage)
    {
        // 如果没有提供订单ID，则自动创建订单并支付
        if (userPackage.getOrderId() == null)
        {
            // 查询套餐信息
            ExamPackage examPackage = examPackageMapper.selectExamPackageByPackageId(userPackage.getPackageId());
            if (examPackage == null)
            {
                throw new ServiceException("套餐不存在");
            }
            
            // 生成订单编号
            String orderNo = generateOrderNo();
            
            // 创建订单对象
            OrderInfo orderInfo = new OrderInfo();
            orderInfo.setOrderNo(orderNo);
            orderInfo.setUserId(userPackage.getUserId());
            orderInfo.setPackageId(userPackage.getPackageId());
            orderInfo.setPackageName(examPackage.getPackageName());
            orderInfo.setAmount(examPackage.getPrice());
            orderInfo.setStatus("1"); // 已支付
            orderInfo.setPayChannel("import"); // 管理员创建
            orderInfo.setPayTime(DateUtils.getNowDate());
            
            // 计算过期时间（当前时间 + 有效期天数）
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(DateUtils.getNowDate());
            calendar.add(Calendar.DAY_OF_YEAR, examPackage.getValidityDays());
            orderInfo.setExpireTime(calendar.getTime());
            
            orderInfo.setCreateTime(DateUtils.getNowDate());
            
            // 插入订单记录
            orderInfoMapper.insertOrderInfo(orderInfo);
            
            // 设置订单ID
            userPackage.setOrderId(orderInfo.getOrderId());
        }
        
        userPackage.setCreateTime(DateUtils.getNowDate());
        return userPackageMapper.insertUserPackage(userPackage);
    }
    
    /**
     * 生成订单编号
     */
    private String generateOrderNo()
    {
        // 生成基于时间的订单编号：年月日时分秒+6位随机数
        String dateStr = DateUtils.dateTimeNow("yyyyMMddHHmmss");
        String randomStr = String.valueOf((int)((Math.random() * 9 + 1) * 100000));
        return "A" + dateStr + randomStr; // A前缀表示管理员创建
    }

    /**
     * 修改用户套餐
     * 
     * @param userPackage 用户套餐
     * @return 结果
     */
    @Override
    public int updateUserPackage(UserPackage userPackage)
    {
        userPackage.setUpdateTime(DateUtils.getNowDate());
        return userPackageMapper.updateUserPackage(userPackage);
    }

    /**
     * 批量删除用户套餐
     * 
     * @param userPackageIds 需要删除的用户套餐主键
     * @return 结果
     */
    @Override
    public int deleteUserPackageByUserPackageIds(Long[] userPackageIds)
    {
        return userPackageMapper.deleteUserPackageByUserPackageIds(userPackageIds);
    }

    /**
     * 删除用户套餐信息
     * 
     * @param userPackageId 用户套餐主键
     * @return 结果
     */
    @Override
    public int deleteUserPackageByUserPackageId(Long userPackageId)
    {
        return userPackageMapper.deleteUserPackageByUserPackageId(userPackageId);
    }
    
    /**
     * 从订单创建用户套餐
     * 
     * @param orderId 订单ID
     * @return 结果
     */
    @Override
    @Transactional
    public int createUserPackageFromOrder(Long orderId)
    {
        // 查询订单信息
        OrderInfo orderInfo = orderInfoMapper.selectOrderInfoByOrderId(orderId);
        if (orderInfo == null)
        {
            throw new ServiceException("订单不存在");
        }
        
        // 检查订单状态，只有已支付的订单才能创建套餐
        if (!"1".equals(orderInfo.getStatus()))
        {
            throw new ServiceException("订单未支付，无法创建套餐");
        }
        
        // 检查是否已经创建过套餐
        UserPackage existPackage = userPackageMapper.selectUserPackageByOrderId(orderId);
        if (existPackage != null)
        {
            return 1; // 已经创建过，直接返回成功
        }
        
        // 查询套餐信息
        ExamPackage examPackage = examPackageMapper.selectExamPackageByPackageId(orderInfo.getPackageId());
        if (examPackage == null)
        {
            throw new ServiceException("套餐不存在");
        }
        
        // 创建用户套餐
        UserPackage userPackage = new UserPackage();
        userPackage.setUserId(orderInfo.getUserId());
        userPackage.setPackageId(orderInfo.getPackageId());
        userPackage.setOrderId(orderId);
        
        // 设置开始时间和结束时间
        Date startTime = DateUtils.getNowDate();
        userPackage.setStartTime(startTime);
        
        // 计算结束时间（开始时间 + 有效期天数）
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startTime);
        calendar.add(Calendar.DAY_OF_YEAR, examPackage.getValidityDays());
        userPackage.setEndTime(calendar.getTime());
        
        // 设置状态为有效
        userPackage.setStatus("0");
        userPackage.setCreateTime(DateUtils.getNowDate());
        
        return userPackageMapper.insertUserPackage(userPackage);
    }
    
    /**
     * 更新过期状态
     * 
     * @return 结果
     */
    @Override
    public int updateExpiredStatus()
    {
        return userPackageMapper.updateExpiredStatus();
    }
    
    /**
     * 导入用户套餐数据
     * 
     * @param file Excel文件
     * @param packageId 默认套餐ID
     * @return 导入结果
     */
    @Override
    @Transactional
    public Map<String, Object> importUserPackage(MultipartFile file, Long packageId)
    {
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> errorList = new ArrayList<>();
        int successCount = 0;
        int failCount = 0;
        int totalCount = 0;
        
        try
        {
            // 解析Excel数据
            ExcelUtil<UserPackageImport> util = new ExcelUtil<>(UserPackageImport.class);
            List<UserPackageImport> importList = util.importExcel(file.getInputStream());
            totalCount = importList.size();
            
            // 检查默认套餐是否存在
            if (packageId != null)
            {
                ExamPackage defaultPackage = examPackageMapper.selectExamPackageByPackageId(packageId);
                if (defaultPackage == null)
                {
                    throw new ServiceException("默认套餐不存在");
                }
            }
            
            // 处理每条导入数据
            for (int i = 0; i < importList.size(); i++)
            {
                UserPackageImport importItem = importList.get(i);
                int rowNum = i + 2; // Excel行号从2开始（标题行为1）
                
                try
                {
                    // 验证必填字段
                    if (StringUtils.isEmpty(importItem.getPhone()))
                    {
                        throw new ServiceException("手机号不能为空");
                    }
                    
                    // 使用参数传入的套餐ID
                    if (packageId == null)
                    {
                        throw new ServiceException("套餐ID不能为空，请在界面上选择套餐");
                    }
                    
                    // 验证套餐是否存在
                    ExamPackage examPackage = examPackageMapper.selectExamPackageByPackageId(packageId);
                    if (examPackage == null)
                    {
                        throw new ServiceException("套餐ID " + packageId + " 不存在");
                    }
                    
                    // 查询或创建微信用户
                    WxUser wxUser = wxUserMapper.selectWxUserByPhone(importItem.getPhone());
                    if (wxUser == null)
                    {
                        wxUser = new WxUser();
                        wxUser.setPhone(importItem.getPhone());
                        wxUser.setNickName(importItem.getUsername());
                        wxUser.setStatus("0"); // 正常状态
                        wxUser.setCreateTime(DateUtils.getNowDate());
                        wxUser.setUserType("线下");
                        wxUserMapper.insertWxUser(wxUser);
                    }
                    
                    // 设置开始时间和结束时间
                    Date startTime = importItem.getStartTime();
                    Date endTime = importItem.getEndTime();
                    
                    // 如果未提供开始时间，则使用当前时间
                    if (startTime == null)
                    {
                        startTime = DateUtils.getNowDate();
                    }
                    
                    // 如果未提供结束时间，则根据套餐有效期计算
                    if (endTime == null)
                    {
                        Calendar calendar = Calendar.getInstance();
                        calendar.setTime(startTime);
                        calendar.add(Calendar.DAY_OF_YEAR, examPackage.getValidityDays());
                        endTime = calendar.getTime();
                    }
                    
                    // 创建订单
                    OrderInfo orderInfo = new OrderInfo();
                    orderInfo.setOrderNo(generateOrderNo());
                    orderInfo.setUserId(wxUser.getUserId());
                    orderInfo.setPackageId(packageId);
                    orderInfo.setPackageName(examPackage.getPackageName());
                    orderInfo.setAmount(examPackage.getPrice());
                    orderInfo.setStatus("1"); // 已支付
                    orderInfo.setPayChannel("import"); // 导入创建
                    orderInfo.setPayTime(DateUtils.getNowDate());
                    orderInfo.setExpireTime(endTime);
                    orderInfo.setCreateTime(DateUtils.getNowDate());
                    orderInfo.setRemark(importItem.getRemark());
                    orderInfoMapper.insertOrderInfo(orderInfo);
                    
                    // 创建用户套餐
                    UserPackage userPackage = new UserPackage();
                    userPackage.setUserId(wxUser.getUserId());
                    userPackage.setPackageId(packageId);
                    userPackage.setOrderId(orderInfo.getOrderId());
                    userPackage.setStartTime(startTime);
                    userPackage.setEndTime(endTime);
                    userPackage.setStatus("0"); // 有效状态
                    userPackage.setCreateTime(DateUtils.getNowDate());
                    userPackage.setRemark(importItem.getRemark());
                    userPackageMapper.insertUserPackage(userPackage);
                    
                    successCount++;
                }
                catch (Exception e)
                {
                    failCount++;
                    Map<String, Object> error = new HashMap<>();
                    error.put("rowNum", rowNum);
                    error.put("phone", importItem.getPhone());
                    error.put("errorMsg", e.getMessage());
                    errorList.add(error);
                }
            }
            
            result.put("total", totalCount);
            result.put("success", successCount);
            result.put("fail", failCount);
            result.put("errors", errorList);
            
            return result;
        }
        catch (Exception e)
        {
            throw new ServiceException("导入用户套餐失败：" + e.getMessage());
        }
    }
} 