package com.ruoyi.system.service;

import java.util.List;
import java.util.Map;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.system.domain.UserPackage;

/**
 * 用户套餐Service接口
 * 
 * <AUTHOR>
 */
public interface IUserPackageService 
{
    /**
     * 查询用户套餐
     * 
     * @param userPackageId 用户套餐主键
     * @return 用户套餐
     */
    public UserPackage selectUserPackageByUserPackageId(Long userPackageId);

    /**
     * 查询用户套餐列表
     * 
     * @param userPackage 用户套餐
     * @return 用户套餐集合
     */
    public List<UserPackage> selectUserPackageList(UserPackage userPackage);
    
    /**
     * 查询用户有效套餐
     * 
     * @param userId 用户ID
     * @return 用户套餐集合
     */
    public List<UserPackage> selectUserPackageByUserId(Long userId);

    /**
     * 新增用户套餐
     * 
     * @param userPackage 用户套餐
     * @return 结果
     */
    public int insertUserPackage(UserPackage userPackage);

    /**
     * 修改用户套餐
     * 
     * @param userPackage 用户套餐
     * @return 结果
     */
    public int updateUserPackage(UserPackage userPackage);

    /**
     * 批量删除用户套餐
     * 
     * @param userPackageIds 需要删除的用户套餐主键集合
     * @return 结果
     */
    public int deleteUserPackageByUserPackageIds(Long[] userPackageIds);

    /**
     * 删除用户套餐信息
     * 
     * @param userPackageId 用户套餐主键
     * @return 结果
     */
    public int deleteUserPackageByUserPackageId(Long userPackageId);
    
    /**
     * 从订单创建用户套餐
     * 
     * @param orderId 订单ID
     * @return 结果
     */
    public int createUserPackageFromOrder(Long orderId);
    
    /**
     * 更新过期状态
     * 
     * @return 结果
     */
    public int updateExpiredStatus();
    
    /**
     * 导入用户套餐数据
     * 
     * @param file Excel文件
     * @param packageId 默认套餐ID
     * @return 导入结果
     */
    public Map<String, Object> importUserPackage(MultipartFile file, Long packageId);
} 