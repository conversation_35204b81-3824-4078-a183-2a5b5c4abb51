package com.ruoyi.system.service.impl;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

import com.ruoyi.system.domain.ExamCategory;
import com.ruoyi.system.mapper.ExamCategoryMapper;
import com.ruoyi.system.util.MyBatisPlusUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.ExamPackageQuestion;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.ruoyi.system.domain.ExamQuestion;
import com.ruoyi.system.domain.vo.ExamQuestionImportVo;
import com.ruoyi.system.mapper.ExamPackageQuestionMapper;
import com.ruoyi.system.mapper.ExamQuestionMapper;
import com.ruoyi.system.service.IExamQuestionService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * 题目Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-09-01
 */
@Service
public class ExamQuestionServiceImpl extends ServiceImpl<ExamQuestionMapper, ExamQuestion> implements IExamQuestionService
{
    private static final Logger logger = LoggerFactory.getLogger(ExamQuestionServiceImpl.class);

    @Autowired
    private ExamQuestionMapper examQuestionMapper;
    
    @Autowired
    private ExamPackageQuestionMapper examPackageQuestionMapper;
    @Autowired
    private ExamCategoryMapper examCategoryMapper;
   

    /**
     * 查询题目
     * 
     * @param questionId 题目ID
     * @return 题目
     */
    @Override
    public ExamQuestion selectExamQuestionByQuestionId(Long questionId)
    {
        return examQuestionMapper.selectExamQuestionByQuestionId(questionId);
    }

    /**
     * 查询题目列表
     * 
     * @param examQuestion 题目
     * @return 题目
     */
    @Override
    public List<ExamQuestion> selectExamQuestionList(ExamQuestion examQuestion)
    {
        List<ExamQuestion> examQuestions = examQuestionMapper.selectExamQuestionList(examQuestion);
        examQuestions.forEach(x -> {
            if (x.getCategoryId() != null) {
                // 使用工具类安全调用
//                ExamCategory examCategory = MyBatisPlusUtil.safeSelectById(
//                    examCategoryMapper,
//                    x.getCategoryId().longValue(),
//                    "selectExamCategoryByCategoryId"
//                );
                ExamCategory examCategory =  examCategoryMapper.selectExamCategoryByCategoryId(x.getCategoryId());
                if (examCategory != null) {
                    x.setCategoryName(examCategory.getCategoryName());
                }
            }
        });
        return examQuestions;
    }

    /**
     * 新增题目
     * 
     * @param examQuestion 题目
     * @return 结果
     */
    @Override
    public int insertExamQuestion(ExamQuestion examQuestion)
    {
        examQuestion.setCreateTime(DateUtils.getNowDate());
        return examQuestionMapper.insertExamQuestion(examQuestion);
    }

    /**
     * 批量新增题目
     * 
     * @param examQuestions 题目列表
     * @return 结果
     */
    @Override
    public int batchInsertExamQuestion(List<ExamQuestion> examQuestions)
    {
        return examQuestionMapper.batchInsertExamQuestion(examQuestions);
    }

    /**
     * 修改题目
     * 
     * @param examQuestion 题目
     * @return 结果
     */
    @Override
    public int updateExamQuestion(ExamQuestion examQuestion)
    {
        examQuestion.setUpdateTime(DateUtils.getNowDate());
        return examQuestionMapper.updateExamQuestion(examQuestion);
    }

    /**
     * 批量删除题目
     * 
     * @param questionIds 需要删除的题目ID
     * @return 结果
     */
    @Override
    public int deleteExamQuestionByQuestionIds(Long[] questionIds)
    {
        // 删除套餐题目关联关系
        for (Long questionId : questionIds)
        {
            examPackageQuestionMapper.deleteExamPackageQuestionByQuestionId(questionId);
        }
        return examQuestionMapper.deleteExamQuestionByQuestionIds(questionIds);
    }

    /**
     * 删除题目信息
     * 
     * @param questionId 题目ID
     * @return 结果
     */
    @Override
    public int deleteExamQuestionByQuestionId(Long questionId)
    {
        // 删除套餐题目关联关系
        examPackageQuestionMapper.deleteExamPackageQuestionByQuestionId(questionId);
        return examQuestionMapper.deleteExamQuestionByQuestionId(questionId);
    }
    
    /**
     * 根据题库套餐ID和题目类型查询题目
     * 
     * @param packageId 题库套餐ID
     * @param questionType 题目类型
     * @return 题目集合
     */
    @Override
    public List<ExamQuestion> selectExamQuestionsByPackageId(Long packageId, String questionType)
    {
        return examQuestionMapper.selectExamQuestionsByPackageId(packageId, questionType);
    }
    
    /**
     * 导入题目数据
     * 
     * @param questionList 题目数据列表
     * @param packageId 套餐ID
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    public String importExamQuestion(List<ExamQuestionImportVo> questionList, Long packageId, Boolean isUpdateSupport, String operName)
    {
        if (StringUtils.isNull(questionList) || questionList.size() == 0)
        {
            throw new RuntimeException("导入题目数据不能为空！");
        }
        if (packageId == null)
        {
            throw new RuntimeException("请选择题库套餐！");
        }
        int successNum = 0;
        int failureNum = 0;
        int updateNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        List<ExamQuestion> insertList = new ArrayList<>();
        List<ExamQuestion> updateList = new ArrayList<>();
        List<ExamPackageQuestion> relationList = new ArrayList<>();

        // 处理题目数据
        for (ExamQuestionImportVo vo : questionList)
        {
            try
            {
                // 验证题目类型
                if (StringUtils.isEmpty(vo.getQuestionType()) || 
                    (!vo.getQuestionType().equals("1") && !vo.getQuestionType().equals("2") && !vo.getQuestionType().equals("3")))
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、题目类型错误");
                    continue;
                }
                
                // 验证题库类型
                if (vo.getCategoryId()==null)
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、题库类型错误");
                    continue;
                }

                // 检查是否需要更新操作
                ExamQuestion existingQuestion = null;
                if (isUpdateSupport && StringUtils.isNotEmpty(vo.getQuestionContent())) {
                    // 根据题目内容查找是否存在相同的题目
                    existingQuestion = examQuestionMapper.selectExamQuestionByContent(vo.getQuestionContent());
                }

                if (existingQuestion != null) {
                    // 更新现有题目
                    existingQuestion.setSubject(vo.getSubject());
                    existingQuestion.setQuestionType(vo.getQuestionType());
                    existingQuestion.setQuestionContent(vo.getQuestionContent());
                    existingQuestion.setOptionA(vo.getOptionA());
                    existingQuestion.setOptionB(vo.getOptionB());
                    existingQuestion.setOptionC(vo.getOptionC());
                    existingQuestion.setOptionD(vo.getOptionD());
                    existingQuestion.setOptionE(vo.getOptionE());
                    existingQuestion.setOptionF(vo.getOptionF());
                    existingQuestion.setCorrectAnswer(vo.getCorrectAnswer());
                    existingQuestion.setAnalysis(vo.getAnalysis());
                    existingQuestion.setUpdateBy(operName);
                    existingQuestion.setUpdateTime(DateUtils.getNowDate());
                    existingQuestion.setCategoryId(vo.getCategoryId());
                    existingQuestion.setIsMock(vo.getIsMock());
                    existingQuestion.setIsStudy(vo.getIsStudy());
                    updateList.add(existingQuestion);
                    updateNum++;

                    // 检查套餐关联关系是否存在
                    try {
                        ExamPackageQuestion existingRelation = examPackageQuestionMapper.selectByPackageIdAndQuestionId(packageId, existingQuestion.getQuestionId());
                        if (existingRelation == null) {
                            // 创建套餐与题目的关联关系
                            ExamPackageQuestion relation = new ExamPackageQuestion();
                            relation.setPackageId(packageId);
                            relation.setQuestionId(existingQuestion.getQuestionId());
                            relation.setQuestionType(vo.getQuestionType());
                            relationList.add(relation);
                        }
                    } catch (Exception e) {
                        // 如果查询关联关系失败，记录日志但不影响主流程
                        logger.warn("查询套餐题目关联关系失败，packageId: {}, questionId: {}", packageId, existingQuestion.getQuestionId());
                    }
                } else {
                    // 创建新题目对象
                    ExamQuestion question = new ExamQuestion();
                    question.setSubject(vo.getSubject());
                    question.setQuestionType(vo.getQuestionType());
                    question.setQuestionContent(vo.getQuestionContent());
                    question.setOptionA(vo.getOptionA());
                    question.setOptionB(vo.getOptionB());
                    question.setOptionC(vo.getOptionC());
                    question.setOptionD(vo.getOptionD());
                    question.setOptionE(vo.getOptionE());
                    question.setOptionF(vo.getOptionF());
                    question.setCorrectAnswer(vo.getCorrectAnswer());
                    question.setAnalysis(vo.getAnalysis());
                    question.setStatus("0");
                    question.setDelFlag("0");
                    question.setCreateBy(operName);
                    question.setCreateTime(DateUtils.getNowDate());
                    question.setUpdateBy(operName);
                    question.setUpdateTime(DateUtils.getNowDate());
                    question.setCategoryId(vo.getCategoryId());
                    question.setIsMock(vo.getIsMock());
                    question.setIsStudy(vo.getIsStudy());
                    insertList.add(question);
                    successNum++;
                }
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、导入题目失败：";
                failureMsg.append(msg + e.getMessage());
            }
        }
        
        // 批量更新题目
        if (updateList.size() > 0)
        {
            for (ExamQuestion question : updateList)
            {
                examQuestionMapper.updateExamQuestion(question);
            }
        }

        // 批量插入题目
        if (insertList.size() > 0)
        {
            examQuestionMapper.batchInsertExamQuestion(insertList);

            // 为新插入的题目创建套餐关联关系
            for (ExamQuestion question : insertList)
            {
                ExamPackageQuestion relation = new ExamPackageQuestion();
                relation.setPackageId(packageId);
                relation.setQuestionId(question.getQuestionId());
                relation.setQuestionType(question.getQuestionType());
                relationList.add(relation);
            }
        }

        // 批量插入关联关系
        if (relationList.size() > 0)
        {
            try {
                // 去重处理，避免重复关联
                List<ExamPackageQuestion> uniqueRelationList = new ArrayList<>();
                for (ExamPackageQuestion relation : relationList) {
                    boolean isDuplicate = false;
                    for (ExamPackageQuestion existing : uniqueRelationList) {
                        if (existing.getPackageId().equals(relation.getPackageId()) &&
                            existing.getQuestionId().equals(relation.getQuestionId())) {
                            isDuplicate = true;
                            break;
                        }
                    }
                    if (!isDuplicate) {
                        uniqueRelationList.add(relation);
                    }
                }

                if (uniqueRelationList.size() > 0) {
                    examPackageQuestionMapper.batchInsertExamPackageQuestion(uniqueRelationList);
                }
            } catch (Exception e) {
                // 如果批量插入失败，尝试逐个插入
                logger.warn("批量插入套餐题目关联关系失败，尝试逐个插入: {}", e.getMessage());
                insertRelationsOneByOne(relationList);
            }
        }
        
        if (successNum > 0 || updateNum > 0)
        {
            if (successNum > 0) {
                successMsg.append("成功导入新题目 " + successNum + " 条");
            }
            if (updateNum > 0) {
                if (successMsg.length() > 0) {
                    successMsg.append("，");
                }
                successMsg.append("成功更新题目 " + updateNum + " 条");
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "导入失败题目 " + failureNum + " 条，错误如下：");
            throw new RuntimeException(failureMsg.toString());
        }
        return successMsg.toString();
    }

    /**
     * 逐个插入关联关系，处理重复数据异常
     *
     * @param relationList 关联关系列表
     */
    private void insertRelationsOneByOne(List<ExamPackageQuestion> relationList) {
        for (ExamPackageQuestion relation : relationList) {
            try {
                // 先检查是否已存在
                ExamPackageQuestion existing = examPackageQuestionMapper.selectByPackageIdAndQuestionId(
                    relation.getPackageId(), relation.getQuestionId());
                if (existing == null) {
                    examPackageQuestionMapper.insertExamPackageQuestion(relation);
                }
            } catch (Exception e) {
                // 记录插入失败的关联关系，但不影响整体流程
                logger.warn("插入套餐题目关联关系失败，packageId: {}, questionId: {}, error: {}",
                    relation.getPackageId(), relation.getQuestionId(), e.getMessage());
            }
        }
    }
    
    /**
     * 导入题目数据
     * 
     * @param file 题目数据文件
     * @param packageId 套餐ID
     * @param updateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    public String importExamQuestion(InputStream file, Long packageId, boolean updateSupport, String operName)
    {
        try
        {
            ExcelUtil<ExamQuestionImportVo> util = new ExcelUtil<ExamQuestionImportVo>(ExamQuestionImportVo.class);
            List<ExamQuestionImportVo> questionList = util.importExcel(file);
            return importExamQuestion(questionList, packageId, updateSupport, operName);
        }
        catch (Exception e)
        {
            throw new RuntimeException("导入Excel数据失败，请检查Excel文件格式是否正确！", e);
        }
    }

    /**
     * 导入题目数据
     * 
     * @param questionList 题目数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    public String importQuestion(List<ExamQuestion> questionList, Boolean isUpdateSupport, String operName)
    {
        if (StringUtils.isNull(questionList) || questionList.size() == 0)
        {
            throw new RuntimeException("导入题目数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        
        for (ExamQuestion question : questionList)
        {
            try
            {
                // 验证是否存在这个题目
                ExamQuestion existQuestion = new ExamQuestion();
                existQuestion.setQuestionContent(question.getQuestionContent());
                List<ExamQuestion> existList = examQuestionMapper.selectExamQuestionList(existQuestion);
                
                if (StringUtils.isNull(existList) || existList.size() == 0)
                {
                    // 设置状态和删除标志
                    question.setStatus("0");
                    question.setDelFlag("0");
                    question.setCreateBy(operName);
                    question.setCreateTime(DateUtils.getNowDate());
                    this.insertExamQuestion(question);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、题目 " + question.getSubject() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    question.setQuestionId(existList.get(0).getQuestionId());
                    question.setUpdateBy(operName);
                    question.setUpdateTime(DateUtils.getNowDate());
                    this.updateExamQuestion(question);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、题目 " + question.getSubject() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、题目 " + question.getSubject() + " 已存在");
                }
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、题目 " + question.getSubject() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
            }
        }
        
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new RuntimeException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    @Override
    public List<String> getSubjectsByPackageId(Long packageId) {
        return examQuestionMapper.getSubjectsByPackageId(packageId);
    }
} 