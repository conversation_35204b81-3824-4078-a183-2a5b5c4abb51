package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.ExamCategory;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 题库分类Service接口
 * 
 * <AUTHOR>
 */
public interface IExamCategoryService extends IService<ExamCategory>
{
    /**
     * 查询题库分类
     * 
     * @param categoryId 题库分类主键
     * @return 题库分类
     */
    public ExamCategory selectExamCategoryByCategoryId(Integer categoryId);

    /**
     * 查询题库分类列表
     * 
     * @param examCategory 题库分类
     * @return 题库分类集合
     */
    public List<ExamCategory> selectExamCategoryList(ExamCategory examCategory);

    /**
     * 构建前端所需要树结构
     * 
     * @param categories 题库分类列表
     * @return 树结构列表
     */
    public List<ExamCategory> buildExamCategoryTree(List<ExamCategory> categories);

    /**
     * 新增题库分类
     * 
     * @param examCategory 题库分类
     * @return 结果
     */
    public int insertExamCategory(ExamCategory examCategory);

    /**
     * 修改题库分类
     * 
     * @param examCategory 题库分类
     * @return 结果
     */
    public int updateExamCategory(ExamCategory examCategory);

    /**
     * 删除题库分类信息
     * 
     * @param categoryId 题库分类主键
     * @return 结果
     */
    public int deleteExamCategoryByCategoryId(Long categoryId);

    /**
     * 批量删除题库分类
     * 
     * @param categoryIds 需要删除的题库分类主键集合
     * @return 结果
     */
    public int deleteExamCategoryByCategoryIds(Long[] categoryIds);
    
    /**
     * 查询所有题库分类
     *
     * @return 题库分类列表
     */
    public List<ExamCategory> selectExamCategoryAll();
} 