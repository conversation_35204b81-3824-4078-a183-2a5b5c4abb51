package com.ruoyi.system.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.system.mapper.OrderInfoMapper;
import com.ruoyi.system.mapper.ExamPackageMapper;
import com.ruoyi.system.domain.OrderInfo;
import com.ruoyi.system.domain.ExamPackage;
import com.ruoyi.system.service.IOrderInfoService;
import com.ruoyi.system.service.IUserPackageService;
import com.ruoyi.system.service.IWxPayService;
import com.github.binarywang.wxpay.bean.notify.WxPayOrderNotifyResult;
import com.github.binarywang.wxpay.bean.result.WxPayOrderQueryResult;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 订单Service业务层�?�理
 *
 * <AUTHOR>
 */
@Service
public class OrderInfoServiceImpl implements IOrderInfoService
{
    private static final Logger log = LoggerFactory.getLogger(OrderInfoServiceImpl.class);

    @Autowired
    private OrderInfoMapper orderInfoMapper;

    @Autowired
    private ExamPackageMapper examPackageMapper;

    @Autowired
    private IWxPayService wxPayService;

    @Autowired
    private IUserPackageService userPackageService;

    /**
     * 查�?��?�单
     *
     * @param orderId 订单主键
     * @return 订单
     */
    @Override
    public OrderInfo selectOrderInfoByOrderId(Long orderId)
    {
        return orderInfoMapper.selectOrderInfoByOrderId(orderId);
    }

    /**
     * 查�?��?�单
     *
     * @param orderNo 订单编号
     * @return 订单
     */
    @Override
    public OrderInfo selectOrderInfoByOrderNo(String orderNo)
    {
        return orderInfoMapper.selectOrderInfoByOrderNo(orderNo);
    }

    /**
     * 查�?��?�单列表
     *
     * @param orderInfo 订单
     * @return 订单
     */
    @Override
    public List<OrderInfo> selectOrderInfoList(OrderInfo orderInfo)
    {
        return orderInfoMapper.selectOrderInfoList(orderInfo);
    }

    /**
     * 查�?�用户的订单列表
     *
     * @param userId 用户ID
     * @return 订单集合
     */
    @Override
    public List<OrderInfo> selectOrderInfoListByUserId(OrderInfo orderInfo)
    {
        return orderInfoMapper.selectOrderInfoListByUserId(orderInfo);
    }

    /**
     * 创建订单
     *
     * @param userId 用户ID
     * @param packageId 套�?�ID
     * @return 创建的�?�单
     */
    @Override
    @Transactional
    public OrderInfo createOrder(Long userId, Long packageId)
    {
        // 查�?��?��?�信�?
        ExamPackage examPackage = examPackageMapper.selectExamPackageByPackageId(packageId);
        if (examPackage == null)
        {
            throw new ServiceException("套餐不存在");
        }

        // 检查�?��?�状�?
        if ("1".equals(examPackage.getStatus()))
        {
            throw new ServiceException("套餐已停用");
        }

        // 生成订单编号
        String orderNo = generateOrderNo();

        // 创建订单对象
        OrderInfo orderInfo = new OrderInfo();
        orderInfo.setOrderNo(orderNo);
        orderInfo.setUserId(userId);
        orderInfo.setPackageId(packageId);
        orderInfo.setPackageName(examPackage.getPackageName());
        orderInfo.setAmount(examPackage.getPrice());
        orderInfo.setStatus("0"); // 待支�?
        orderInfo.setCreateTime(DateUtils.getNowDate());

        // 插入订单记录
        orderInfoMapper.insertOrderInfo(orderInfo);

        // 返回创建好的订单
        return orderInfo;
    }

    /**
     * �?付�?�单
     *
     * @param orderNo 订单编号
     * @param payChannel �?付渠�?
     * @param transactionId 交易流水�?
     * @return 结果
     */
    @Override
    @Transactional
    public int payOrder(String orderNo, String payChannel, String transactionId)
    {
        // 查�?��?�单
        OrderInfo orderInfo = orderInfoMapper.selectOrderInfoByOrderNo(orderNo);
        if (orderInfo == null)
        {
            throw new ServiceException("订单不存�?");
        }

        // 检查�?�单状�?
        if (!"0".equals(orderInfo.getStatus()))
        {
            throw new ServiceException("订单状态不正确，无法支�?");
        }

        // 查�?��?��?�信�?，获取有效期天数
        ExamPackage examPackage = examPackageMapper.selectExamPackageByPackageId(orderInfo.getPackageId());
        if (examPackage == null)
        {
            throw new ServiceException("套�?�不存在");
        }

        // �?付时间和过期时间
        Date payTime = DateUtils.getNowDate();
        Date expireTime = DateUtils.addDays(payTime, examPackage.getValidityDays());

        // 更新订单为已�?付状�?
        OrderInfo updateOrder = new OrderInfo();
        updateOrder.setOrderId(orderInfo.getOrderId());
        updateOrder.setStatus("1"); // 已支�?
        updateOrder.setPayTime(payTime);
        updateOrder.setExpireTime(expireTime);
        updateOrder.setPayChannel(payChannel);
        updateOrder.setTransactionId(transactionId);
        updateOrder.setUpdateTime(payTime);

        return orderInfoMapper.updateOrderInfo(updateOrder);
    }

    /**
     * 创建�?信支付�?�单
     *
     * @param orderNo 订单编号
     * @param openid 用户openid
     * @return �?信支付参�?
     */
    @Override
    public Map<String, String> createWxPayOrder(String orderNo, String openid)
    {
        // 查�?��?�单
        OrderInfo orderInfo = orderInfoMapper.selectOrderInfoByOrderNo(orderNo);
        if (orderInfo == null)
        {
            throw new ServiceException("订单不存�?");
        }

        // 检查�?�单状�?
        if (!"0".equals(orderInfo.getStatus()))
        {
            throw new ServiceException("订单状态不正确，无法支�?");
        }

        try {
            // 调用�?信支付服务创建支付�?�单
            String body = "套�?�购�?-" + orderInfo.getPackageName();
            BigDecimal amount = orderInfo.getAmount();

            // 创建�?信支付�?�单并返回支付参�?
            return wxPayService.createOrder(orderNo, amount, body, openid);
        } catch (WxPayException e) {
            log.error("创建�?信支付�?�单失败：{}", e.getMessage(), e);
            throw new ServiceException("创建�?信支付�?�单失败�?" + e.getMessage());
        }
    }

    /**
     * 处理�?信支付通知
     *
     * @param notifyData 通知数据
     * @return 处理结果
     */
    @Override
    @Transactional
    public String handleWxPayNotify(String notifyData)
    {
        try {
            // 解析�?信支付通知
            WxPayOrderNotifyResult notifyResult = wxPayService.parseOrderNotifyResult(notifyData);

            // 获取订单�?
            String orderNo = notifyResult.getOutTradeNo();

            // 查�?��?�单
            OrderInfo orderInfo = orderInfoMapper.selectOrderInfoByOrderNo(orderNo);
            if (orderInfo == null) {
                log.error("�?信支付通知�?的�?�单不存�?：{}", orderNo);
                return wxPayService.getFailNotifyResponse("订单不存�?");
            }

            // 如果订单已经处理过，直接返回成功
            if ("1".equals(orderInfo.getStatus())) {
                return wxPayService.getSuccessNotifyResponse();
            }

            // 验证�?付金�?
            int wxPayAmount = notifyResult.getTotalFee();
            int orderAmount = orderInfo.getAmount().multiply(new BigDecimal("100")).intValue();
            if (wxPayAmount != orderAmount) {
                log.error("�?信支付通知金�?�不匹配，�?�单金�?�：{}分，实际�?付：{}�?", orderAmount, wxPayAmount);
                return wxPayService.getFailNotifyResponse("金�?�不匹配");
            }

            // 获取�?信支付交易号
            String transactionId = notifyResult.getTransactionId();

            // 更新订单状态为已支�?
            int result = payOrder(orderNo, "wechat", transactionId);
            if (result > 0) {
                try {
                    userPackageService.createUserPackageFromOrder(orderInfo.getOrderId());
                    log.info("成功创建用户套�?��?�录，�?�单号：{}", orderNo);
                } catch (Exception e) {
                    // 创建用户套�?��?�录失败，但不影响支付结�?
                    log.error("创建用户套�?��?�录失败：{}", e.getMessage(), e);
                }
                return wxPayService.getSuccessNotifyResponse();
            } else {
                return wxPayService.getFailNotifyResponse("订单状态更新失�?");
            }
        } catch (WxPayException e) {
            log.error("处理�?信支付通知失败：{}", e.getMessage(), e);
            return wxPayService.getFailNotifyResponse(e.getMessage());
        }
    }

    /**
     * 查�?�微信支付�?�单状�?
     *
     * @param orderNo 订单编号
     * @return �?付状�?
     */
    @Override
    public String queryWxPayOrderStatus(String orderNo)
    {
        // 查�?��?�单
        OrderInfo orderInfo = orderInfoMapper.selectOrderInfoByOrderNo(orderNo);
        if (orderInfo == null)
        {
            throw new ServiceException("订单不存�?");
        }

        // 如果订单已支付，直接返回已支付状�?
        if ("1".equals(orderInfo.getStatus()))
        {
            return "SUCCESS";
        }

        try {
            // 调用�?信支付API查�?��?�单状�?
            WxPayOrderQueryResult queryResult = wxPayService.queryOrder(orderNo);

            // 检查支付状�?
            if ("SUCCESS".equals(queryResult.getTradeState())) {
                // 如果�?信支付成功，但本地�?�单状态未更新，则更新�?地�?�单状�?
                if (!"1".equals(orderInfo.getStatus())) {
                    payOrder(orderNo, "wechat", queryResult.getTransactionId());

                    // �?付成功后，创建用户�?��?��?�录
                    try {
                        userPackageService.createUserPackageFromOrder(orderInfo.getOrderId());
                        log.info("成功创建用户套�?��?�录，�?�单号：{}", orderNo);
                    } catch (Exception e) {
                        // 创建用户套�?��?�录失败，但不影响支付结�?
                        log.error("创建用户套�?��?�录失败：{}", e.getMessage(), e);
                    }
                }
                return "SUCCESS";
            } else {
                return queryResult.getTradeState();
            }
        } catch (WxPayException e) {
            log.error("查�?�微信支付�?�单状态失败：{}", e.getMessage(), e);
            throw new ServiceException("查�?�微信支付�?�单状态失�?");
        }
    }

    /**
     * 取消订单
     *
     * @param orderNo 订单编号
     * @return 结果
     */
    @Override
    public int cancelOrder(String orderNo)
    {
        // 查�?��?�单
        OrderInfo orderInfo = orderInfoMapper.selectOrderInfoByOrderNo(orderNo);
        if (orderInfo == null)
        {
            throw new ServiceException("订单不存�?");
        }

        // 检查�?�单状�?
        if (!"0".equals(orderInfo.getStatus()))
        {
            throw new ServiceException("�?有待�?付状态的订单才能取消");
        }

        try {
            // 如果�?�?信支付，尝试关闭�?信支付�?�单
            wxPayService.closeOrder(orderNo);
        } catch (WxPayException e) {
            // 关闭�?信支付�?�单失败，但仍然�?以继�?取消�?地�?�单
            log.error("关闭�?信支付�?�单失败：{}", e.getMessage(), e);
        }

        // 更新订单为已取消状�?
        OrderInfo updateOrder = new OrderInfo();
        updateOrder.setOrderId(orderInfo.getOrderId());
        updateOrder.setStatus("2"); // 已取�?
        updateOrder.setUpdateTime(DateUtils.getNowDate());

        return orderInfoMapper.updateOrderStatus(updateOrder);
    }

    /**
     * ��������
     *
     * @param orderInfo ����
     * @return ���
     */
    @Override
    public int insertOrderInfo(OrderInfo orderInfo)
    {
        // ���������Ϊ�գ����Զ����ɶ�����
        if (orderInfo.getOrderNo() == null || orderInfo.getOrderNo().isEmpty())
        {
            orderInfo.setOrderNo(generateOrderNo());
        }
        orderInfo.setCreateTime(DateUtils.getNowDate());
        return orderInfoMapper.insertOrderInfo(orderInfo);
    }

    /**
     * �?改�?�单
     *
     * @param orderInfo 订单
     * @return 结果
     */
    @Override
    public int updateOrderInfo(OrderInfo orderInfo)
    {
        orderInfo.setUpdateTime(DateUtils.getNowDate());
        return orderInfoMapper.updateOrderInfo(orderInfo);
    }

    /**
     * 批量删除订单
     *
     * @param orderIds 需要删除的订单主键
     * @return 结果
     */
    @Override
    public int deleteOrderInfoByOrderIds(Long[] orderIds)
    {
        return orderInfoMapper.deleteOrderInfoByOrderIds(orderIds);
    }

    /**
     * 删除订单信息
     *
     * @param orderId 订单主键
     * @return 结果
     */
    @Override
    public int deleteOrderInfoByOrderId(Long orderId)
    {
        return orderInfoMapper.deleteOrderInfoByOrderId(orderId);
    }

    /**
     * 生成订单编号
     */
    private String generateOrderNo()
    {
        // 生成基于时间的�?�单编号：年月日时分�?+6位随机数
        String dateStr = DateUtils.dateTimeNow("yyyyMMddHHmmss");
        String randomStr = String.valueOf((int)((Math.random() * 9 + 1) * 100000));
        return dateStr + randomStr;
    }
} 