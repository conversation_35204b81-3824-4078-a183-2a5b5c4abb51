package com.ruoyi.system.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.mapper.OrderInfoMapper;
import com.ruoyi.system.mapper.WxUserMapper;
import com.ruoyi.system.service.IStatisticsService;

/**
 * 数据统计Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class StatisticsServiceImpl implements IStatisticsService 
{
    @Autowired
    private WxUserMapper wxUserMapper;
    
    @Autowired
    private OrderInfoMapper orderInfoMapper;

    /**
     * 获取首页统计数据
     * 
     * @return 统计数据
     */
    @Override
    public Map<String, Object> getDashboardStatistics() 
    {
        Map<String, Object> result = new HashMap<>();
        
        // 1. 平台用户总数
        int userCount = wxUserMapper.countTotalUsers();
        result.put("userCount", userCount);
        
        // 2. 今日订单数
        Date today = DateUtils.getNowDate();
        String dateStr = DateUtils.dateTimeNow("yyyy-MM-dd");
        int todayOrderCount = orderInfoMapper.countTodayOrders(dateStr);
        result.put("todayOrderCount", todayOrderCount);
        
        // 3. 今日营业额
        BigDecimal todayRevenue = orderInfoMapper.sumTodayRevenue(dateStr);
        result.put("todayRevenue", todayRevenue != null ? todayRevenue : BigDecimal.ZERO);
        
        // 4. 总营业额
        BigDecimal totalRevenue = orderInfoMapper.sumTotalRevenue();
        result.put("totalRevenue", totalRevenue != null ? totalRevenue : BigDecimal.ZERO);
        
        return result;
    }

    /**
     * 获取营业额趋势数据（最近7天）
     * 
     * @return 趋势数据
     */
    @Override
    public Map<String, Object> getRevenueTrend() 
    {
        Map<String, Object> result = new HashMap<>();
        
        // 准备日期和收入数据列表
        List<String> dateList = new ArrayList<>();
        List<BigDecimal> revenueList = new ArrayList<>();
        
        // 获取最近7天的日期
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(DateUtils.getNowDate());
        calendar.add(Calendar.DAY_OF_YEAR, -6); // 从6天前开始，加上今天共7天
        
        for (int i = 0; i < 7; i++) {
            // 格式化日期
            String dateStr = DateUtils.parseDateToStr("yyyy-MM-dd", calendar.getTime());
            dateList.add(dateStr);
            
            // 获取当天营业额
            BigDecimal dayRevenue = orderInfoMapper.sumDayRevenue(dateStr);
            revenueList.add(dayRevenue != null ? dayRevenue : BigDecimal.ZERO);
            
            // 日期加1
            calendar.add(Calendar.DAY_OF_YEAR, 1);
        }
        
        result.put("dateList", dateList);
        result.put("revenueList", revenueList);
        
        return result;
    }

    /**
     * 获取指定时间段营业额统计
     *
     * @param startDate 开始日期（格式：yyyy-MM-dd）
     * @param endDate 结束日期（格式：yyyy-MM-dd）
     * @return 统计数据
     */
    @Override
    public Map<String, Object> getCustomRangeRevenue(String startDate, String endDate)
    {
        Map<String, Object> result = new HashMap<>();

        // 获取指定时间段的营业额
        BigDecimal revenue = orderInfoMapper.sumCustomRangeRevenue(startDate, endDate);
        result.put("revenue", revenue != null ? revenue : BigDecimal.ZERO);

        // 获取指定时间段的订单数量
        int orderCount = orderInfoMapper.countCustomRangeOrders(startDate, endDate);
        result.put("orderCount", orderCount);

        return result;
    }

    /**
     * 获取首页按渠道统计数据
     *
     * @return 按渠道统计数据
     */
    @Override
    public Map<String, Object> getDashboardChannelStatistics()
    {
        Map<String, Object> result = new HashMap<>();

        // 1. 平台用户总数
        int userCount = wxUserMapper.countTotalUsers();
        result.put("userCount", userCount);

        String dateStr = DateUtils.dateTimeNow("yyyy-MM-dd");
        String yearMonth = DateUtils.dateTimeNow("yyyy-MM");

        // 2. 本月线下用户数（原：今日线上订单数）
        int monthOfflineUserCount = wxUserMapper.countMonthUsersByType("线下", yearMonth);
        result.put("todayOnlineOrderCount", monthOfflineUserCount);

        // 3. 本月线上用户数（原：今日线下订单数）
        int monthOnlineUserCount = wxUserMapper.countMonthUsersByType("线上", yearMonth);
        result.put("todayOfflineOrderCount", monthOnlineUserCount);

        // 4. 今日线上营业额（微信支付）
        BigDecimal todayOnlineRevenue = orderInfoMapper.sumTodayOnlineRevenue(dateStr);
        result.put("todayOnlineRevenue", todayOnlineRevenue != null ? todayOnlineRevenue : BigDecimal.ZERO);

        // 5. 本月线上营业额（原：今日线下营业额）
        String startDate = yearMonth + "-01";
        String endDate = DateUtils.dateTimeNow("yyyy-MM-dd");
        BigDecimal monthOnlineRevenue = orderInfoMapper.sumCustomRangeOnlineRevenue(startDate, endDate);
        result.put("todayOfflineRevenue", monthOnlineRevenue != null ? monthOnlineRevenue : BigDecimal.ZERO);

        // 6. 总线上营业额（微信支付）
        BigDecimal totalOnlineRevenue = orderInfoMapper.sumTotalOnlineRevenue();
        result.put("totalOnlineRevenue", totalOnlineRevenue != null ? totalOnlineRevenue : BigDecimal.ZERO);

        // 7. 线下用户数（原：总线下营业额）
        int offlineUserCount = wxUserMapper.countUsersByType("线下");
        result.put("totalOfflineRevenue", offlineUserCount);

        return result;
    }

    /**
     * 获取指定时间段按渠道营业额统计
     *
     * @param startDate 开始日期（格式：yyyy-MM-dd）
     * @param endDate 结束日期（格式：yyyy-MM-dd）
     * @return 按渠道统计数据
     */
    @Override
    public Map<String, Object> getCustomRangeChannelRevenue(String startDate, String endDate)
    {
        Map<String, Object> result = new HashMap<>();

        // 获取指定时间段的线上营业额（微信支付）
        BigDecimal onlineRevenue = orderInfoMapper.sumCustomRangeOnlineRevenue(startDate, endDate);
        result.put("onlineRevenue", onlineRevenue != null ? onlineRevenue : BigDecimal.ZERO);

        // 获取指定时间段的线下营业额（后台新增）
        BigDecimal offlineRevenue = orderInfoMapper.sumCustomRangeOfflineRevenue(startDate, endDate);
        result.put("offlineRevenue", offlineRevenue != null ? offlineRevenue : BigDecimal.ZERO);

        // 获取指定时间段的线上订单数量（微信支付）
        int onlineOrderCount = orderInfoMapper.countCustomRangeOnlineOrders(startDate, endDate);
        result.put("onlineOrderCount", onlineOrderCount);

        // 获取指定时间段的线下订单数量（后台新增）
        int offlineOrderCount = orderInfoMapper.countCustomRangeOfflineOrders(startDate, endDate);
        result.put("offlineOrderCount", offlineOrderCount);

        return result;
    }

    /**
     * 获取按渠道营业额趋势数据（最近7天）
     *
     * @return 按渠道趋势数据
     */
    @Override
    public Map<String, Object> getChannelRevenueTrend()
    {
        Map<String, Object> result = new HashMap<>();

        // 准备日期和收入数据列表
        List<String> dateList = new ArrayList<>();
        List<BigDecimal> onlineRevenueList = new ArrayList<>();
        List<BigDecimal> offlineRevenueList = new ArrayList<>();

        // 获取最近7天的日期
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(DateUtils.getNowDate());
        calendar.add(Calendar.DAY_OF_YEAR, -6); // 从6天前开始，加上今天共7天

        for (int i = 0; i < 7; i++) {
            // 格式化日期
            String dateStr = DateUtils.parseDateToStr("yyyy-MM-dd", calendar.getTime());
            dateList.add(dateStr);

            // 获取当天线上营业额（微信支付）
            BigDecimal dayOnlineRevenue = orderInfoMapper.sumDayOnlineRevenue(dateStr);
            onlineRevenueList.add(dayOnlineRevenue != null ? dayOnlineRevenue : BigDecimal.ZERO);

            // 获取当天线下营业额（后台新增）
            BigDecimal dayOfflineRevenue = orderInfoMapper.sumDayOfflineRevenue(dateStr);
            offlineRevenueList.add(dayOfflineRevenue != null ? dayOfflineRevenue : BigDecimal.ZERO);

            // 日期加1
            calendar.add(Calendar.DAY_OF_YEAR, 1);
        }

        result.put("dateList", dateList);
        result.put("onlineRevenueList", onlineRevenueList);
        result.put("offlineRevenueList", offlineRevenueList);

        return result;
    }

    /**
     * 获取按套餐统计的订单数量
     *
     * @return 套餐订单统计数据
     */
    @Override
    public Map<String, Object> getPackageOrderStatistics()
    {
        Map<String, Object> result = new HashMap<>();

        // 获取各套餐的订单统计
        List<Map<String, Object>> packageStats = orderInfoMapper.countOrdersByPackage();

        // 准备数据列表
        List<String> packageNames = new ArrayList<>();
        List<Integer> orderCounts = new ArrayList<>();

        // 定义四种套餐的标准名称（用于匹配和排序）
        String[] standardPackages = {"中级维保","中级监控","高级维保","高级监控", "初级"};

        // 按标准顺序整理数据
        for (String standardName : standardPackages) {
            boolean found = false;
            for (Map<String, Object> stat : packageStats) {
                String packageName = (String) stat.get("packageName");
                if (packageName != null && packageName.contains(standardName)) {
                    if(packageNames.contains(standardName)){
                        orderCounts.set(packageNames.indexOf(standardName), ((Number) stat.get("orderCount")).intValue() + orderCounts.get(packageNames.indexOf(standardName)));
                    }else{
                        packageNames.add(standardName);
                        orderCounts.add(((Number) stat.get("orderCount")).intValue());
                    }
                    found = true;
//                    break;
                }
            }
            // 如果没有找到对应套餐的订单，添加0
            if (!found) {
                packageNames.add(standardName);
                orderCounts.add(0);
            }
        }

        result.put("packageNames", packageNames);
        result.put("orderCounts", orderCounts);
        result.put("rawData", packageStats); // 原始数据，用于调试

        return result;
    }
}