package com.ruoyi.system.service;

import java.math.BigDecimal;
import java.util.Map;

import com.github.binarywang.wxpay.bean.notify.WxPayNotifyResponse;
import com.github.binarywang.wxpay.bean.notify.WxPayOrderNotifyResult;
import com.github.binarywang.wxpay.bean.result.WxPayOrderQueryResult;
import com.github.binarywang.wxpay.exception.WxPayException;

/**
 * 微信支付服务接口
 * 
 * <AUTHOR>
 */
public interface IWxPayService {
    
    /**
     * 创建微信支付订单
     * 
     * @param orderNo 订单编号
     * @param amount 金额（元）
     * @param body 商品描述
     * @param openid 用户openid
     * @return 支付参数
     * @throws WxPayException 支付异常
     */
    public Map<String, String> createOrder(String orderNo, BigDecimal amount, String body, String openid) throws WxPayException;
    
    /**
     * 查询订单
     * 
     * @param orderNo 订单编号
     * @return 订单查询结果
     * @throws WxPayException 支付异常
     */
    public WxPayOrderQueryResult queryOrder(String orderNo) throws WxPayException;
    
    /**
     * 关闭订单
     * 
     * @param orderNo 订单编号
     * @return 操作结果
     * @throws WxPayException 支付异常
     */
    public boolean closeOrder(String orderNo) throws WxPayException;
    
    /**
     * 处理支付结果通知
     * 
     * @param xmlData 微信支付结果通知XML数据
     * @return 处理结果
     * @throws WxPayException 支付异常
     */
    public WxPayOrderNotifyResult parseOrderNotifyResult(String xmlData) throws WxPayException;
    
    /**
     * 处理支付结果通知并返回成功响应
     * 
     * @param notifyResult 通知结果
     * @return 给微信的响应
     */
    public String getSuccessNotifyResponse(WxPayOrderNotifyResult notifyResult);
    
    /**
     * 处理支付结果通知并返回成功响应
     * 
     * @return 给微信的响应
     */
    public String getSuccessNotifyResponse();
    
    /**
     * 处理支付结果通知并返回错误响应
     * 
     * @param msg 错误信息
     * @return 给微信的响应
     */
    public String getFailNotifyResponse(String msg);
} 