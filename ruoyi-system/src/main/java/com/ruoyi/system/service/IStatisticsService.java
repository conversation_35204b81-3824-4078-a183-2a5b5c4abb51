package com.ruoyi.system.service;

import java.util.Map;

/**
 * 数据统计Service接口
 * 
 * <AUTHOR>
 */
public interface IStatisticsService 
{
    /**
     * 获取首页统计数据
     * 
     * @return 统计数据
     */
    public Map<String, Object> getDashboardStatistics();
    
    /**
     * 获取营业额趋势数据（最近7天）
     * 
     * @return 趋势数据
     */
    public Map<String, Object> getRevenueTrend();
    
    /**
     * 获取指定时间段营业额统计
     *
     * @param startDate 开始日期（格式：yyyy-MM-dd）
     * @param endDate 结束日期（格式：yyyy-MM-dd）
     * @return 统计数据
     */
    public Map<String, Object> getCustomRangeRevenue(String startDate, String endDate);

    /**
     * 获取首页按渠道统计数据
     *
     * @return 按渠道统计数据
     */
    public Map<String, Object> getDashboardChannelStatistics();

    /**
     * 获取指定时间段按渠道营业额统计
     *
     * @param startDate 开始日期（格式：yyyy-MM-dd）
     * @param endDate 结束日期（格式：yyyy-MM-dd）
     * @return 按渠道统计数据
     */
    public Map<String, Object> getCustomRangeChannelRevenue(String startDate, String endDate);

    /**
     * 获取按渠道营业额趋势数据（最近7天）
     *
     * @return 按渠道趋势数据
     */
    public Map<String, Object> getChannelRevenueTrend();

    /**
     * 获取按套餐统计的订单数量
     *
     * @return 套餐订单统计数据
     */
    public Map<String, Object> getPackageOrderStatistics();
} 