package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.ExamPackage;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 题库套餐Service接口
 * 
 * <AUTHOR>
 */
public interface IExamPackageService extends IService<ExamPackage>
{
    /**
     * 查询题库套餐
     * 
     * @param packageId 题库套餐主键
     * @return 题库套餐
     */
    public ExamPackage selectExamPackageByPackageId(Long packageId);

    /**
     * 查询题库套餐列表
     * 
     * @param examPackage 题库套餐
     * @return 题库套餐集合
     */
    public List<ExamPackage> selectExamPackageList(ExamPackage examPackage);

    /**
     * 新增题库套餐
     * 
     * @param examPackage 题库套餐
     * @return 结果
     */
    public int insertExamPackage(ExamPackage examPackage);

    /**
     * 修改题库套餐
     * 
     * @param examPackage 题库套餐
     * @return 结果
     */
    public int updateExamPackage(ExamPackage examPackage);

    /**
     * 批量删除题库套餐
     * 
     * @param packageIds 需要删除的题库套餐主键集合
     * @return 结果
     */
    public int deleteExamPackageByPackageIds(Long[] packageIds);

    /**
     * 删除题库套餐信息
     * 
     * @param packageId 题库套餐主键
     * @return 结果
     */
    public int deleteExamPackageByPackageId(Long packageId);
    
    /**
     * 查询所有题库套餐
     *
     * @return 题库套餐列表
     */
    public List<ExamPackage> selectExamPackageAll();
}