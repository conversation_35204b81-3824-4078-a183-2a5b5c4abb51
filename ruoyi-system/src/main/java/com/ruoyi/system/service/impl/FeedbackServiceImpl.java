package com.ruoyi.system.service.impl;

import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.mapper.FeedbackMapper;
import com.ruoyi.system.domain.Feedback;
import com.ruoyi.system.service.IFeedbackService;

/**
 * 意见反馈服务实现
 * 
 * <AUTHOR>
 */
@Service
public class FeedbackServiceImpl implements IFeedbackService 
{
    @Autowired
    private FeedbackMapper feedbackMapper;

    /**
     * 查询反馈信息
     * 
     * @param feedbackId 反馈ID
     * @return 反馈信息
     */
    @Override
    public Feedback selectFeedbackByFeedbackId(Long feedbackId)
    {
        return feedbackMapper.selectFeedbackByFeedbackId(feedbackId);
    }

    /**
     * 查询反馈列表
     * 
     * @param feedback 反馈信息
     * @return 反馈集合
     */
    @Override
    public List<Feedback> selectFeedbackList(Feedback feedback)
    {
        return feedbackMapper.selectFeedbackList(feedback);
    }
    
    /**
     * 根据用户ID查询反馈列表
     * 
     * @param userId 用户ID
     * @return 反馈集合
     */
    @Override
    public List<Feedback> selectFeedbackListByUserId(Long userId)
    {
        return feedbackMapper.selectFeedbackListByUserId(userId);
    }

    /**
     * 新增反馈
     * 
     * @param feedback 反馈信息
     * @return 结果
     */
    @Override
    public int insertFeedback(Feedback feedback)
    {
        // 设置默认状态为未回复
        if (feedback.getStatus() == null)
        {
            feedback.setStatus("0");
        }
        return feedbackMapper.insertFeedback(feedback);
    }

    /**
     * 修改反馈
     * 
     * @param feedback 反馈信息
     * @return 结果
     */
    @Override
    public int updateFeedback(Feedback feedback)
    {
        return feedbackMapper.updateFeedback(feedback);
    }
    
    /**
     * 回复反馈
     *
     * @param feedback 反馈信息
     * @return 结果
     */
    @Override
    public int replyFeedback(Feedback feedback)
    {
        // 设置状态为已回复
        feedback.setStatus("1");
        // 设置回复时间
        feedback.setReplyTime(new Date());
        // 设置回复人
        feedback.setReplyBy(SecurityUtils.getUsername());
        return feedbackMapper.updateFeedback(feedback);
    }

    /**
     * 批量删除反馈
     * 
     * @param feedbackIds 需要删除的反馈ID数组
     * @return 结果
     */
    @Override
    public int deleteFeedbackByFeedbackIds(Long[] feedbackIds)
    {
        return feedbackMapper.deleteFeedbackByFeedbackIds(feedbackIds);
    }

    /**
     * 删除反馈信息
     * 
     * @param feedbackId 反馈ID
     * @return 结果
     */
    @Override
    public int deleteFeedbackByFeedbackId(Long feedbackId)
    {
        return feedbackMapper.deleteFeedbackByFeedbackId(feedbackId);
    }
} 