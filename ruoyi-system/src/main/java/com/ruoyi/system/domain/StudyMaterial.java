package com.ruoyi.system.domain;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 学习资料对象 study_material
 * 
 * <AUTHOR>
 */
@TableName("study_material")
@Data
public class StudyMaterial extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 资料ID */
    @TableId
    private Long materialId;

    /** 资料名称 */
    @Excel(name = "资料名称")
    private String materialName;

    /** 资料类型（0电子书 1视频） */
    @Excel(name = "资料类型", readConverterExp = "0=电子书,1=视频")
    private String materialType;

    /** 资源链接 */
    @Excel(name = "资源链接")
    private String resourceUrl;
    
    /** 封面图片 */
    private String coverImg;

    /** 资料介绍 */
    @Excel(name = "资料介绍")
    private String materialDesc;

    /** 资源主题 */
    @Excel(name = "资源主题")
    private String theme;

    /** 所属套餐ID */
    @Excel(name = "所属套餐ID")
    private Long packageId;
    
    /** 所属套餐名称（非数据库字段） */
    @Excel(name = "所属套餐名称")
    @TableField(exist = false)
    private String packageName;
    
    /** 文件大小(KB) */
    @Excel(name = "文件大小(KB)")
    private Long fileSize;
    
    /** 浏览次数 */
    @Excel(name = "浏览次数")
    private Integer viewCount;
    
    /** 下载次数 */
    @Excel(name = "下载次数")
    private Integer downloadCount;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 排序号 */
    @Excel(name = "排序号")
    private Integer sortNum;

    private Boolean isLock;

    public Long getMaterialId() {
        return materialId;
    }

    public void setMaterialId(Long materialId) {
        this.materialId = materialId;
    }

    @NotBlank(message = "资料名称不能为空")
    @Size(min = 0, max = 100, message = "资料名称长度不能超过100个字符")
    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    @NotBlank(message = "资料类型不能为空")
    public String getMaterialType() {
        return materialType;
    }

    public void setMaterialType(String materialType) {
        this.materialType = materialType;
    }

    @NotBlank(message = "资源链接不能为空")
    @Size(min = 0, max = 255, message = "资源链接长度不能超过255个字符")
    public String getResourceUrl() {
        return resourceUrl;
    }

    public void setResourceUrl(String resourceUrl) {
        this.resourceUrl = resourceUrl;
    }
    
    public String getCoverImg() {
        return coverImg;
    }

    public void setCoverImg(String coverImg) {
        this.coverImg = coverImg;
    }

    public String getMaterialDesc() {
        return materialDesc;
    }

    public void setMaterialDesc(String materialDesc) {
        this.materialDesc = materialDesc;
    }

    @Size(min = 0, max = 100, message = "资源主题长度不能超过100个字符")
    public String getTheme() {
        return theme;
    }

    public void setTheme(String theme) {
        this.theme = theme;
    }

    @NotNull(message = "所属套餐ID不能为空")
    public Long getPackageId() {
        return packageId;
    }

    public void setPackageId(Long packageId) {
        this.packageId = packageId;
    }
    
    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }
    
    public Long getFileSize() {
        return fileSize;
    }

    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }
    
    public Integer getViewCount() {
        return viewCount;
    }

    public void setViewCount(Integer viewCount) {
        this.viewCount = viewCount;
    }
    
    public Integer getDownloadCount() {
        return downloadCount;
    }

    public void setDownloadCount(Integer downloadCount) {
        this.downloadCount = downloadCount;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getSortNum() {
        return sortNum;
    }

    public void setSortNum(Integer sortNum) {
        this.sortNum = sortNum;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("materialId", getMaterialId())
            .append("materialName", getMaterialName())
            .append("materialType", getMaterialType())
            .append("resourceUrl", getResourceUrl())
            .append("coverImg", getCoverImg())
            .append("materialDesc", getMaterialDesc())
            .append("theme", getTheme())
            .append("packageId", getPackageId())
            .append("packageName", getPackageName())
            .append("fileSize", getFileSize())
            .append("viewCount", getViewCount())
            .append("downloadCount", getDownloadCount())
            .append("status", getStatus())
            .append("sortNum", getSortNum())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
} 