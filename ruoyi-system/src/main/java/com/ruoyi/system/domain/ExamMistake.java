package com.ruoyi.system.domain;

import java.util.Date;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

/**
 * 错题对象 exam_mistake
 * 
 * <AUTHOR>
 */
@TableName("exam_mistake")
public class ExamMistake extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 错题ID */
    @Excel(name = "错题序号", cellType = Excel.ColumnType.NUMERIC)
    @TableId
    private Long mistakeId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 题目ID */
    @Excel(name = "题目ID")
    private Long questionId;

    /** 错误答案 */
    @Excel(name = "错误答案")
    private String wrongAnswer;

    /** 错误次数 */
    @Excel(name = "错误次数")
    private Integer mistakeCount;

    /** 最后错误时间 */
    @Excel(name = "最后错误时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date lastMistakeTime;

    /** 状态（0未复习 1已复习） */
    @Excel(name = "状态", readConverterExp = "0=未复习,1=已复习")
    private String status;
    
    /** 关联的题目对象 */
    @TableField(exist = false)
    private ExamQuestion question;
    
    /** 关联的用户对象 */
    @TableField(exist = false)
    private WxUser user;

    public void setMistakeId(Long mistakeId) 
    {
        this.mistakeId = mistakeId;
    }

    public Long getMistakeId() 
    {
        return mistakeId;
    }

    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }

    public void setQuestionId(Long questionId) 
    {
        this.questionId = questionId;
    }

    public Long getQuestionId() 
    {
        return questionId;
    }

    public void setWrongAnswer(String wrongAnswer) 
    {
        this.wrongAnswer = wrongAnswer;
    }

    public String getWrongAnswer() 
    {
        return wrongAnswer;
    }

    public void setMistakeCount(Integer mistakeCount) 
    {
        this.mistakeCount = mistakeCount;
    }

    public Integer getMistakeCount() 
    {
        return mistakeCount;
    }

    public void setLastMistakeTime(Date lastMistakeTime) 
    {
        this.lastMistakeTime = lastMistakeTime;
    }

    public Date getLastMistakeTime() 
    {
        return lastMistakeTime;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    
    public void setQuestion(ExamQuestion question)
    {
        this.question = question;
    }
    
    public ExamQuestion getQuestion()
    {
        return question;
    }
    
    public void setUser(WxUser user)
    {
        this.user = user;
    }
    
    public WxUser getUser()
    {
        return user;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("mistakeId", getMistakeId())
            .append("userId", getUserId())
            .append("questionId", getQuestionId())
            .append("wrongAnswer", getWrongAnswer())
            .append("mistakeCount", getMistakeCount())
            .append("lastMistakeTime", getLastMistakeTime())
            .append("status", getStatus())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
} 