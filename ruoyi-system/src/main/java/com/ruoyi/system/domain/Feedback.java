package com.ruoyi.system.domain;

import java.util.Date;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 反馈信息对象 feedback
 * 
 * <AUTHOR>
 */
public class Feedback extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 反馈ID */
    private Long feedbackId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 反馈内容 */
    @Excel(name = "反馈内容")
    private String content;

    /** 联系方式 */
    @Excel(name = "联系方式")
    private String contact;

    /** 反馈类型（0建议 1问题 2其他） */
    @Excel(name = "反馈类型", readConverterExp = "0=建议,1=问题,2=其他")
    private String feedbackType;

    /** 图片地址 */
    private String images;

    /** 状态（0未处理 1已处理） */
    @Excel(name = "状态", readConverterExp = "0=未处理,1=已处理")
    private String status;

    /** 回复内容 */
    @Excel(name = "回复内容")
    private String reply;

    /** 回复时间 */
    @Excel(name = "回复时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date replyTime;

    /** 回复人 */
    @Excel(name = "回复人")
    private String replyBy;
    
    /** 关联的用户信息（前端展示使用） */
    private WxUser user;

    public void setFeedbackId(Long feedbackId) 
    {
        this.feedbackId = feedbackId;
    }

    public Long getFeedbackId() 
    {
        return feedbackId;
    }

    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }

    @NotBlank(message = "反馈内容不能为空")
    @Size(min = 1, max = 500, message = "反馈内容长度不能超过500个字符")
    public String getContent() 
    {
        return content;
    }

    public void setContent(String content) 
    {
        this.content = content;
    }

    @Size(max = 100, message = "联系方式长度不能超过100个字符")
    public String getContact() 
    {
        return contact;
    }

    public void setContact(String contact) 
    {
        this.contact = contact;
    }

    public String getFeedbackType() 
    {
        return feedbackType;
    }

    public void setFeedbackType(String feedbackType) 
    {
        this.feedbackType = feedbackType;
    }

    public String getImages() 
    {
        return images;
    }

    public void setImages(String images) 
    {
        this.images = images;
    }

    public String getStatus() 
    {
        return status;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    @Size(max = 500, message = "回复内容长度不能超过500个字符")
    public String getReply() 
    {
        return reply;
    }

    public void setReply(String reply) 
    {
        this.reply = reply;
    }

    public Date getReplyTime() 
    {
        return replyTime;
    }

    public void setReplyTime(Date replyTime) 
    {
        this.replyTime = replyTime;
    }

    public String getReplyBy() 
    {
        return replyBy;
    }

    public void setReplyBy(String replyBy) 
    {
        this.replyBy = replyBy;
    }
    
    public WxUser getUser() 
    {
        return user;
    }

    public void setUser(WxUser user) 
    {
        this.user = user;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("feedbackId", getFeedbackId())
            .append("userId", getUserId())
            .append("content", getContent())
            .append("contact", getContact())
            .append("feedbackType", getFeedbackType())
            .append("images", getImages())
            .append("status", getStatus())
            .append("reply", getReply())
            .append("replyTime", getReplyTime())
            .append("replyBy", getReplyBy())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
} 