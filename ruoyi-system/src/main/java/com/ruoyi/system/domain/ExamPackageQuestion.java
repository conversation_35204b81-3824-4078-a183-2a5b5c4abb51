package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 套餐题目关联对象 exam_package_question
 * 
 * <AUTHOR>
 */
public class ExamPackageQuestion extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 套餐ID */
    @Excel(name = "套餐ID")
    private Long packageId;

    /** 题目ID */
    @Excel(name = "题目ID")
    private Long questionId;

    /** 题库类型（1模拟考试题库 2练习题库） */
    @Excel(name = "题库类型", readConverterExp = "1=模拟考试题库,2=练习题库")
    private String questionType;

    public Long getId()
    {
        return id;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getPackageId()
    {
        return packageId;
    }

    public void setPackageId(Long packageId)
    {
        this.packageId = packageId;
    }

    public Long getQuestionId()
    {
        return questionId;
    }

    public void setQuestionId(Long questionId)
    {
        this.questionId = questionId;
    }

    public String getQuestionType()
    {
        return questionType;
    }

    public void setQuestionType(String questionType)
    {
        this.questionType = questionType;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("packageId", getPackageId())
            .append("questionId", getQuestionId())
            .append("questionType", getQuestionType())
            .toString();
    }
} 