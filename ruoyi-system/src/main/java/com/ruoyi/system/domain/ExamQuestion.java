package com.ruoyi.system.domain;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import javax.validation.constraints.NotBlank;


/**
 * 题目对象 exam_question
 * 
 * <AUTHOR>
 */
@Data
@TableName("exam_question")
public class ExamQuestion extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 题目ID */
    @TableId
    private Long questionId;

    /** 题目主题 */
    @Excel(name = "题目主题")
        private String subject;

    /** 题目类型（1单选题 2多选题 3判断题） */
    @Excel(name = "题目类型", readConverterExp = "1=单选题,2=多选题,3=判断题")
    private String questionType;

    /** 题目内容 */
    @Excel(name = "题目内容")
    private String questionContent;

    /** 选项A */
    @Excel(name = "选项A")
    private String optionA;

    /** 选项B */
    @Excel(name = "选项B")
    private String optionB;

    /** 选项C */
    @Excel(name = "选项C")
    private String optionC;

    /** 选项D */
    @Excel(name = "选项D")
    private String optionD;

    /** 选项C */
    @Excel(name = "选项E")
    private String optionE;

    /** 选项D */
    @Excel(name = "选项F")
    private String optionF;

    /** 正确答案 */
    @Excel(name = "正确答案")
    private String correctAnswer;

    /** 答案解析 */
    @Excel(name = "答案解析")
    private String analysis;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;
    
    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    @TableField(exist = false)
    private String categoryName;

    @Excel(name = "题库id")
    private Integer categoryId;

    @Excel(name = "是否模考题", readConverterExp = "0=否,1=是")
    private String isMock;
    @Excel(name = "是否学习题库", readConverterExp = "0=否,1=是")
    private String isStudy;

    @TableField(exist = false)
    private Boolean isRight;

    @TableField(exist = false)
    private Boolean isFinish;

    @TableField(exist = false)
    private String userAnswer;



    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }



    public String getCategoryName() {
        return categoryName;
    }

    public Integer getCategoryId() {
        return categoryId;
    }


    public Long getQuestionId()
    {
        return questionId;
    }

    public void setQuestionId(Long questionId)
    {
        this.questionId = questionId;
    }

    public String getSubject()
    {
        return subject;
    }

    public void setSubject(String subject)
    {
        this.subject = subject;
    }

    @NotBlank(message = "题目类型不能为空")
    public String getQuestionType()
    {
        return questionType;
    }

    public void setQuestionType(String questionType)
    {
        this.questionType = questionType;
    }

    @NotBlank(message = "题目内容不能为空")
    public String getQuestionContent()
    {
        return questionContent;
    }

    public void setQuestionContent(String questionContent)
    {
        this.questionContent = questionContent;
    }

    public String getOptionA()
    {
        return optionA;
    }

    public void setOptionA(String optionA)
    {
        this.optionA = optionA;
    }

    public String getOptionB()
    {
        return optionB;
    }

    public void setOptionB(String optionB)
    {
        this.optionB = optionB;
    }

    public String getOptionC()
    {
        return optionC;
    }

    public void setOptionC(String optionC)
    {
        this.optionC = optionC;
    }

    public String getOptionD()
    {
        return optionD;
    }

    public void setOptionD(String optionD)
    {
        this.optionD = optionD;
    }

    @NotBlank(message = "正确答案不能为空")
    public String getCorrectAnswer()
    {
        return correctAnswer;
    }

    public void setCorrectAnswer(String correctAnswer)
    {
        this.correctAnswer = correctAnswer;
    }

    public String getAnalysis()
    {
        return analysis;
    }

    public void setAnalysis(String analysis)
    {
        this.analysis = analysis;
    }

    public String getStatus()
    {
        return status;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getDelFlag()
    {
        return delFlag;
    }

    public void setDelFlag(String delFlag)
    {
        this.delFlag = delFlag;
    }


} 