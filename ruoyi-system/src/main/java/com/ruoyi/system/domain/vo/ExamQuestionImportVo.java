package com.ruoyi.system.domain.vo;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import javax.validation.constraints.NotBlank;

/**
 * 题目导入VO对象
 * 
 * <AUTHOR>
 */
@Data
public class ExamQuestionImportVo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 题目主题 */
    @Excel(name = "题目主题")
    private String subject;

    /** 题目类型（1单选题 2多选题 3判断题） */
    @Excel(name = "题目类型", readConverterExp = "1=单选题,2=多选题,3=判断题")
    private String questionType;

    /** 题目内容 */
    @Excel(name = "题目内容")
    private String questionContent;

    /** 选项A */
    @Excel(name = "选项A")
    private String optionA;

    /** 选项B */
    @Excel(name = "选项B")
    private String optionB;

    /** 选项C */
    @Excel(name = "选项C")
    private String optionC;

    /** 选项D */
    @Excel(name = "选项D")
    private String optionD;

    /** 选项C */
    @Excel(name = "选项E")
    private String optionE;

    /** 选项D */
    @Excel(name = "选项F")
    private String optionF;

    /** 正确答案 */
    @Excel(name = "正确答案")
    private String correctAnswer;

    /** 答案解析 */
    @Excel(name = "答案解析")
    private String analysis;

    /** 题库类型（1模拟考试题库 2练习题库） */
//    @Excel(name = "题库类型", readConverterExp = "1=模拟考试题库,2=练习题库")
    @Excel(name = "题库id")
    private Integer categoryId;

    @Excel(name = "是否模考题", readConverterExp = "0=否,1=是")
    private String isMock;
    @Excel(name = "是否学习题库", readConverterExp = "0=否,1=是")
    private String isStudy;

    public String getSubject()
    {
        return subject;
    }

    public void setSubject(String subject)
    {
        this.subject = subject;
    }

    @NotBlank(message = "题目类型不能为空")
    public String getQuestionType()
    {
        return questionType;
    }

    public void setQuestionType(String questionType)
    {
        this.questionType = questionType;
    }

    @NotBlank(message = "题目内容不能为空")
    public String getQuestionContent()
    {
        return questionContent;
    }

    public void setQuestionContent(String questionContent)
    {
        this.questionContent = questionContent;
    }

    public String getOptionA()
    {
        return optionA;
    }

    public void setOptionA(String optionA)
    {
        this.optionA = optionA;
    }

    public String getOptionB()
    {
        return optionB;
    }

    public void setOptionB(String optionB)
    {
        this.optionB = optionB;
    }

    public String getOptionC()
    {
        return optionC;
    }

    public void setOptionC(String optionC)
    {
        this.optionC = optionC;
    }

    public String getOptionD()
    {
        return optionD;
    }

    public void setOptionD(String optionD)
    {
        this.optionD = optionD;
    }

    @NotBlank(message = "正确答案不能为空")
    public String getCorrectAnswer()
    {
        return correctAnswer;
    }

    public void setCorrectAnswer(String correctAnswer)
    {
        this.correctAnswer = correctAnswer;
    }

    public String getAnalysis()
    {
        return analysis;
    }

    public void setAnalysis(String analysis)
    {
        this.analysis = analysis;
    }

//    @NotBlank(message = "题库类型不能为空")
//    public String getLibType()
//    {
//        return libType;
//    }

//    public void setLibType(String libType)
//    {
//        this.libType = libType;
//    }


} 