package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import javax.validation.constraints.NotNull;

/**
 * 学习记录对象 study_record
 * 
 * <AUTHOR>
 */
public class StudyRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 记录ID */
    private Long recordId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;
    
    /** 用户昵称（非数据库字段） */
    @Excel(name = "用户昵称")
    private String nickName;

    /** 用户手机号（非数据库字段） */
    @Excel(name = "用户手机号")
    private String phone;

    /** 资料ID */
    @Excel(name = "资料ID")
    private Long materialId;
    
    /** 资料名称（非数据库字段） */
    @Excel(name = "资料名称")
    private String materialName;

    /** 套餐ID */
    @Excel(name = "套餐ID")
    private Long packageId;
    
    /** 套餐名称（非数据库字段） */
    @Excel(name = "套餐名称")
    private String packageName;

    /** 资料类型（0电子书 1视频） */
    @Excel(name = "资料类型", readConverterExp = "0=电子书,1=视频")
    private String materialType;

    /** 学习进度(百分比) */
    @Excel(name = "学习进度(%)")
    private Integer progress;

    /** 上次学习位置 */
    @Excel(name = "上次学习位置")
    private String lastPosition;

    /** 学习时长(秒) */
    @Excel(name = "学习次数")
    private Integer studyTime;

    public Long getRecordId() {
        return recordId;
    }

    public void setRecordId(Long recordId) {
        this.recordId = recordId;
    }

    @NotNull(message = "用户ID不能为空")
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    @NotNull(message = "资料ID不能为空")
    public Long getMaterialId() {
        return materialId;
    }

    public void setMaterialId(Long materialId) {
        this.materialId = materialId;
    }
    
    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    @NotNull(message = "套餐ID不能为空")
    public Long getPackageId() {
        return packageId;
    }

    public void setPackageId(Long packageId) {
        this.packageId = packageId;
    }
    
    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    @NotNull(message = "资料类型不能为空")
    public String getMaterialType() {
        return materialType;
    }

    public void setMaterialType(String materialType) {
        this.materialType = materialType;
    }

    public Integer getProgress() {
        return progress;
    }

    public void setProgress(Integer progress) {
        this.progress = progress;
    }

    public String getLastPosition() {
        return lastPosition;
    }

    public void setLastPosition(String lastPosition) {
        this.lastPosition = lastPosition;
    }

    public Integer getStudyTime() {
        return studyTime;
    }

    public void setStudyTime(Integer studyTime) {
        this.studyTime = studyTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("recordId", getRecordId())
            .append("userId", getUserId())
            .append("nickName", getNickName())
            .append("phone", getPhone())
            .append("materialId", getMaterialId())
            .append("materialName", getMaterialName())
            .append("packageId", getPackageId())
            .append("packageName", getPackageName())
            .append("materialType", getMaterialType())
            .append("progress", getProgress())
            .append("lastPosition", getLastPosition())
            .append("studyTime", getStudyTime())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
} 