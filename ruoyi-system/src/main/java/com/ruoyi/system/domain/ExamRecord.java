package com.ruoyi.system.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.config.deserializer.NullableDoubleDeserializer;
import com.ruoyi.common.config.deserializer.NullableIntegerDeserializer;
import com.ruoyi.common.utils.ExamRecordUtils;
import java.util.List;

/**
 * 考试记录对象 exam_record
 * 
 * <AUTHOR>
 */
@Data
public class ExamRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 记录ID */
    private Long recordId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 套餐ID */
    @Excel(name = "套餐ID")
    private Long packageId;

    /** 考试开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "考试开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date examStartTime;

    /** 考试结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "考试结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date examEndTime;

    /** 分数 */
    @Excel(name = "分数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonDeserialize(using = NullableDoubleDeserializer.class)
    private Double score;

    /** 正确数 */
    @Excel(name = "正确数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonDeserialize(using = NullableIntegerDeserializer.class)
    private Integer correctCount;

    /** 错误数 */
    @Excel(name = "错误数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonDeserialize(using = NullableIntegerDeserializer.class)
    private Integer wrongCount;

    /** 空题数 */
    @Excel(name = "空题数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonDeserialize(using = NullableIntegerDeserializer.class)
    private Integer emptyCount;

    /** 状态（0正常 1删除） */
    private String status;

    /** 是否提交（0未交卷 1已交卷） */
    @Excel(name = "是否提交", readConverterExp = "0=未交卷,1=已交卷")
    private String state;

    /** 已提交正确题目ID集合 */
    @Excel(name = "正确题目ID集合")
    private String finishIds;

    /** 已提交错误题目ID集合 */
    @Excel(name = "错误题目ID集合")
    private String finishAnswer;

    /** 未提交题目ID集合 */
    @Excel(name = "未提交题目ID集合")
    private String forgetIds;

    private Integer overTime;

    /** 微信用户（关联查询使用） */
    private WxUser user;
    
    /** 套餐（关联查询使用） */
    private ExamPackage examPackage;

    private String finishRate;

    private String isPass;

    private List<ExamQuestion> questions;

    /** 用户姓名（查询条件使用） */
    private String userName;

    /** 用户手机号（查询条件使用） */
    private String phone;

    /** 套餐名称（查询条件使用） */
    private String packageName;

}