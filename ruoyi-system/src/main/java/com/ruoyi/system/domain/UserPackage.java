package com.ruoyi.system.domain;

import java.util.Date;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 用户套餐对象 user_package
 * 
 * <AUTHOR>
 */
public class UserPackage extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 用户套餐ID */
    private Long userPackageId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 套餐ID */
    @Excel(name = "套餐ID")
    private Long packageId;

    /** 订单ID */
    @Excel(name = "订单ID")
    private Long orderId;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 状态（0有效 1已过期） */
    @Excel(name = "状态", readConverterExp = "0=有效,1=已过期")
    private String status;

    /** 微信用户（关联查询使用） */
    private WxUser user;
    
    /** 套餐（关联查询使用） */
    private ExamPackage examPackage;
    
    /** 订单（关联查询使用） */
    private OrderInfo orderInfo;

    public void setUserPackageId(Long userPackageId) 
    {
        this.userPackageId = userPackageId;
    }

    public Long getUserPackageId() 
    {
        return userPackageId;
    }

    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }

    public void setPackageId(Long packageId) 
    {
        this.packageId = packageId;
    }

    public Long getPackageId() 
    {
        return packageId;
    }

    public void setOrderId(Long orderId) 
    {
        this.orderId = orderId;
    }

    public Long getOrderId() 
    {
        return orderId;
    }

    public void setStartTime(Date startTime) 
    {
        this.startTime = startTime;
    }

    public Date getStartTime() 
    {
        return startTime;
    }

    public void setEndTime(Date endTime) 
    {
        this.endTime = endTime;
    }

    public Date getEndTime() 
    {
        return endTime;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    
    public WxUser getUser() 
    {
        return user;
    }

    public void setUser(WxUser user) 
    {
        this.user = user;
    }
    
    public ExamPackage getExamPackage() 
    {
        return examPackage;
    }

    public void setExamPackage(ExamPackage examPackage) 
    {
        this.examPackage = examPackage;
    }
    
    public OrderInfo getOrderInfo() 
    {
        return orderInfo;
    }

    public void setOrderInfo(OrderInfo orderInfo) 
    {
        this.orderInfo = orderInfo;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("userPackageId", getUserPackageId())
            .append("userId", getUserId())
            .append("packageId", getPackageId())
            .append("orderId", getOrderId())
            .append("startTime", getStartTime())
            .append("endTime", getEndTime())
            .append("status", getStatus())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
} 