package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import javax.validation.constraints.NotNull;

/**
 * 用户学习进度对象 user_study_progress
 * 
 * <AUTHOR>
 */
@TableName("user_study_progress")
public class UserStudyProgress extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 进度ID */
    @TableId
    private Long progressId;

    /** 套餐ID */
    @Excel(name = "套餐ID")
    private Long packageId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 当前学习进度排序号 */
    @Excel(name = "当前学习进度排序号")
    private Integer sortNum;

    /** 套餐名称（非数据库字段） */
    @Excel(name = "套餐名称")
    @TableField(exist = false)
    private String packageName;

    /** 用户昵称（非数据库字段） */
    @Excel(name = "用户昵称")
    @TableField(exist = false)
    private String nickName;

    /** 当前学习资料名称（非数据库字段） */
    @Excel(name = "当前学习资料名称")
    @TableField(exist = false)
    private String currentMaterialName;

    public Long getProgressId() {
        return progressId;
    }

    public void setProgressId(Long progressId) {
        this.progressId = progressId;
    }

    @NotNull(message = "套餐ID不能为空")
    public Long getPackageId() {
        return packageId;
    }

    public void setPackageId(Long packageId) {
        this.packageId = packageId;
    }

    @NotNull(message = "用户ID不能为空")
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getSortNum() {
        return sortNum;
    }

    public void setSortNum(Integer sortNum) {
        this.sortNum = sortNum;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getCurrentMaterialName() {
        return currentMaterialName;
    }

    public void setCurrentMaterialName(String currentMaterialName) {
        this.currentMaterialName = currentMaterialName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("progressId", getProgressId())
            .append("packageId", getPackageId())
            .append("userId", getUserId())
            .append("sortNum", getSortNum())
            .append("packageName", getPackageName())
            .append("nickName", getNickName())
            .append("currentMaterialName", getCurrentMaterialName())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
