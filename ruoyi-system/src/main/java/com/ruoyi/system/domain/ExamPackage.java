package com.ruoyi.system.domain;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;

/**
 * 题库套餐对象 exam_package
 * 
 * <AUTHOR>
 */
@TableName("exam_package")
@Data
public class ExamPackage extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 套餐ID */
    @TableId
    private Long packageId;

    /** 套餐名称 */
    @Excel(name = "套餐名称")
    private String packageName;

    /** 分类ID */
    @Excel(name = "分类ID")
    private Long categoryId;

    /** 套餐介绍 */
    @Excel(name = "套餐介绍")
    private String introduction;

    /** 套餐价格 */
    @Excel(name = "套餐价格")
    private BigDecimal price;

    /** 有效期(天) */
    @Excel(name = "有效期(天)")
    private Integer validityDays;

    private String coverImage;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;
    
    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;
    
    /** 分类名称 */
    @Excel(name = "分类名称")
    @TableField(exist = false)
    private String categoryName;

    public Long getPackageId()
    {
        return packageId;
    }

    public void setPackageId(Long packageId)
    {
        this.packageId = packageId;
    }

    @NotBlank(message = "套餐名称不能为空")
    @Size(min = 0, max = 100, message = "套餐名称长度不能超过100个字符")
    public String getPackageName()
    {
        return packageName;
    }

    public void setPackageName(String packageName)
    {
        this.packageName = packageName;
    }

    @NotNull(message = "分类ID不能为空")
    public Long getCategoryId()
    {
        return categoryId;
    }

    public void setCategoryId(Long categoryId)
    {
        this.categoryId = categoryId;
    }

    @NotBlank(message = "套餐介绍不能为空")
    @Size(min = 0, max = 500, message = "套餐介绍长度不能超过500个字符")
    public String getIntroduction()
    {
        return introduction;
    }

    public void setIntroduction(String introduction)
    {
        this.introduction = introduction;
    }

    @NotNull(message = "套餐价格不能为空")
    public BigDecimal getPrice()
    {
        return price;
    }

    public void setPrice(BigDecimal price)
    {
        this.price = price;
    }

    @NotNull(message = "有效期不能为空")
    public Integer getValidityDays()
    {
        return validityDays;
    }

    public void setValidityDays(Integer validityDays)
    {
        this.validityDays = validityDays;
    }

    public String getStatus()
    {
        return status;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getDelFlag()
    {
        return delFlag;
    }

    public void setDelFlag(String delFlag)
    {
        this.delFlag = delFlag;
    }

    public String getCategoryName()
    {
        return categoryName;
    }

    public void setCategoryName(String categoryName)
    {
        this.categoryName = categoryName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("packageId", getPackageId())
            .append("packageName", getPackageName())
            .append("categoryId", getCategoryId())
            .append("introduction", getIntroduction())
            .append("price", getPrice())
            .append("validityDays", getValidityDays())
            .append("status", getStatus())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
} 