package com.ruoyi.system.util;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.lang.reflect.Method;
import java.util.function.Function;

/**
 * MyBatis-Plus工具类，提供安全调用方式
 * 
 * 当MyBatis-Plus方法调用失败时，�?动回退到原生MyBatis方法
 */
public class MyBatisPlusUtil {
    
    private static final Logger log = LoggerFactory.getLogger(MyBatisPlusUtil.class);
    
    /**
     * 安全调用selectById方法，失败时回退到自定义方法
     * 
     * @param <T> 实体类型
     * @param <R> 返回类型
     * @param mapper BaseMapper接口
     * @param id 主键
     * @param fallbackMethod 回退方法�?
     * @return 查�?�结�?
     */
    public static <T, R> R safeSelectById(BaseMapper<T> mapper, Serializable id, String fallbackMethod) {
        try {
            // 尝试调用MyBatis-Plus的selectById方法
            return (R) mapper.selectById(id);
        } catch (Exception e) {
            log.debug("MyBatis-Plus selectById方法调用失败，回退到{}方法: {}", fallbackMethod, e.getMessage());
            try {
                // 通过反射调用回退方法
                Method method = mapper.getClass().getMethod(fallbackMethod, id.getClass());
                return (R) method.invoke(mapper, id);
            } catch (Exception ex) {
                log.error("回退方法{}调用失败: {}", fallbackMethod, ex.getMessage());
                throw new RuntimeException("数据查�?�失�?", ex);
            }
        }
    }
    
    /**
     * 安全执�?�方法，提供回退策略
     * 
     * @param <T> 参数类型
     * @param <R> 返回类型
     * @param mainAction 主�?�操�?
     * @param fallbackAction 回退操作
     * @param param 参数
     * @return 执�?�结�?
     */
    public static <T, R> R safeExecute(Function<T, R> mainAction, Function<T, R> fallbackAction, T param) {
        try {
            // 尝试执�?�主操作
            return mainAction.apply(param);
        } catch (Exception e) {
            log.debug("主操作执行失败，使用回退操作: {}", e.getMessage());
            try {
                // 执�?�回退操作
                return fallbackAction.apply(param);
            } catch (Exception ex) {
                log.error("回退操作执�?�失�?: {}", ex.getMessage());
                throw new RuntimeException("操作执�?�失�?", ex);
            }
        }
    }
} 