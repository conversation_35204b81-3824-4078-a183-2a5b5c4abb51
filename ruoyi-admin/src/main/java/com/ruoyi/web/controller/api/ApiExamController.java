package com.ruoyi.web.controller.api;

import java.util.*;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.*;
import com.ruoyi.system.service.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.CalculateUtils;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 小程序做题相关API
 * 
 * <AUTHOR>
 */
@Api(tags = "小程序做题API")
@RestController
@RequestMapping("/v1/exam")
public class ApiExamController extends BaseController
{
    @Autowired
    private IExamQuestionService examQuestionService;
    @Autowired
    private IExamRecordService examRecordService;
    
    @Autowired
    private IExamMistakeService examMistakeService;
    
    @Autowired
    private IExamPackageService examPackageService;
    
    @Autowired
    private IUserPackageService userPackageService;

    @Autowired
    private IOrderInfoService orderInfoService;
    
    @Autowired
    private IWxUserService wxUserService;
    
    @Autowired
    private IExamCategoryService examCategoryService;
    @Autowired
    private IOrderInfoService iOrderInfoService;

    /**
     * 提交答案并记录错题
     */
    @ApiOperation(value = "提交答案并记录错题", 
                notes = "做题正确则删除错题记录，做题错误则新增或更新错题记录\n" +
                       "请求体格式：{\"questionId\": 123, \"answer\": \"A\"}\n" +
                       "返回结果格式：{\n" +
                       "  \"code\": 200,\n" +
                       "  \"msg\": \"操作成功\",\n" +
                       "  \"data\": {\n" +
                       "    \"isCorrect\": true/false,\n" +
                       "    \"correctAnswer\": \"正确答案\",\n" +
                       "    \"analysis\": \"答案解析内容\"\n" +
                       "  }\n" +
                       "}")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "Authorization", value = "用户登录令牌", required = true, paramType = "header", dataType = "String")
    })
    @PostMapping("/answer")
    public AjaxResult submitAnswer(@ApiParam(value = "答题信息", required = true, example = "{\"questionId\": 123, \"answer\": \"A\"}") 
                                  @RequestBody Map<String, Object> requestMap, HttpServletRequest request)
    {
        logger.info("开始接收答题信息",requestMap);
        // 从Token中获取用户的openid
        String token = request.getHeader("Authorization");
        if (token == null || token.isEmpty()) {
            return AjaxResult.error("未登录");
        }
        
        // 根据Token查询用户信息
        WxUser wxUser = wxUserService.getWxUserByToken(token);
        if (wxUser == null) {
            return AjaxResult.error("用户不存在或登录已过期");
        }
        
        // 获取请求参数
        Long questionId = Long.valueOf(requestMap.get("questionId").toString());
        String userAnswer = requestMap.get("answer").toString();
        
        // 查询题目
        ExamQuestion question = examQuestionService.selectExamQuestionByQuestionId(questionId);
        if (question == null) {
            return AjaxResult.error("题目不存在");
        }
        
        // 比对答案
        boolean isCorrect = question.getCorrectAnswer().equalsIgnoreCase(userAnswer);
        Map<String, Object> result = new HashMap<>();
        result.put("isCorrect", isCorrect);
        result.put("correctAnswer", question.getCorrectAnswer());
        result.put("analysis", question.getAnalysis());
        
        // 根据答题情况处理错题记录
        if (isCorrect&&"1".equals(question.getIsMock())) {
            // 答对了，删除错题记录（如果存在）
            ExamMistake existMistake = examMistakeService.selectExamMistakeByUserIdAndQuestionId(
                    wxUser.getUserId(), questionId);
            if (existMistake != null) {
                examMistakeService.deleteExamMistakeByMistakeId(existMistake.getMistakeId());
            }
            wxUser.setCorrectNum(wxUser.getCorrectNum() + 1);
            wxUserService.updateWxUser(wxUser);
        } else {
            // 答错了，更新或新增错题记录
            ExamMistake existMistake = examMistakeService.selectExamMistakeByUserIdAndQuestionId(
                    wxUser.getUserId(), questionId);
            
            if (existMistake != null) {
                // 已存在，更新错误次数和最后错误时间
                existMistake.setMistakeCount(existMistake.getMistakeCount() + 1);
                existMistake.setWrongAnswer(userAnswer);
                existMistake.setLastMistakeTime(new Date());
                existMistake.setStatus("0"); // 设置为未复习状态
                examMistakeService.updateExamMistake(existMistake);
            } else {
                // 不存在，新增记录
                ExamMistake newMistake = new ExamMistake();
                newMistake.setUserId(wxUser.getUserId());
                newMistake.setQuestionId(questionId);
                newMistake.setWrongAnswer(userAnswer);
                newMistake.setMistakeCount(1);
                newMistake.setLastMistakeTime(new Date());
                newMistake.setStatus("0"); // 设置为未复习状态
                examMistakeService.insertExamMistake(newMistake);
            }
        }
        
        return AjaxResult.success(result);
    }




    /**
     * 提交答案并记录错题
     */
    @ApiOperation(value = "查询自己的错题")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authorization", value = "用户登录令牌", required = true, paramType = "header", dataType = "String")
    })
    @GetMapping("/getMistakes/{packageId}")
    public AjaxResult getMistakes(@PathVariable Long packageId, HttpServletRequest request)
    {
        // 从Token中获取用户的openid
        String token = request.getHeader("Authorization");
        if (token == null || token.isEmpty()) {
            return AjaxResult.error("未登录");
        }

        // 根据Token查询用户信息
        WxUser wxUser = wxUserService.getWxUserByToken(token);
        if (wxUser == null) {
            return AjaxResult.error("用户不存在或登录已过期");
        }
        long categoryId = 0l;
        List<ExamPackage> packages = examPackageService.list(new QueryWrapper<ExamPackage>().eq("package_id", packageId));
        if(packages.size()>0){
            categoryId = packages.get(0).getCategoryId();
        }else{
            return AjaxResult.error("套餐不存在");
        }
        List<ExamQuestion> questions = examQuestionService.list(new QueryWrapper<ExamQuestion>().eq("category_id", categoryId));
        List<Long> ids = questions.stream().map(ExamQuestion::getQuestionId).collect(Collectors.toList());
        if(ids.size()>0){
            List<ExamMistake> list = examMistakeService.list(new QueryWrapper<ExamMistake>().eq("user_id", wxUser.getUserId()).in("question_id", ids));
            list.forEach(x->{
                x.setQuestion(examQuestionService.getById(x.getQuestionId()));
            });
            return AjaxResult.success(list);
        }else{
            return AjaxResult.success(new ArrayList<ExamMistake>());
        }

    }


    /**
     * 获取套餐关联的所有资源
     */
    @ApiOperation(value = "获取套餐资源", 
                notes = "获取套餐关联的所有资源信息\n" +
                       "返回结果格式：{\n" +
                       "  \"code\": 200,\n" +
                       "  \"msg\": \"操作成功\",\n" +
                       "  \"data\": {\n" +
                       "    \"package\": {\n" +
                       "      \"packageId\": 套餐ID,\n" +
                       "      \"packageName\": \"套餐名称\",\n" +
                       "      \"introduction\": \"套餐介绍\",\n" +
                       "      \"price\": 套餐价格,\n" +
                       "      \"validityDays\": 有效期天数\n" +
                       "    },\n" +
                       "    \"category\": {\n" +
                       "      \"categoryId\": 分类ID,\n" +
                       "      \"categoryName\": \"分类名称\"\n" +
                       "    }\n" +
                       "  }\n" +
                       "}")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "packageId", value = "套餐ID", required = true, dataType = "Long", paramType = "path", dataTypeClass = Long.class),
        @ApiImplicitParam(name = "Authorization", value = "用户登录令牌", required = true, paramType = "header", dataType = "String")
    })
    @GetMapping("/package/{packageId}/resources")
    public AjaxResult getPackageResources(@PathVariable("packageId") Long packageId, HttpServletRequest request)
    {
        // 从Token中获取用户的openid
        String token = request.getHeader("Authorization");
        if (token == null || token.isEmpty()) {
            return AjaxResult.error("未登录");
        }
        
        // 根据Token查询用户信息
        WxUser wxUser = wxUserService.getWxUserByToken(token);
        if (wxUser == null) {
            return AjaxResult.error("用户不存在或登录已过期");
        }
        
        // 查询套餐信息
        ExamPackage examPackage = examPackageService.selectExamPackageByPackageId(packageId);
        if (examPackage == null) {
            return AjaxResult.error("套餐不存在");
        }
        
        // 查询用户是否有权限访问该套餐
        List<UserPackage> userPackages = userPackageService.selectUserPackageByUserId(wxUser.getUserId());
        boolean hasAccess = false;
        for (UserPackage userPackage : userPackages) {
            if (userPackage.getPackageId().equals(packageId) && "0".equals(userPackage.getStatus())) {
                Long orderId = userPackage.getOrderId();
                OrderInfo orderInfo = orderInfoService.selectOrderInfoByOrderId(orderId);
                if(new Date().after(orderInfo.getExpireTime())){
                    hasAccess = false;
                }else{
                    hasAccess = true;
                }
            }
        }
        
        if (!hasAccess) {
            return AjaxResult.error("您没有权限访问该套餐或套餐已过期");
        }
        
        // 查询分类信息
        ExamCategory category = examCategoryService.selectExamCategoryByCategoryId(examPackage.getCategoryId().intValue());
        
        // 构建返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("package", examPackage);
        result.put("category", category);
        
        return AjaxResult.success(result);
    }

    /**
     * 获取套餐相关的题目
     */
    @ApiOperation(value = "获取套餐题目", 
                notes = "获取套餐关联的题目，1为练习题（随机100道），2为模拟题（随机200道）\n" +
                       "返回结果格式：{\n" +
                       "  \"code\": 200,\n" +
                       "  \"msg\": \"操作成功\",\n" +
                       "  \"data\": [\n" +
                       "    {\n" +
                       "      \"questionId\": 题目ID,\n" +
                       "      \"subject\": \"题目主题\",\n" +
                       "      \"questionType\": \"题目类型（1单选 2多选 3判断）\",\n" +
                       "      \"questionContent\": \"题目内容\",\n" +
                       "      \"optionA\": \"选项A\",\n" +
                       "      \"optionB\": \"选项B\",\n" +
                       "      \"optionC\": \"选项C\",\n" +
                       "      \"optionD\": \"选项D\",\n" +
                       "      \"correctAnswer\": \"正确答案（仅后端可见）\",\n" +
                       "      \"analysis\": \"答案解析（作答后可见）\"\n" +
                       "    },\n" +
                       "    ...\n" +
                       "  ]\n" +
                       "}")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "packageId", value = "套餐ID", required = true, dataType = "Long", paramType = "path", dataTypeClass = Long.class),
        @ApiImplicitParam(name = "type", value = "题目类型：1练习题（返回100道），2模拟题（返回200道）", required = true, dataType = "Integer", paramType = "path", dataTypeClass = Integer.class),
        @ApiImplicitParam(name = "Authorization", value = "用户登录令牌", required = true, paramType = "header", dataType = "String")
    })
    @GetMapping("/package/{packageId}/questions/{type}")
    public AjaxResult getPackageQuestions(
            @PathVariable("packageId") Long packageId,
            @PathVariable("type") Integer type,
            HttpServletRequest request)
    {
        // 从Token中获取用户的openid
        String token = request.getHeader("Authorization");
        if (token == null || token.isEmpty()) {
            return AjaxResult.error("未登录");
        }
        
        // 根据Token查询用户信息
        WxUser wxUser = wxUserService.getWxUserByToken(token);
        if (wxUser == null) {
            return AjaxResult.error("用户不存在或登录已过期");
        }
        
        // 查询套餐信息
        ExamPackage examPackage = examPackageService.selectExamPackageByPackageId(packageId);
        if (examPackage == null) {
            return AjaxResult.error("套餐不存在");
        }
        
        // 查询用户是否有权限访问该套餐
        List<UserPackage> userPackages = userPackageService.selectUserPackageByUserId(wxUser.getUserId());
        boolean hasAccess = false;
        for (UserPackage userPackage : userPackages) {
            if (userPackage.getPackageId().equals(packageId) && "0".equals(userPackage.getStatus())) {
                Long orderId = userPackage.getOrderId();
                OrderInfo orderInfo = orderInfoService.selectOrderInfoByOrderId(orderId);
                if(new Date().after(orderInfo.getExpireTime())){
                    hasAccess = false;
                }else{
                    hasAccess = true;
                }
            }
        }
        
        if (!hasAccess&&"0".equals(wxUser.getIsFree())) {
            return AjaxResult.error("体验已结束或套餐已过期");
        }
        


        // 根据题目类型筛选
        List<ExamQuestion> resultList;
        if (type == 2) {
            // 查询题目
            List<ExamQuestion> questions = examQuestionService.list(
                    new QueryWrapper<ExamQuestion>()
                            .eq("category_id", examPackage.getCategoryId())
                            .eq("status", "0")
                            .eq("del_flag", "0")
                            .eq("is_study","0")
                            .orderByAsc("RAND()"));
            // 练习题，返回100道
            if(hasAccess){
                resultList = questions.stream().limit(100).collect(Collectors.toList());
            }else{
                resultList = questions.stream().limit(5).collect(Collectors.toList());
            }
        } else if (type == 1) {
            List<ExamQuestion> questions = examQuestionService.list(
                    new QueryWrapper<ExamQuestion>()
                            .eq("category_id", examPackage.getCategoryId())
                            .eq("status", "0")
                            .eq("del_flag", "0")
                            .eq("is_mock","1")
                            .eq("is_study","0")
                            .orderByAsc("RAND()"));
            // 模拟题，返回200道
            if(hasAccess){
                resultList = questions.stream().limit(200).collect(Collectors.toList());
            }else{
                resultList = questions.stream().limit(5).collect(Collectors.toList());
            }
        }else{
            return AjaxResult.error("题目类型参数错误");
        }
        
        return AjaxResult.success(resultList);
    }


    /**
     * 获取套餐相关的题目
     */
    @ApiOperation(value = "获取套餐题目")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "packageId", value = "套餐ID", required = true, dataType = "Long", paramType = "path", dataTypeClass = Long.class),
            @ApiImplicitParam(name = "type", value = "题目类型：1练习题（返回100道），2模拟题（返回200道）", required = true, dataType = "Integer", paramType = "path", dataTypeClass = Integer.class),
            @ApiImplicitParam(name = "Authorization", value = "用户登录令牌", required = true, paramType = "header", dataType = "String")
    })
    @GetMapping("/getTitles/{packageId}")
    public AjaxResult getTitles(
            @PathVariable("packageId") Long packageId, HttpServletRequest request)
    {
        // 从Token中获取用户的openid
        String token = request.getHeader("Authorization");
        if (token == null || token.isEmpty()) {
            return AjaxResult.error("未登录");
        }

        // 根据Token查询用户信息
        WxUser wxUser = wxUserService.getWxUserByToken(token);
        if (wxUser == null) {
            return AjaxResult.error("用户不存在或登录已过期");
        }

        // 查询套餐信息
        ExamPackage examPackage = examPackageService.selectExamPackageByPackageId(packageId);
        if (examPackage == null) {
            return AjaxResult.error("套餐不存在");
        }

        // 查询用户是否有权限访问该套餐
        List<UserPackage> userPackages = userPackageService.selectUserPackageByUserId(wxUser.getUserId());

        List<String> list = examQuestionService.getSubjectsByPackageId(examPackage.getCategoryId()).stream().filter(x->!"".equals(x)).collect(Collectors.toList());

        return AjaxResult.success(list);
    }

    @GetMapping("/questionList/{packageId}/{title}")
    public AjaxResult getStudyQuestionsList(
            @PathVariable("title") String title, @PathVariable("packageId") Long packageId, HttpServletRequest request)
    {
        // 从Token中获取用户的openid
        String token = request.getHeader("Authorization");
        if (token == null || token.isEmpty()) {
            return AjaxResult.error("未登录");
        }

        // 根据Token查询用户信息
        WxUser wxUser = wxUserService.getWxUserByToken(token);
        if (wxUser == null) {
            return AjaxResult.error("用户不存在或登录已过期");
        }

        // 查询套餐信息
        ExamPackage examPackage = examPackageService.selectExamPackageByPackageId(packageId);
        if (examPackage == null) {
            return AjaxResult.error("套餐不存在");
        }


        // 查询用户是否有权限访问该套餐
        List<UserPackage> userPackages = userPackageService.selectUserPackageByUserId(wxUser.getUserId());
        boolean hasAccess = false;
        for (UserPackage userPackage : userPackages) {
            if (userPackage.getPackageId().equals(packageId) && "0".equals(userPackage.getStatus())) {
                Long orderId = userPackage.getOrderId();
                OrderInfo orderInfo = orderInfoService.selectOrderInfoByOrderId(orderId);
                if(new Date().after(orderInfo.getExpireTime())){
                    hasAccess = false;
                }else{
                    hasAccess = true;
                }
                break;
            }
        }
        if (!hasAccess&&"0".equals(wxUser.getIsFree())) {
            return AjaxResult.error("体验已结束或套餐已过期");
        }
        List<ExamQuestion> list;
        if (hasAccess) {
            list = examQuestionService.list(new QueryWrapper<ExamQuestion>().eq("category_id", examPackage.getCategoryId()).eq("subject", title));
        }else{
            list = examQuestionService.list(new QueryWrapper<ExamQuestion>().eq("category_id", examPackage.getCategoryId()).eq("subject", title)).stream().limit(5).collect(Collectors.toList());
        }
        return AjaxResult.success(list);
    }


    /**
     * 新增考试记录
     */
    @PostMapping("/saveExamLog")
    @ApiOperation(value = "新增考试记录", notes = "新增考试记录")
    public AjaxResult addExam(@RequestBody ExamRecord examRecord,HttpServletRequest request)
    {
        String token = request.getHeader("Authorization");
        if (token == null || token.isEmpty()) {
            return AjaxResult.error("未登录");
        }

        // 根据Token查询用户信息
        WxUser wxUser = wxUserService.getWxUserByToken(token);
        if (wxUser == null) {
            return AjaxResult.error("用户不存在或登录已过期");
        }
        examRecord.setUserId(wxUser.getUserId());
        if(examRecord.getRecordId()!=null) {
            examRecordService.updateExamRecord(examRecord);
            return AjaxResult.success();
        }else{
            ExamRecord examRecord1 = new ExamRecord();
            examRecord1.setState("0");
            examRecord1.setUserId(wxUser.getUserId());
            List<ExamRecord> examRecords = examRecordService.selectExamRecordList(examRecord1);
            if(examRecords.size()>0){
                ExamRecord examRecord2 = examRecords.get(0);
                BeanUtils.copyProperties(examRecord,examRecord2,"recordId");
                return AjaxResult.success( examRecordService.updateExamRecord(examRecord2));
            }else{
                return toAjax(examRecordService.insertExamRecord(examRecord));
            }
        }
    }

    @GetMapping("/examLog")
    @ApiOperation(value = "查询考试记录列表", notes = "查询考试记录列表")
    public AjaxResult list(ExamRecord examRecord,HttpServletRequest request)
    {
        String token = request.getHeader("Authorization");
        if (token == null || token.isEmpty()) {
            return AjaxResult.error("未登录");
        }
        WxUser wxUser = wxUserService.getWxUserByToken(token);
        if (wxUser == null) {
            return AjaxResult.error("用户不存在或登录已过期");
        }
        examRecord.setUserId(wxUser.getUserId());
        startPage();
        List<ExamRecord> list = examRecordService.selectExamRecordList(examRecord);
        list.forEach(x->{
            // 计算完成率（正确率），保留两位小数
            x.setFinishRate(CalculateUtils.calculateFinishRate(
                x.getCorrectCount(), x.getWrongCount(), x.getEmptyCount()));

            // 判断是否合格
            Double score = x.getScore();
            if (score != null) {
                x.setIsPass(score > 60 ? "合格" : "不合格");
            } else {
                x.setIsPass("未评分");
            }
        });
        return AjaxResult.success(getDataTable(list));
    }

    @GetMapping("/checkExamByPackageId/{packageId}")
    @ApiOperation(value = "查询考试记录列表", notes = "查询考试记录列表")
    public AjaxResult checkExamByPackageId(@PathVariable("packageId") Long packageId, HttpServletRequest request)
    {
        String token = request.getHeader("Authorization");
        if (token == null || token.isEmpty()) {
            return AjaxResult.error("未登录");
        }
        WxUser wxUser = wxUserService.getWxUserByToken(token);
        if (wxUser == null) {
            return AjaxResult.error("用户不存在或登录已过期");
        }
        ExamRecord examRecord = new ExamRecord();
        examRecord.setUserId(wxUser.getUserId());
        examRecord.setPackageId(packageId);
        examRecord.setState("0");
        List<ExamRecord> examRecords = examRecordService.selectExamRecordList(examRecord);
        if(examRecords.size()>0){
            ExamRecord examRecord1 = examRecords.get(0);
            String finishIds = examRecord1.getFinishIds();
            String finishAnswer = examRecord1.getFinishAnswer();
            String[] finishAnswers = finishAnswer.split(",");
            String[] ids = finishIds.split(",");
            List<ExamQuestion> examQuestions = new ArrayList<>();
            List<ExamQuestion> forgetQuestion = new ArrayList<>();
            //ids转Integer类型的数组
            if(StringUtils.isNotEmpty(finishIds)){
                List<Integer> list = Arrays.stream(ids).map(Integer::parseInt).collect(Collectors.toList());
                examQuestions = examQuestionService.listByIds(list);
                List<ExamQuestion> finalExamQuestions = examQuestions;
                examQuestions.forEach(x->{
                    x.setIsRight(x.getCorrectAnswer().equals(finishAnswers[finalExamQuestions.indexOf(x)]));
                    x.setIsFinish(true);
                    x.setUserAnswer(finishAnswers[finalExamQuestions.indexOf(x)]);
                });
            }

            String forgetIds = examRecord1.getForgetIds();
            if(StringUtils.isNotEmpty(forgetIds)) {
                String[] split = forgetIds.split(",");
                List<Integer> forgetList = Arrays.stream(split).map(Integer::parseInt).collect(Collectors.toList());
                forgetQuestion = examQuestionService.listByIds(forgetList);
                forgetQuestion.forEach(x->{
                    x.setIsFinish(false);
                    x.setIsRight(false);
                });
            }
            examQuestions.addAll(forgetQuestion);
            examRecord1.setQuestions(examQuestions);
            return AjaxResult.success(examRecord1);
        }else{
            return AjaxResult.success(new HashMap<>());
        }
    }

} 