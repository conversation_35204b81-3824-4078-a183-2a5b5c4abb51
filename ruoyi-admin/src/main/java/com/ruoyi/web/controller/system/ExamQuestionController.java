package com.ruoyi.web.controller.system;

import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.system.domain.ExamPackage;
import com.ruoyi.system.service.IExamPackageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.ExamQuestion;
import com.ruoyi.system.service.IExamQuestionService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

/**
 * 题目Controller
 * 
 * <AUTHOR>
 */
@Api(tags = "题目管理")
@RestController
@RequestMapping("/system/question")
public class ExamQuestionController extends BaseController
{
    @Autowired
    private IExamQuestionService examQuestionService;
    @Autowired
    private IExamPackageService  packageService;
    /**
     * 查询题目列表
     */
    @ApiOperation(value = "获取题目列表", notes = "分页查询题目列表数据")
    @PreAuthorize("@ss.hasPermi('system:question:list')")
    @GetMapping("/list")
    public TableDataInfo list(ExamQuestion examQuestion)
    {
        startPage();
        List<ExamQuestion> list = examQuestionService.selectExamQuestionList(examQuestion);
        return getDataTable(list);
    }

    /**
     * 导出题目列表
     */
    @ApiOperation(value = "导出题目数据", notes = "导出所有符合条件的题目数据")
    @PreAuthorize("@ss.hasPermi('system:question:export')")
    @Log(title = "题目", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ExamQuestion examQuestion)
    {
        List<ExamQuestion> list = examQuestionService.selectExamQuestionList(examQuestion);
        ExcelUtil<ExamQuestion> util = new ExcelUtil<ExamQuestion>(ExamQuestion.class);
        util.exportExcel(response, list, "题目数据");
    }

    /**
     * 获取题目详细信息
     */
    @ApiOperation(value = "获取题目详细", notes = "根据题目ID获取题目详细信息")
    @ApiImplicitParam(name = "questionId", value = "题目ID", required = true, dataType = "Long", paramType = "path", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermi('system:question:query')")
    @GetMapping(value = "/{questionId}")
    public AjaxResult getInfo(@PathVariable("questionId") Long questionId)
    {
        return success(examQuestionService.selectExamQuestionByQuestionId(questionId));
    }

    /**
     * 新增题目
     */
    @ApiOperation(value = "新增题目", notes = "新增题目信息")
    @PreAuthorize("@ss.hasPermi('system:question:add')")
    @Log(title = "题目", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody ExamQuestion examQuestion)
    {
        examQuestion.setCreateBy(SecurityUtils.getUsername());
        return toAjax(examQuestionService.insertExamQuestion(examQuestion));
    }

    /**
     * 修改题目
     */
    @ApiOperation(value = "修改题目", notes = "修改题目信息")
    @PreAuthorize("@ss.hasPermi('system:question:edit')")
    @Log(title = "题目", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody ExamQuestion examQuestion)
    {
        examQuestion.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(examQuestionService.updateExamQuestion(examQuestion));
    }

    /**
     * 删除题目
     */
    @ApiOperation(value = "删除题目", notes = "根据题目ID删除题目信息")
    @ApiImplicitParam(name = "questionIds", value = "题目ID数组", required = true, dataType = "Long[]", paramType = "path", dataTypeClass = Long[].class)
    @PreAuthorize("@ss.hasPermi('system:question:remove')")
    @Log(title = "题目", businessType = BusinessType.DELETE)
    @DeleteMapping("/{questionIds}")
    public AjaxResult remove(@PathVariable Long[] questionIds)
    {
        return toAjax(examQuestionService.deleteExamQuestionByQuestionIds(questionIds));
    }
    
    /**
     * 查询套餐下的题目列表
     */
    @ApiOperation(value = "获取套餐题目列表", notes = "根据套餐ID和题目类型查询题目列表")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "packageId", value = "套餐ID", required = true, dataType = "Long", paramType = "path", dataTypeClass = Long.class),
        @ApiImplicitParam(name = "questionType", value = "题目类型", required = true, dataType = "String", paramType = "path", dataTypeClass = String.class)
    })
    @PreAuthorize("@ss.hasPermi('system:question:list')")
    @GetMapping("/listByPackage/{packageId}/{questionType}")
    public TableDataInfo listByPackage(@PathVariable("packageId") Long packageId, @PathVariable("questionType") String questionType)
    {
        startPage();
        List<ExamQuestion> list = examQuestionService.selectExamQuestionsByPackageId(packageId, questionType);
        return getDataTable(list);
    }


    
    /**
     * 导入题目数据
     */
    @ApiOperation(value = "导入题目数据", notes = "从Excel文件导入题目数据到指定套餐")
    @PreAuthorize("@ss.hasPermi('system:question:import')")
    @Log(title = "题目导入", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, Long packageId,Boolean updateSupport) throws Exception
    {
        if (StringUtils.isNull(file))
        {
            return error("请选择要导入的文件");
        }
        if (StringUtils.isNull(packageId))
        {
            return error("请选择题库套餐");
        }
        String message = examQuestionService.importExamQuestion(file.getInputStream(), packageId, updateSupport, SecurityUtils.getUsername());
        return success(message);
    }

    /**
     * 下载导入模板
     */
    @ApiOperation(value = "下载导入模板", notes = "下载题目数据导入的Excel模板")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<ExamQuestion> util = new ExcelUtil<ExamQuestion>(ExamQuestion.class);
        util.importTemplateExcel(response, "题目数据");
    }
} 