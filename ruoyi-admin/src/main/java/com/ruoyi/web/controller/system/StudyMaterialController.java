package com.ruoyi.web.controller.system;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.StudyMaterial;
import com.ruoyi.system.service.IStudyMaterialService;
import com.ruoyi.system.service.IExamPackageService;

/**
 * 学习资料Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/material")
public class StudyMaterialController extends BaseController
{
    @Autowired
    private IStudyMaterialService studyMaterialService;
    
    @Autowired
    private IExamPackageService examPackageService;

    /**
     * 查询学习资料列表
     */
    @PreAuthorize("@ss.hasPermi('system:material:list')")
    @GetMapping("/list")
    public TableDataInfo list(StudyMaterial studyMaterial)
    {
        startPage();
        List<StudyMaterial> list = studyMaterialService.selectStudyMaterialList(studyMaterial);
        return getDataTable(list);
    }

    /**
     * 导出学习资料列表
     */
    @PreAuthorize("@ss.hasPermi('system:material:export')")
    @Log(title = "学习资料", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(StudyMaterial studyMaterial)
    {
        List<StudyMaterial> list = studyMaterialService.selectStudyMaterialList(studyMaterial);
        ExcelUtil<StudyMaterial> util = new ExcelUtil<StudyMaterial>(StudyMaterial.class);
        return util.exportExcel(list, "学习资料数据");
    }

    /**
     * 获取学习资料详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:material:query')")
    @GetMapping(value = "/{materialId}")
    public AjaxResult getInfo(@PathVariable("materialId") Long materialId)
    {
        return success(studyMaterialService.selectStudyMaterialByMaterialId(materialId));
    }

    /**
     * 新增学习资料
     */
    @PreAuthorize("@ss.hasPermi('system:material:add')")
    @Log(title = "学习资料", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody StudyMaterial studyMaterial)
    {
        return toAjax(studyMaterialService.insertStudyMaterial(studyMaterial));
    }

    /**
     * 修改学习资料
     */
    @PreAuthorize("@ss.hasPermi('system:material:edit')")
    @Log(title = "学习资料", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody StudyMaterial studyMaterial)
    {
        return toAjax(studyMaterialService.updateStudyMaterial(studyMaterial));
    }

    /**
     * 删除学习资料
     */
    @PreAuthorize("@ss.hasPermi('system:material:remove')")
    @Log(title = "学习资料", businessType = BusinessType.DELETE)
    @DeleteMapping("/{materialIds}")
    public AjaxResult remove(@PathVariable Long[] materialIds)
    {
        return toAjax(studyMaterialService.deleteStudyMaterialByMaterialIds(materialIds));
    }
    
    /**
     * 根据套餐ID查询学习资料列表
     */
    @GetMapping("/listByPackage/{packageId}")
    public AjaxResult listByPackage(@PathVariable("packageId") Long packageId)
    {
        List<StudyMaterial> list = studyMaterialService.selectStudyMaterialListByPackageId(packageId);
        return success(list);
    }
    
    /**
     * 根据资料类型查询学习资料列表
     */
    @GetMapping("/listByType/{materialType}")
    public AjaxResult listByType(@PathVariable("materialType") String materialType)
    {
        List<StudyMaterial> list = studyMaterialService.selectStudyMaterialListByType(materialType);
        return success(list);
    }
    
    /**
     * 查看资料（增加浏览次数）
     */
    @PostMapping("/view/{materialId}")
    public AjaxResult viewMaterial(@PathVariable("materialId") Long materialId)
    {
        studyMaterialService.incrementViewCount(materialId);
        return success(studyMaterialService.selectStudyMaterialByMaterialId(materialId));
    }
    
    /**
     * 下载资料（增加下载次数）
     */
    @PostMapping("/download/{materialId}")
    public AjaxResult downloadMaterial(@PathVariable("materialId") Long materialId)
    {
        studyMaterialService.incrementDownloadCount(materialId);
        return success(studyMaterialService.selectStudyMaterialByMaterialId(materialId));
    }
} 