package com.ruoyi.web.controller.system;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.service.IStatisticsService;

/**
 * 数据统计 Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/statistics")
public class StatisticsController extends BaseController
{
    @Autowired
    private IStatisticsService statisticsService;

    /**
     * 获取首页统计数据
     */
    @PreAuthorize("@ss.hasPermi('system:statistics:list')")
    @GetMapping("/dashboard")
    public AjaxResult getDashboardStatistics()
    {
        // 获取统计数据
        Map<String, Object> data = statisticsService.getDashboardStatistics();
        return AjaxResult.success(data);
    }
    
    /**
     * 获取营业额趋势数据（最近7天）
     */
    @PreAuthorize("@ss.hasPermi('system:statistics:list')")
    @GetMapping("/revenue/trend")
    public AjaxResult getRevenueTrend()
    {
        // 获取营业额趋势数据
        Map<String, Object> data = statisticsService.getRevenueTrend();
        return AjaxResult.success(data);
    }
    
    /**
     * 获取指定时间段营业额统计
     */
    @PreAuthorize("@ss.hasPermi('system:statistics:list')")
    @GetMapping("/revenue/custom")
    public AjaxResult getCustomRangeRevenue(@RequestParam String startDate, @RequestParam String endDate)
    {
        // 获取指定时间段营业额统计
        Map<String, Object> data = statisticsService.getCustomRangeRevenue(startDate, endDate);
        return AjaxResult.success(data);
    }

    /**
     * 获取首页按渠道统计数据
     */
    @PreAuthorize("@ss.hasPermi('system:statistics:list')")
    @GetMapping("/dashboard/channel")
    public AjaxResult getDashboardChannelStatistics()
    {
        // 获取按渠道统计数据
        Map<String, Object> data = statisticsService.getDashboardChannelStatistics();
        return AjaxResult.success(data);
    }

    /**
     * 获取指定时间段按渠道营业额统计
     */
    @PreAuthorize("@ss.hasPermi('system:statistics:list')")
    @GetMapping("/revenue/channel/custom")
    public AjaxResult getCustomRangeChannelRevenue(@RequestParam String startDate, @RequestParam String endDate)
    {
        // 获取指定时间段按渠道营业额统计
        Map<String, Object> data = statisticsService.getCustomRangeChannelRevenue(startDate, endDate);
        return AjaxResult.success(data);
    }

    /**
     * 获取按渠道营业额趋势数据（最近7天）
     */
    @PreAuthorize("@ss.hasPermi('system:statistics:list')")
    @GetMapping("/revenue/channel/trend")
    public AjaxResult getChannelRevenueTrend()
    {
        // 获取按渠道营业额趋势数据
        Map<String, Object> data = statisticsService.getChannelRevenueTrend();
        return AjaxResult.success(data);
    }

    /**
     * 获取按套餐统计的订单数量
     */
    @PreAuthorize("@ss.hasPermi('system:statistics:list')")
    @GetMapping("/package/orders")
    public AjaxResult getPackageOrderStatistics()
    {
        // 获取按套餐统计的订单数量
        Map<String, Object> data = statisticsService.getPackageOrderStatistics();
        return AjaxResult.success(data);
    }
}