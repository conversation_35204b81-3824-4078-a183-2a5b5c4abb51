package com.ruoyi.web.controller.system;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.ExamMistake;
import com.ruoyi.system.service.IExamMistakeService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

/**
 * 错题Controller
 * 
 * <AUTHOR>
 */
@Api(tags = "错题管理")
@RestController
@RequestMapping("/system/mistake")
public class ExamMistakeController extends BaseController
{
    @Autowired
    private IExamMistakeService examMistakeService;

    /**
     * 查询错题列表
     */
    @ApiOperation(value = "获取错题列表", notes = "分页查询错题列表数据")
    @PreAuthorize("@ss.hasPermi('system:mistake:list')")
    @GetMapping("/list")
    public TableDataInfo list(ExamMistake examMistake)
    {
        startPage();
        List<ExamMistake> list = examMistakeService.selectExamMistakeList(examMistake);
        return getDataTable(list);
    }

    /**
     * 导出错题列表
     */
    @ApiOperation(value = "导出错题数据", notes = "导出所有符合条件的错题数据")
    @PreAuthorize("@ss.hasPermi('system:mistake:export')")
    @Log(title = "错题", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(ExamMistake examMistake)
    {
        List<ExamMistake> list = examMistakeService.selectExamMistakeList(examMistake);
        ExcelUtil<ExamMistake> util = new ExcelUtil<ExamMistake>(ExamMistake.class);
        return util.exportExcel(list, "错题数据");
    }

    /**
     * 获取错题详细信息
     */
    @ApiOperation(value = "获取错题详细", notes = "根据错题ID获取错题详细信息")
    @ApiImplicitParam(name = "mistakeId", value = "错题ID", required = true, dataType = "Long", paramType = "path", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermi('system:mistake:query')")
    @GetMapping(value = "/{mistakeId}")
    public AjaxResult getInfo(@PathVariable("mistakeId") Long mistakeId)
    {
        return success(examMistakeService.selectExamMistakeByMistakeId(mistakeId));
    }

    /**
     * 新增错题
     */
    @ApiOperation(value = "新增错题", notes = "新增错题记录")
    @PreAuthorize("@ss.hasPermi('system:mistake:add')")
    @Log(title = "错题", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ExamMistake examMistake)
    {
        return toAjax(examMistakeService.insertExamMistake(examMistake));
    }

    /**
     * 修改错题
     */
    @ApiOperation(value = "修改错题", notes = "修改错题记录")
    @PreAuthorize("@ss.hasPermi('system:mistake:edit')")
    @Log(title = "错题", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ExamMistake examMistake)
    {
        return toAjax(examMistakeService.updateExamMistake(examMistake));
    }

    /**
     * 删除错题
     */
    @ApiOperation(value = "删除错题", notes = "根据错题ID删除错题记录")
    @ApiImplicitParam(name = "mistakeIds", value = "错题ID数组", required = true, dataType = "Long[]", paramType = "path", dataTypeClass = Long[].class)
    @PreAuthorize("@ss.hasPermi('system:mistake:remove')")
    @Log(title = "错题", businessType = BusinessType.DELETE)
    @DeleteMapping("/{mistakeIds}")
    public AjaxResult remove(@PathVariable Long[] mistakeIds)
    {
        return toAjax(examMistakeService.deleteExamMistakeByMistakeIds(mistakeIds));
    }
    
    /**
     * 小程序查询用户错题列表
     */
    @ApiOperation(value = "获取用户错题列表", notes = "根据用户ID获取该用户的所有错题记录")
    @ApiImplicitParam(name = "userId", value = "用户ID", required = true, dataType = "Long", paramType = "path", dataTypeClass = Long.class)
    @GetMapping("/listByUserId/{userId}")
    public AjaxResult listByUserId(@PathVariable("userId") Long userId)
    {
        List<ExamMistake> list = examMistakeService.selectExamMistakeListByUserId(userId);
        return success(list);
    }
    
    /**
     * 小程序添加错题记录
     */
    @ApiOperation(value = "添加用户错题", notes = "添加用户错题记录，如已存在则更新错误次数")
    @PostMapping("/add")
    public AjaxResult addMistake(@RequestBody ExamMistake examMistake)
    {
        if (examMistake.getUserId() == null || examMistake.getQuestionId() == null)
        {
            return error("用户ID和题目ID不能为空");
        }
        
        // 检查是否已存在该错题记录
        ExamMistake existMistake = examMistakeService.selectExamMistakeByUserIdAndQuestionId(
                examMistake.getUserId(), examMistake.getQuestionId());
        
        if (existMistake != null)
        {
            // 已存在，更新错误次数和最后错误时间
            existMistake.setMistakeCount(existMistake.getMistakeCount() + 1);
            existMistake.setWrongAnswer(examMistake.getWrongAnswer());
            existMistake.setStatus("0"); // 设置为未复习状态
            return toAjax(examMistakeService.updateExamMistake(existMistake));
        }
        else
        {
            // 不存在，新增记录
            examMistake.setMistakeCount(1);
            examMistake.setStatus("0"); // 设置为未复习状态
            return toAjax(examMistakeService.insertExamMistake(examMistake));
        }
    }
    
    /**
     * 小程序更新错题状态（标记为已复习）
     */
    @ApiOperation(value = "更新错题状态", notes = "将错题标记为已复习状态")
    @PostMapping("/updateStatus")
    public AjaxResult updateStatus(@RequestBody ExamMistake examMistake)
    {
        if (examMistake.getMistakeId() == null)
        {
            return error("错题ID不能为空");
        }
        
        // 只更新状态
        ExamMistake updateMistake = new ExamMistake();
        updateMistake.setMistakeId(examMistake.getMistakeId());
        updateMistake.setStatus("1"); // 设置为已复习状态
        
        return toAjax(examMistakeService.updateExamMistakeStatus(updateMistake));
    }
    
    /**
     * 小程序删除错题记录
     */
    @ApiOperation(value = "删除用户错题", notes = "根据错题ID删除用户错题记录")
    @ApiImplicitParam(name = "mistakeId", value = "错题ID", required = true, dataType = "Long", paramType = "path", dataTypeClass = Long.class)
    @DeleteMapping("/delete/{mistakeId}")
    public AjaxResult deleteMistake(@PathVariable("mistakeId") Long mistakeId)
    {
        return toAjax(examMistakeService.deleteExamMistakeByMistakeId(mistakeId));
    }
} 