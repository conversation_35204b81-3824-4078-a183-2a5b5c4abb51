package com.ruoyi.web.controller.api;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.system.domain.*;
import com.ruoyi.system.service.*;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 小程序用户套餐API
 * 
 * <AUTHOR>
 */
@Api(tags = "小程序用户套餐API")
@RestController
@RequestMapping("/v1/userPackage")
public class ApiUserPackageController extends BaseController
{
    @Autowired
    private IUserPackageService userPackageService;
    
    @Autowired
    private IWxUserService wxUserService;

    @Autowired
    private IOrderInfoService orderInfoService;

    @Autowired
    private IExamQuestionService examQuestionService;
    @Autowired
    private IExamPackageService packageService;

    /**
     * 获取用户的有效套餐列表
     */
    @ApiOperation(value = "获取用户有效套餐列表", 
                notes = "获取当前登录用户的所有有效套餐\n" +
                       "返回结果格式：{\n" +
                       "  \"code\": 200,\n" +
                       "  \"msg\": \"操作成功\",\n" +
                       "  \"data\": [\n" +
                       "    {\n" +
                       "      \"userPackageId\": 用户套餐ID,\n" +
                       "      \"userId\": 用户ID,\n" +
                       "      \"packageId\": 套餐ID,\n" +
                       "      \"orderId\": 订单ID,\n" +
                       "      \"startTime\": \"开始时间\",\n" +
                       "      \"endTime\": \"结束时间\",\n" +
                       "      \"status\": \"状态（0有效 1已过期）\"\n" +
                       "    },\n" +
                       "    ...\n" +
                       "  ]\n" +
                       "}")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "Authorization", value = "用户登录令牌", required = true, paramType = "header", dataType = "String")
    })
    @GetMapping("/myList")
    public AjaxResult listUserPackages(HttpServletRequest request)
    {
        // 从Token中获取用户的openid
        String token = request.getHeader("Authorization");
        if (token == null || token.isEmpty()) {
            return AjaxResult.error("未登录");
        }
        
        // 根据Token查询用户信息
        WxUser wxUser = wxUserService.getWxUserByToken(token);
        if (wxUser == null) {
            return AjaxResult.error("用户不存在或登录已过期");
        }
        
        // 查询用户的有效套餐
        List<UserPackage> packages = userPackageService.selectUserPackageByUserId(wxUser.getUserId());
        List<UserPackage> collect = packages.stream().filter(x -> x.getEndTime().getTime() > System.currentTimeMillis()).collect(Collectors.toList());
        return AjaxResult.success(collect);
    }

    @ApiOperation(value = "获取所有套餐",
            notes = "  \"msg\": \"操作成功\",\n" +
                    "    \"code\": 200,\n" +
                    "    \"data\": [\n" +
                    "        {\n" +
                    "            \"createBy\": \"admin\",\n" +
                    "            \"createTime\": \"2025-05-17 18:13:25\",\n" +
                    "            \"updateBy\": \"ry\",\n" +
                    "            \"updateTime\": \"2025-05-19 14:13:42\",\n" +
                    "            \"remark\": \"1\",\n" +
                    "            \"packageId\": 102,\n" +
                    "            \"packageName\": \"监控实操\",\n" +
                    "            \"categoryId\": 107,\n" +
                    "            \"introduction\": \"1\",\n" +
                    "            \"price\": 111.00,\n" +
                    "            \"validityDays\": 30,\n" +
                    "            \"coverImage\": \"https://hailanzhibo.oss-cn-beijing.aliyuncs.com/file/1747635218540.png\",\n" +
                    "            \"status\": \"0\",\n" +
                    "            \"delFlag\": \"0\",\n" +
                    "            \"categoryName\": \"实操\"\n" +
                    "        }]")
    @GetMapping("/list")
    public AjaxResult myList(HttpServletRequest request)
    {
        // 查询用户的有效套餐
        List<ExamPackage> examPackages = packageService.selectExamPackageAll();
        return AjaxResult.success(examPackages);
    }
    
    /**
     * 获取套餐详情
     */
    @ApiOperation(value = "获取套餐详情", 
                notes = "根据ID获取用户套餐详情\n" +
                       "返回结果格式：{\n" +
                       "  \"code\": 200,\n" +
                       "  \"msg\": \"操作成功\",\n" +
                       "  \"data\": {\n" +
                       "    \"userPackageId\": 用户套餐ID,\n" +
                       "    \"userId\": 用户ID,\n" +
                       "    \"packageId\": 套餐ID,\n" +
                       "    \"orderId\": 订单ID,\n" +
                       "    \"startTime\": \"开始时间\",\n" +
                       "    \"endTime\": \"结束时间\",\n" +
                       "    \"status\": \"状态（0有效 1已过期）\",\n" +
                       "    \"examPackage\": {\n" +
                       "      \"packageId\": 套餐ID,\n" +
                       "      \"packageName\": \"套餐名称\",\n" +
                       "      \"introduction\": \"套餐介绍\"\n" +
                       "    },\n" +
                       "    \"orderInfo\": {\n" +
                       "      \"orderId\": 订单ID,\n" +
                       "      \"orderNo\": \"订单编号\",\n" +
                       "      \"amount\": 订单金额\n" +
                       "    }\n" +
                       "  }\n" +
                       "}")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "userPackageId", value = "用户套餐ID", required = true, dataType = "Long", paramType = "path", dataTypeClass = Long.class),
        @ApiImplicitParam(name = "Authorization", value = "用户登录令牌", required = true, paramType = "header", dataType = "String")
    })
    @GetMapping("/{userPackageId}")
    public AjaxResult getPackageDetail(@PathVariable("userPackageId") Long userPackageId, HttpServletRequest request)
    {
        // 从Token中获取用户的openid
        String token = request.getHeader("Authorization");
        if (token == null || token.isEmpty()) {
            return AjaxResult.error("未登录");
        }
        
        // 根据Token查询用户信息
        WxUser wxUser = wxUserService.getWxUserByToken(token);
        if (wxUser == null) {
            return AjaxResult.error("用户不存在或登录已过期");
        }
        
        // 查询套餐详情
        UserPackage userPackage = userPackageService.selectUserPackageByUserPackageId(userPackageId);
        if (userPackage == null) {
            return AjaxResult.error("套餐不存在");
        }
        
        // 验证套餐是否属于当前用户
        if (!wxUser.getUserId().equals(userPackage.getUserId())) {
            return AjaxResult.error("无权访问此套餐");
        }
        
        return AjaxResult.success(userPackage);
    }

    /**
     * 查询套餐下的题目列表
     */
    @ApiOperation(value = "获取套餐题目列表", 
                notes = "根据套餐ID和题目类型查询题目列表\n" +
                       "返回结果格式：{\n" +
                       "  \"code\": 200,\n" +
                       "  \"msg\": \"操作成功\",\n" +
                       "  \"data\": [\n" +
                       "    {\n" +
                       "      \"questionId\": 题目ID,\n" +
                       "      \"subject\": \"题目主题\",\n" +
                       "      \"questionType\": \"题目类型（1单选 2多选 3判断）\",\n" +
                       "      \"questionContent\": \"题目内容\",\n" +
                       "      \"optionA\": \"选项A\",\n" +
                       "      \"optionB\": \"选项B\",\n" +
                       "      \"optionC\": \"选项C\",\n" +
                       "      \"optionD\": \"选项D\"\n" +
                       "    },\n" +
                       "    ...\n" +
                       "  ]\n" +
                       "}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "packageId", value = "套餐ID", required = true, dataType = "Long", paramType = "path", dataTypeClass = Long.class),
            @ApiImplicitParam(name = "questionType", value = "题目类型：1练习题（返回100道），2模拟题（返回200道）", required = true, dataType = "Long", paramType = "path", dataTypeClass = Long.class)
    })
    @GetMapping("/listByPackage/{packageId}/{questionType}")
    public AjaxResult wxListByPackage(@PathVariable("packageId") Long packageId,@PathVariable("questionType") Long questionType)
    {
        ExamPackage packageServiceById = packageService.getById(packageId);
        List<ExamQuestion> list = examQuestionService.list(new QueryWrapper<ExamQuestion>().eq("category_id", packageServiceById.getCategoryId()));
        if(questionType==1){//练习
            //随机出100道题
            list = examQuestionService.list(new QueryWrapper<ExamQuestion>().eq("category_id", packageServiceById.getCategoryId()).eq("is_mock","0").eq("is_study","0"));
            list = list.stream().limit(100).collect(Collectors.toList());

        }
        if(questionType==2){//模拟
            //随机出200道题
            list = examQuestionService.list(new QueryWrapper<ExamQuestion>().eq("is_mock","1").eq("is_study","0"));
            list = list.stream().limit(200).collect(Collectors.toList());
        }

        if(questionType==3){//模拟
            //随机出200道题
            list = examQuestionService.list(new QueryWrapper<ExamQuestion>().eq("category_id", packageServiceById.getCategoryId()).eq("is_study","1"));
        }
        return AjaxResult.success(list);
    }

    /**
     * 获取订单详细信息
     */
    @ApiOperation(value = "获取订单详细", notes = "根据订单ID获取订单详细信息")
    @ApiImplicitParam(name = "orderId", value = "订单ID", required = true, dataType = "Long", paramType = "path", dataTypeClass = Long.class)
    @GetMapping(value = "/order/{orderId}")
    public AjaxResult getInfo(@PathVariable("orderId") Long orderId)
    {
        return success(orderInfoService.selectOrderInfoByOrderId(orderId));
    }


    /**
     * 获取订单详细信息
     */
    @ApiOperation(value = "获取所有订单信息", notes = "获取所有订单信息")
    @GetMapping(value = "/orderList/{status}")
    public AjaxResult list(HttpServletRequest request,@PathVariable("status") String status)
    {
        // 从Token中获取用户的openid
        String token = request.getHeader("Authorization");
        if (token == null || token.isEmpty()) {
            return AjaxResult.error("未登录");
        }

        // 根据Token查询用户信息
        WxUser wxUser = wxUserService.getWxUserByToken(token);
        if (wxUser == null) {
            return AjaxResult.error("用户不存在或登录已过期");
        }
        OrderInfo orderInfo = new OrderInfo();
        orderInfo.setUserId(wxUser.getUserId());
        if(status.equals("0")||status.equals("1")){
            orderInfo.setStatus(status);
        }
        List<OrderInfo> orderInfos = orderInfoService.selectOrderInfoListByUserId(orderInfo);
        if("2".equals(status)){
            orderInfos = orderInfos.stream().filter(x->isDateWithDays(x.getCreateTime(),x.getExamPackage().getValidityDays())).collect(Collectors.toList());
        }
        return success(orderInfos);
    }

    public static boolean isDateWithDays(Date specifiedDate,int days) {
        // 获取当前日期
        Date currentDate = new Date();

        // 创建Calendar对象并设置指定日期
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(specifiedDate);
        // 指定日期加30天后的日期
        calendar.add(Calendar.DAY_OF_YEAR, days);

        // 比较当前日期与指定日期加30天后的日期
        return currentDate.after(calendar.getTime());
    }

} 