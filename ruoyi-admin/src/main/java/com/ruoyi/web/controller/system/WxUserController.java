package com.ruoyi.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.WxUser;
import com.ruoyi.system.service.IWxUserService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

/**
 * 微信用户Controller
 * 
 * <AUTHOR>
 */
@Api(tags = "微信用户管理")
@RestController
@RequestMapping("/system/wxuser")
public class WxUserController extends BaseController
{
    @Autowired
    private IWxUserService wxUserService;

    /**
     * 查询微信用户列表
     */
    @ApiOperation(value = "获取微信用户列表", notes = "分页查询微信用户列表数据")
    @PreAuthorize("@ss.hasPermi('system:wxuser:list')")
    @GetMapping("/list")
    public TableDataInfo list(WxUser wxUser)
    {
        startPage();
        List<WxUser> list = wxUserService.selectWxUserList(wxUser);
        return getDataTable(list);
    }

    /**
     * 导出微信用户列表
     */
    @ApiOperation(value = "导出微信用户", notes = "导出所有符合条件的微信用户数据")
    @PreAuthorize("@ss.hasPermi('system:wxuser:export')")
    @Log(title = "微信用户", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WxUser wxUser)
    {
        List<WxUser> list = wxUserService.selectWxUserList(wxUser);
        ExcelUtil<WxUser> util = new ExcelUtil<WxUser>(WxUser.class);
        util.exportExcel(response, list, "微信用户数据");
    }

    /**
     * 获取微信用户详细信息
     */
    @ApiOperation(value = "获取微信用户详细", notes = "根据用户ID获取微信用户详细信息")
    @ApiImplicitParam(name = "userId", value = "用户ID", required = true, dataType = "Long", paramType = "path", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermi('system:wxuser:query')")
    @GetMapping(value = "/{userId}")
    public AjaxResult getInfo(@PathVariable("userId") Long userId)
    {
        return success(wxUserService.selectWxUserByUserId(userId));
    }

    /**
     * 新增微信用户
     */
    @ApiOperation(value = "新增微信用户", notes = "新增微信用户信息")
    @PreAuthorize("@ss.hasPermi('system:wxuser:add')")
    @Log(title = "微信用户", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody WxUser wxUser)
    {
        return toAjax(wxUserService.insertWxUser(wxUser));
    }

    /**
     * 修改微信用户
     */
    @ApiOperation(value = "修改微信用户", notes = "修改微信用户信息")
    @PreAuthorize("@ss.hasPermi('system:wxuser:edit')")
    @Log(title = "微信用户", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WxUser wxUser)
    {
        return toAjax(wxUserService.updateWxUser(wxUser));
    }

    /**
     * 删除微信用户
     */
    @ApiOperation(value = "删除微信用户", notes = "根据用户ID删除微信用户信息")
    @ApiImplicitParam(name = "userIds", value = "用户ID数组", required = true, dataType = "Long[]", paramType = "path", dataTypeClass = Long[].class)
    @PreAuthorize("@ss.hasPermi('system:wxuser:remove')")
    @Log(title = "微信用户", businessType = BusinessType.DELETE)
    @DeleteMapping("/{userIds}")
    public AjaxResult remove(@PathVariable Long[] userIds)
    {
        return toAjax(wxUserService.deleteWxUserByUserIds(userIds));
    }
    
    /**
     * 微信小程序登录接口
     */
    @ApiOperation(value = "微信小程序登录", notes = "使用openid和sessionKey登录微信小程序")
    @PostMapping("/wxlogin")
    public AjaxResult wxLogin(@RequestBody WxUser wxUser)
    {
        if (wxUser.getOpenid() == null || wxUser.getOpenid().isEmpty())
        {
            return error("openid不能为空");
        }
        
        WxUser user = wxUserService.loginWxUser(wxUser.getOpenid(), wxUser.getSessionKey());
        if (user == null)
        {
            // 用户不存在，需要注册
            return AjaxResult.success("用户不存在，请先注册", null);
        }
        return success(user);
    }
    
    /**
     * 微信小程序注册接口
     */
    @ApiOperation(value = "微信小程序注册", notes = "注册新的微信小程序用户")
    @PostMapping("/wxregister")
    public AjaxResult wxRegister(@RequestBody WxUser wxUser)
    {
        if (wxUser.getOpenid() == null || wxUser.getOpenid().isEmpty())
        {
            return error("openid不能为空");
        }
        
        // 检查用户是否已存在
        WxUser existUser = wxUserService.selectWxUserByOpenid(wxUser.getOpenid());
        if (existUser != null)
        {
            return error("用户已存在");
        }
        
        // 设置默认值
        wxUser.setStatus("0");  // 正常状态
        wxUser.setDelFlag("0"); // 未删除
        
        int rows = wxUserService.registerWxUser(wxUser);
        if (rows > 0)
        {
            WxUser newUser = wxUserService.selectWxUserByOpenid(wxUser.getOpenid());
            return success(newUser);
        }
        return error("注册失败");
    }
    
    /**
     * 更新微信用户信息
     */
    @ApiOperation(value = "更新微信用户信息", notes = "更新微信用户的基本信息")
    @PostMapping("/updateInfo")
    public AjaxResult updateInfo(@RequestBody WxUser wxUser)
    {
        if (wxUser.getUserId() == null)
        {
            return error("用户ID不能为空");
        }
        
        return toAjax(wxUserService.updateWxUser(wxUser));
    }
} 