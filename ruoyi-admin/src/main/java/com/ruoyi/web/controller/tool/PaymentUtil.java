package com.ruoyi.web.controller.tool;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class PaymentUtil {
	@Value("${wx.pay.mchId}")
	private String mchId;
	@Value("${wx.pay.mchKey}")
	private String mchKey;
	@Value("${wx.pay.notifyUrl}")
	private String notifyUrl;
	@Value("${wx.miniapp.appid}")
	private String appid;
	@Value("${wx.miniapp.secret}")
	private String secret;


	
	
    public String getWechatAppIdMiniProgram() {
    	return appid;
    }
    
    public String getWechatAppIdOfficialAccount() {
		return "";
    }
    
    public String getWechatAppIdOpen() {
		return "";
    }
    
    public String getWechatApiKey() {
		return mchKey;
    }
    
    public String getWechatAppSecretMiniProgram() {
		return secret;
    }
    
    public String getWechatAppSecretOfficialAccount() {
		return "";
    }
    
    public String getWechatMerchantId() {
		return mchId;
    }
    
    public String getWechatNotifyUrl() {
		return notifyUrl;
    }
    
    public String getWechatRefundNotifyUrl() {
		return "";
    }
    
    public String getAlipayAppId() {
		return "";
    }

    public String getAlipayPrivateKey() {
		return "";
    }
    
    public String getAlipayPublicKey() {
		return "";
    }
    
    public String getAlipayNotifyUrl() {
		return "";
    }
    
    public String getAlipayRefundNotifyUrl() {
		return "";
    }
    
    public String getAlipayReturnUrl() {
		return "";
    }
	
	/**
	 * 小程序通过code换取open id
	 */
	public String getWechatOpenId() {
		return "https://api.weixin.qq.com/sns/jscode2session?appid="+getWechatAppIdMiniProgram()+"&secret="+getWechatAppSecretMiniProgram()+"&js_code="+"CODE"+"&grant_type=authorization_code";
	}
	
	/**
	 * 微信公众号授权跳转地址(获取code)
	 */
	public String getWechatOauth2Redirect() {
		return "https://open.weixin.qq.com/connect/oauth2/authorize?appid="+getWechatAppIdOfficialAccount()+
		           "&redirect_uri=REDIRECTURL&response_type=code&scope=snsapi_base&state=qjh#wechat_redirect";	
	}
	
	/**
	 * 公众号通过code换取open id
	 */
	public String getWechatAccessTokenOfficialAccount() {
		return "https://api.weixin.qq.com/sns/oauth2/access_token?appid="+getWechatAppIdOfficialAccount()+"&secret="+getWechatAppSecretOfficialAccount()+"&code="+"CODE"+"&grant_type=authorization_code";	
	}
	
	public String getDomainName() {
		return "";
	}
}
