package com.ruoyi.web.controller.system;

import java.util.List;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.domain.ExamCategory;
import com.ruoyi.system.service.IExamCategoryService;

/**
 * 题库分类Controller
 * 
 * <AUTHOR>
 */
@Api(tags = "题库分类管理")
@RestController
@RequestMapping("/system/category")
public class ExamCategoryController extends BaseController
{
    @Autowired
    private IExamCategoryService examCategoryService;

    /**
     * 查询题库分类列表
     */
    @ApiOperation(value = "获取题库分类列表", notes = "分页查询题库分类列表数据")
    @PreAuthorize("@ss.hasPermi('system:category:list')")
    @GetMapping("/list")
    public TableDataInfo list(ExamCategory examCategory)
    {
//        startPage();
        List<ExamCategory> list = examCategoryService.selectExamCategoryList(examCategory);
        return getDataTable(list);
    }

    /**
     * 获取题库分类详细信息
     */
    @ApiOperation(value = "获取题库分类详细", notes = "根据分类ID获取题库分类详细信息")
    @ApiImplicitParam(name = "categoryId", value = "分类ID", required = true, dataType = "Long", paramType = "path", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermi('system:category:query')")
    @GetMapping(value = "/{categoryId}")
    public AjaxResult getInfo(@PathVariable("categoryId") Integer categoryId)
    {
        return success(examCategoryService.selectExamCategoryByCategoryId(categoryId));
    }

    /**
     * 获取题库分类树列表
     */
    @ApiOperation(value = "获取题库分类树列表", notes = "获取所有题库分类的树形结构数据")
    @PreAuthorize("@ss.hasPermi('system:category:list')")
    @GetMapping("/tree")
    public AjaxResult tree()
    {
        List<ExamCategory> categories = examCategoryService.selectExamCategoryAll();
        return success(examCategoryService.buildExamCategoryTree(categories));
    }

    /**
     * 新增题库分类
     */
    @ApiOperation(value = "新增题库分类", notes = "新增题库分类信息")
    @PreAuthorize("@ss.hasPermi('system:category:add')")
    @Log(title = "题库分类", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody ExamCategory examCategory)
    {
        examCategory.setCreateBy(SecurityUtils.getUsername());
        return toAjax(examCategoryService.insertExamCategory(examCategory));
    }

    /**
     * 修改题库分类
     */
    @ApiOperation(value = "修改题库分类", notes = "修改题库分类信息")
    @PreAuthorize("@ss.hasPermi('system:category:edit')")
    @Log(title = "题库分类", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody ExamCategory examCategory)
    {
        examCategory.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(examCategoryService.updateExamCategory(examCategory));
    }

    /**
     * 删除题库分类
     */
    @ApiOperation(value = "删除题库分类", notes = "根据分类ID删除题库分类信息")
    @ApiImplicitParam(name = "categoryIds", value = "分类ID数组", required = true, dataType = "Long[]", paramType = "path", dataTypeClass = Long[].class)
    @PreAuthorize("@ss.hasPermi('system:category:remove')")
    @Log(title = "题库分类", businessType = BusinessType.DELETE)
    @DeleteMapping("/{categoryIds}")
    public AjaxResult remove(@PathVariable Long[] categoryIds)
    {
        return toAjax(examCategoryService.deleteExamCategoryByCategoryIds(categoryIds));
    }
    
    /**
     * 获取题库分类选择框列表
     */
    @ApiOperation(value = "获取分类选择框列表", notes = "获取所有可用的题库分类列表用于下拉选择")
    @GetMapping("/optionselect")
    public AjaxResult optionselect()
    {
        List<ExamCategory> categories = examCategoryService.selectExamCategoryAll();
        return success(categories);
    }
} 