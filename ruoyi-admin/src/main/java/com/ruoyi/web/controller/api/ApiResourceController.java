package com.ruoyi.web.controller.api;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.domain.*;
import com.ruoyi.system.service.*;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 小程序做题相关API
 * 
 * <AUTHOR>
 */
@Api(tags = "小程序资源API")
@RestController
@RequestMapping("/v1/resource")
@Component("customApiResourceController")
public class ApiResourceController extends BaseController
{

    @Autowired
    private IWxUserService wxUserService;


    @Autowired
    private IStudyMaterialService studyMaterialService;

    @Autowired
    private IUserStudyProgressService userStudyProgressService;

    @Autowired
    private IUserPackageService userPackageService;

    @Autowired
    private IStudyRecordService studyRecordService;

    /**
     * 获取套�?�关联的所有资�?
     */
    @ApiOperation(value = "查寻全部资源", notes = "查寻全部资源")
    @GetMapping("/list/{packageId}/{resourceName}")
    public AjaxResult getPackageResources(@PathVariable("packageId") Long packageId,@PathVariable("resourceName") String resourceName, HttpServletRequest request)
    {
        String token = request.getHeader("Authorization");
        if (token == null || token.isEmpty()) {
            return AjaxResult.error("请重新登录");
        }
        WxUser wxUser = wxUserService.getWxUserByToken(token);
        if (wxUser == null) {
            return AjaxResult.error("用户不存在或登录已过期");
        }
        List<StudyMaterial> studyMaterials = studyMaterialService.selectStudyMaterialListByPackageId(packageId);
        List<StudyMaterial> collect = studyMaterials.stream().filter(x -> x.getTheme().equals(resourceName)).collect(Collectors.toList());
        UserStudyProgress progress = userStudyProgressService.selectUserStudyProgressByUserAndPackage(
                wxUser.getUserId(), packageId);

        // 找到套餐中的最小sortNum
        Integer minSortNum = null;
//        for (StudyMaterial material : collect) {
//            if (material.getSortNum() != null) {
//                if (minSortNum == null || material.getSortNum() < minSortNum) {
//                    minSortNum = material.getSortNum();
//                }
//            }
//        }

        // 检查用户进度是否刚好比最小sortNum小1
        boolean isValidProgress = false;
        if (progress != null && progress.getSortNum() != null ) {
            minSortNum = progress.getSortNum()+1;
        }

        for (int i = 0; i < collect.size(); i++) {
            if(progress == null) {
                // 没有学习进度时，只有第一个资料解锁
                if (i == 0||i == 1) {
                    collect.get(i).setIsLock(false);
                } else {
                    collect.get(i).setIsLock(true);
                }
            } else {
                // 有学习进度时的逻辑
                if (collect.get(i).getSortNum() == null) {
                    // sortNum为null的资料默认锁定
                    collect.get(i).setIsLock(true);
                } else {
                    // 根据用户进度是否刚好比最小sortNum小1来决定锁定状态
                    if (collect.get(i).getSortNum() ==  minSortNum||collect.get(i).getSortNum() ==2||collect.get(i).getSortNum() ==1) {
                        // 用户进度刚好比最小sortNum小1，返回false（解锁）
                        collect.get(i).setIsLock(false);
                    } else if(collect.get(i).getSortNum() <  minSortNum){
                        collect.get(i).setIsLock(false);
                    }else{
                        collect.get(i).setIsLock(true);
                    }
                }
            }
        }
        return AjaxResult.success(collect);
    }


    @ApiOperation(value = "查寻全部资源", notes = "查寻全部资源")
    @GetMapping("/checkTitle/{packageId}/{resourceName}")
    public AjaxResult titleList(@PathVariable("packageId") Long packageId,@PathVariable("resourceName") String resourceName, HttpServletRequest request)
    {
        String token = request.getHeader("Authorization");
        if (token == null || token.isEmpty()) {
            return AjaxResult.error("请重新登录");
        }
        WxUser wxUser = wxUserService.getWxUserByToken(token);
        if (wxUser == null) {
            return AjaxResult.error("用户不存在或登录已过期");
        }
        List<StudyMaterial> studyMaterials = studyMaterialService.selectStudyMaterialListByPackageId(packageId);
        List<StudyMaterial> collect = studyMaterials.stream().filter(x -> x.getTheme().equals(resourceName)).collect(Collectors.toList());
        UserStudyProgress progress = userStudyProgressService.selectUserStudyProgressByUserAndPackage(
                wxUser.getUserId(), packageId);

        for (int i = 0; i < collect.size(); i++) {
            if(progress == null) {
                UserPackage userPackage = new UserPackage();
                userPackage.setUserId(wxUser.getUserId());
                userPackage.setPackageId(packageId);
                List<UserPackage> userPackages = userPackageService.selectUserPackageList(userPackage);
                if(userPackages.size() == 0){
                    if(collect.get(0).getSortNum() == 1){
                        return AjaxResult.success(false);
                    }else{
                        return AjaxResult.success(true);
                    }
                }else{
                    return AjaxResult.success(false);
                }
            } else {
                 if (collect.get(0).getSortNum() <= progress.getSortNum()+1) {
                     return AjaxResult.success(false);
                }else{
                     return AjaxResult.success(true);
                 }
            }
        }
        return AjaxResult.success(true);
    }

    /**
     * 更新用户学习进度
     */
    @ApiOperation(value = "更新学习进度", notes = "更新用户在指定套餐下的学习进度")
    @PostMapping("/updateProgress")
    public AjaxResult updateStudyProgress(@RequestBody Map<String, Object> params, HttpServletRequest request)
    {
        // 1. 验证用户身份
        String token = request.getHeader("Authorization");
        if (token == null || token.isEmpty()) {
            return AjaxResult.error("请重新登录");
        }
        WxUser wxUser = wxUserService.getWxUserByToken(token);
        if (wxUser == null) {
            return AjaxResult.error("用户不存在或登录已过期");
        }

        // 2. 获取参数
        Long packageId = null;
        Integer sortNum = null;

        try {
            packageId = Long.valueOf(params.get("packageId").toString());
            sortNum = Integer.valueOf(params.get("sortNum").toString());
        } catch (Exception e) {
            return AjaxResult.error("参数格式错误");
        }

        if (packageId == null || sortNum == null) {
            return AjaxResult.error("套餐ID和排序号不能为空");
        }

        // 3. 更新学习进度
        try {
            int result = userStudyProgressService.updateUserStudyProgressBySortNum(
                wxUser.getUserId(), packageId, sortNum);

            if (result > 0) {
                // 4. 返回更新后的进度信息
                UserStudyProgress progress = userStudyProgressService.selectUserStudyProgressByUserAndPackage(
                    wxUser.getUserId(), packageId);

                Map<String, Object> resultData = new HashMap<>();
                resultData.put("packageId", packageId);
                resultData.put("currentSortNum", progress != null ? progress.getSortNum() : null);
                resultData.put("message", "学习进度更新成功");

                return AjaxResult.success("学习进度更新成功", resultData);
            } else {
                return AjaxResult.success("学习进度更新成功");
            }
        } catch (Exception e) {
            logger.error("更新学习进度失败", e);
            return AjaxResult.error("更新学习进度失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户学习进度
     */
    @ApiOperation(value = "获取学习进度", notes = "获取用户在指定套餐下的学习进度")
    @GetMapping("/getProgress/{packageId}")
    public AjaxResult getStudyProgress(@PathVariable("packageId") Long packageId, HttpServletRequest request)
    {
        // 1. 验证用户身份
        String token = request.getHeader("Authorization");
        if (token == null || token.isEmpty()) {
            return AjaxResult.error("请重新登录");
        }
        WxUser wxUser = wxUserService.getWxUserByToken(token);
        if (wxUser == null) {
            return AjaxResult.error("用户不存在或登录已过期");
        }

        // 2. 查询学习进度
        UserStudyProgress progress = userStudyProgressService.selectUserStudyProgressByUserAndPackage(
            wxUser.getUserId(), packageId);

        Map<String, Object> resultData = new HashMap<>();
        resultData.put("packageId", packageId);
        resultData.put("currentSortNum", progress != null ? progress.getSortNum() : null);
        resultData.put("hasProgress", progress != null);

        return AjaxResult.success(resultData);
    }

    /**
     * 获取用户所有学习进度
     */
    @ApiOperation(value = "获取所有学习进度", notes = "获取用户的所有学习进度")
    @GetMapping("/getAllProgress")
    public AjaxResult getAllStudyProgress(HttpServletRequest request)
    {
        // 1. 验证用户身份
        String token = request.getHeader("Authorization");
        if (token == null || token.isEmpty()) {
            return AjaxResult.error("请重新登录");
        }
        WxUser wxUser = wxUserService.getWxUserByToken(token);
        if (wxUser == null) {
            return AjaxResult.error("用户不存在或登录已过期");
        }

        // 2. 查询所有学习进度
        List<UserStudyProgress> progressList = userStudyProgressService.selectUserStudyProgressListByUserId(
            wxUser.getUserId());

        return AjaxResult.success(progressList);
    }

    /**
     * 保存学习记录
     */
    @ApiOperation(value = "保存学习记录", notes = "")
    @PostMapping("/updateRecord")
    public AjaxResult updateRecord(@RequestBody Map<String, Object> params, HttpServletRequest request)
    {
        // 1. 验证用户身份
        String token = request.getHeader("Authorization");
        if (token == null || token.isEmpty()) {
            return AjaxResult.error("请重新登录");
        }
        WxUser wxUser = wxUserService.getWxUserByToken(token);
        if (wxUser == null) {
            return AjaxResult.error("用户不存在或登录已过期");
        }

        // 2. 获取参数
        Long packageId = null;
        Long materialId = null;
        String materialType = null;

        try {
            packageId = Long.valueOf(params.get("packageId").toString());
            materialId = Long.valueOf(params.get("materialId").toString());
            materialType = params.get("materialType").toString();
        } catch (Exception e) {
            return AjaxResult.error("参数格式错误");
        }
        StudyRecord studyRecord = new StudyRecord();
        studyRecord.setUserId(wxUser.getUserId());
        studyRecord.setMaterialId(materialId);
        studyRecord.setPackageId(packageId);
        studyRecord.setMaterialType(materialType);
        List<StudyRecord> studyRecords = studyRecordService.selectStudyRecordList(studyRecord);
        if(studyRecords.size()>0){
            studyRecord = studyRecords.get(0);
            studyRecord.setStudyTime(studyRecord.getStudyTime()+1);
            studyRecordService.updateStudyRecord(studyRecord);
        }else{
            studyRecord.setStudyTime(1);
            studyRecordService.insertStudyRecord(studyRecord);
        }
        if (packageId == null) {
            return AjaxResult.error("套餐ID和排序号不能为空");
        }

        // 3. 更新学习进度
        try {

                return AjaxResult.success("学习进度更新成功");

        } catch (Exception e) {
            logger.error("更新学习进度失败", e);
            return AjaxResult.error("更新学习进度失败：" + e.getMessage());
        }
    }
}