package com.ruoyi.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.UserStudyProgress;
import com.ruoyi.system.service.IUserStudyProgressService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 用户学习进度Controller
 * 
 * <AUTHOR>
 */
@Api(tags = "用户学习进度管理")
@RestController
@RequestMapping("/system/userStudyProgress")
public class UserStudyProgressController extends BaseController
{
    @Autowired
    private IUserStudyProgressService userStudyProgressService;

    /**
     * 查询用户学习进度列表
     */
    @ApiOperation("查询用户学习进度列表")
    @PreAuthorize("@ss.hasPermi('system:userStudyProgress:list')")
    @GetMapping("/list")
    public TableDataInfo list(UserStudyProgress userStudyProgress)
    {
        startPage();
        List<UserStudyProgress> list = userStudyProgressService.selectUserStudyProgressList(userStudyProgress);
        return getDataTable(list);
    }

    /**
     * 导出用户学习进度列表
     */
    @ApiOperation("导出用户学习进度列表")
    @PreAuthorize("@ss.hasPermi('system:userStudyProgress:export')")
    @Log(title = "用户学习进度", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, UserStudyProgress userStudyProgress)
    {
        List<UserStudyProgress> list = userStudyProgressService.selectUserStudyProgressList(userStudyProgress);
        ExcelUtil<UserStudyProgress> util = new ExcelUtil<UserStudyProgress>(UserStudyProgress.class);
        util.exportExcel(response, list, "用户学习进度数据");
    }

    /**
     * 获取用户学习进度详细信息
     */
    @ApiOperation("获取用户学习进度详细信息")
    @PreAuthorize("@ss.hasPermi('system:userStudyProgress:query')")
    @GetMapping(value = "/{progressId}")
    public AjaxResult getInfo(@PathVariable("progressId") Long progressId)
    {
        return success(userStudyProgressService.selectUserStudyProgressByProgressId(progressId));
    }

    /**
     * 新增用户学习进度
     */
    @ApiOperation("新增用户学习进度")
    @PreAuthorize("@ss.hasPermi('system:userStudyProgress:add')")
    @Log(title = "用户学习进度", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody UserStudyProgress userStudyProgress)
    {
        return toAjax(userStudyProgressService.insertUserStudyProgress(userStudyProgress));
    }

    /**
     * 修改用户学习进度
     */
    @ApiOperation("修改用户学习进度")
    @PreAuthorize("@ss.hasPermi('system:userStudyProgress:edit')")
    @Log(title = "用户学习进度", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody UserStudyProgress userStudyProgress)
    {
        return toAjax(userStudyProgressService.updateUserStudyProgress(userStudyProgress));
    }

    /**
     * 删除用户学习进度
     */
    @ApiOperation("删除用户学习进度")
    @PreAuthorize("@ss.hasPermi('system:userStudyProgress:remove')")
    @Log(title = "用户学习进度", businessType = BusinessType.DELETE)
	@DeleteMapping("/{progressIds}")
    public AjaxResult remove(@PathVariable Long[] progressIds)
    {
        return toAjax(userStudyProgressService.deleteUserStudyProgressByProgressIds(progressIds));
    }

    /**
     * 根据用户ID查询学习进度
     */
    @ApiOperation("根据用户ID查询学习进度")
    @PreAuthorize("@ss.hasPermi('system:userStudyProgress:query')")
    @GetMapping("/user/{userId}")
    public AjaxResult getProgressByUserId(@PathVariable("userId") Long userId)
    {
        List<UserStudyProgress> list = userStudyProgressService.selectUserStudyProgressListByUserId(userId);
        return success(list);
    }

    /**
     * 根据用户ID和套餐ID查询学习进度
     */
    @ApiOperation("根据用户ID和套餐ID查询学习进度")
    @PreAuthorize("@ss.hasPermi('system:userStudyProgress:query')")
    @GetMapping("/user/{userId}/package/{packageId}")
    public AjaxResult getProgressByUserAndPackage(@PathVariable("userId") Long userId, 
                                                  @PathVariable("packageId") Long packageId)
    {
        UserStudyProgress progress = userStudyProgressService.selectUserStudyProgressByUserAndPackage(userId, packageId);
        return success(progress);
    }
}
