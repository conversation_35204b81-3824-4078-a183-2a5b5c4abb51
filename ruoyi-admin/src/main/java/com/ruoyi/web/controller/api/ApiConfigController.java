package com.ruoyi.web.controller.api;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.domain.SysConfig;
import com.ruoyi.system.service.ISysConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
@RestController
@RequestMapping("/v1/config")
public class ApiConfigController {
    @Autowired
    private ISysConfigService configService;

    @GetMapping(value = "/getList")
    public AjaxResult getInfo()
    {
        HashMap<String, Object> map = new HashMap<>();
        SysConfig phone = configService.selectConfigById(7l);
        SysConfig name = configService.selectConfigById(9l);
        SysConfig telephone = configService.selectConfigById(8l);
        SysConfig video = configService.selectConfigById(103l);
        map.put("phone", phone.getConfigValue());
        map.put("name", name.getConfigValue());
        map.put("video", video.getConfigValue());
        map.put("telephone", telephone.getConfigValue());
        return AjaxResult.success(map);
    }
}
