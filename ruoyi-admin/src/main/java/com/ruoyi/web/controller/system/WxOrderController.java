package com.ruoyi.web.controller.system;

import java.util.List;

import com.ruoyi.system.domain.WxUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.OrderInfo;
import com.ruoyi.system.service.IOrderInfoService;
import com.ruoyi.system.service.IWxUserService;
import com.ruoyi.system.service.IExamPackageService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

import javax.servlet.http.HttpServletRequest;

/**
 * 微信小程序订单Controller
 * 
 * <AUTHOR>
 */
@Api(tags = "微信小程序订单管理")
@RestController
@RequestMapping("/system/wxorder")
public class WxOrderController extends BaseController
{
    @Autowired
    private IOrderInfoService orderInfoService;
    
    @Autowired
    private IWxUserService wxUserService;
    
    @Autowired
    private IExamPackageService examPackageService;

    /**
     * 用户订单列表
     */
    @ApiOperation(value = "获取用户订单列表", notes = "根据用户ID获取该用户的订单列表")
    @ApiImplicitParam(name = "userId", value = "用户ID", required = true, dataType = "Long", paramType = "path", dataTypeClass = Long.class)
    @GetMapping("/list/{userId}")
    public AjaxResult list(@PathVariable("userId") Long userId,@PathVariable("status") Integer status)
    {
        OrderInfo orderInfo = new OrderInfo();
        orderInfo.setUserId(userId);
        List<OrderInfo> list = orderInfoService.selectOrderInfoListByUserId(orderInfo);
        return success(list);
    }

    /**
     * 获取订单详情
     */
    @ApiOperation(value = "获取订单详情", notes = "根据订单编号获取订单详细信息")
    @ApiImplicitParam(name = "orderNo", value = "订单编号", required = true, dataType = "String", paramType = "path", dataTypeClass = String.class)
    @GetMapping("/{orderNo}")
    public AjaxResult getInfo(@PathVariable("orderNo") String orderNo)
    {
        return success(orderInfoService.selectOrderInfoByOrderNo(orderNo));
    }

    /**
     * 创建订单
     */
    @ApiOperation(value = "创建订单", notes = "根据用户ID和套餐ID创建新的订单")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "userId", value = "用户ID", required = true, dataType = "Long", dataTypeClass = Long.class),
        @ApiImplicitParam(name = "packageId", value = "套餐ID", required = true, dataType = "Long", dataTypeClass = Long.class)
    })
    @Log(title = "创建订单", businessType = BusinessType.INSERT)
    @PostMapping("/create")
    public AjaxResult create(@RequestBody OrderInfo orderInfo, HttpServletRequest request)
    {
        // 从Token中获取用户的openid
        String token = request.getHeader("Authorization");
        if (token == null || token.isEmpty()) {
            return AjaxResult.error("未登录");
        }

        // 根据Token查询用户信息
        WxUser wxUser = wxUserService.getWxUserByToken(token);
        if (wxUser == null) {
            return AjaxResult.error("用户不存在或登录已过期");
        }
        if (orderInfo.getPackageId() == null)
        {
            return error("套餐ID不能为空");
        }
        OrderInfo order = orderInfoService.createOrder(wxUser.getUserId(), orderInfo.getPackageId());
        return success(order);
    }

    /**
     * 支付订单
     */
    @ApiOperation(value = "支付订单", notes = "支付订单，需提供订单编号、支付渠道和交易流水号")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "orderNo", value = "订单编号", required = true, dataType = "String", dataTypeClass = String.class),
        @ApiImplicitParam(name = "payChannel", value = "支付渠道", required = true, dataType = "String", dataTypeClass = String.class),
        @ApiImplicitParam(name = "transactionId", value = "交易流水号", required = true, dataType = "String", dataTypeClass = String.class)
    })
    @Log(title = "支付订单", businessType = BusinessType.UPDATE)
    @PostMapping("/pay")
    public AjaxResult pay(@RequestBody OrderInfo orderInfo)
    {
        if (orderInfo.getOrderNo() == null || orderInfo.getOrderNo().isEmpty())
        {
            return error("订单编号不能为空");
        }
//        if (orderInfo.getPayChannel() == null || orderInfo.getPayChannel().isEmpty())
//        {
//            return error("支付渠道不能为空");
//        }
        if (orderInfo.getTransactionId() == null || orderInfo.getTransactionId().isEmpty())
        {
            return error("交易流水号不能为空");
        }
        
        int rows = orderInfoService.payOrder(orderInfo.getOrderNo(), orderInfo.getPayChannel(), orderInfo.getTransactionId());
        if (rows > 0)
        {
            return success(orderInfoService.selectOrderInfoByOrderNo(orderInfo.getOrderNo()));
        }
        return error("支付失败");
    }

    /**
     * 取消订单
     */
    @ApiOperation(value = "取消订单", notes = "取消待支付状态的订单")
    @ApiImplicitParam(name = "orderNo", value = "订单编号", required = true, dataType = "String", dataTypeClass = String.class)
    @Log(title = "取消订单", businessType = BusinessType.UPDATE)
    @PostMapping("/cancel")
    public AjaxResult cancel(@RequestBody OrderInfo orderInfo)
    {
        if (orderInfo.getOrderNo() == null || orderInfo.getOrderNo().isEmpty())
        {
            return error("订单编号不能为空");
        }
        
        int rows = orderInfoService.cancelOrder(orderInfo.getOrderNo());
        if (rows > 0)
        {
            return success();
        }
        return error("取消失败");
    }
} 