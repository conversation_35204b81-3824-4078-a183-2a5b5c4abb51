package com.ruoyi.web.controller.api;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import com.aliyun.oss.common.utils.HttpUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.http.HttpUtils;
import com.ruoyi.system.domain.WxUser;
import com.ruoyi.system.service.IWxUserService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.client.RestTemplate;

/**
 * 微信小程序用户API
 * 
 * <AUTHOR>
 */
@Api(tags = "微信小程序用户API")
@RestController
@RequestMapping("/v1/wxuser")
public class ApiWxUserController extends BaseController
{
    private static final Logger log = LoggerFactory.getLogger(ApiWxUserController.class);
    
    @Autowired
    private IWxUserService wxUserService;
    
    @Value("${wx.miniapp.appid}")
    private String appid;
    
    @Value("${wx.miniapp.secret}")
    private String secret;
    
    /**
     * 微信小程序登录接口
     */
    @ApiOperation(value = "微信小程序登录", notes = "通过临时code获取openid并完成登录或注册\n" +
            "请求参数格式: {\"code\": \"微信临时登录凭证\"}\n" +
            "返回结果格式：{\n" +
            "  \"code\": 200,\n" +
            "  \"msg\": \"登录成功\",\n" +
            "  \"data\": {\n" +
            "    \"token\": \"用户登录令牌\",\n" +
            "    \"userInfo\": {\n" +
            "      \"userId\": 用户ID,\n" +
            "      \"openid\": \"微信openid\",\n" +
            "      \"nickName\": \"用户昵称\",\n" +
            "      \"avatarUrl\": \"头像URL\",\n" +
            "      \"gender\": \"性别（0未知 1男 2女）\",\n" +
            "      \"status\": \"状态（0正常 1停用）\"\n" +
            "    }\n" +
            "  }\n" +
            "}")
    @PostMapping("/login")
    public AjaxResult wxLogin(@ApiParam(value = "登录请求参数", required = true) @RequestBody Map<String, Object> params) 
    {
        // 获取临时code
        String code = (String) params.get("code");
        String phone = (String) params.get("phone");
        if (code == null || code.isEmpty()) {
            return AjaxResult.error("临时code不能为空");
        }
        if (phone == null || phone.isEmpty()) {
            return AjaxResult.error("手机号不能为空");
        }
        
        try {
            // 构建请求参数
            String requestUrl = "https://api.weixin.qq.com/sns/jscode2session";
            String requestParams = "appid=" + appid + "&secret=" + secret + "&js_code=" + code + "&grant_type=authorization_code";

            // 发送请求到微信接口
            String result = HttpUtils.sendGet(requestUrl, requestParams);
            log.info("微信登录接口返回: {}", result);
            
            // 解析返回结果
            JSONObject jsonObject = JSON.parseObject(result);
            
            // 检查是否返回错误
            if (jsonObject.containsKey("errcode") && jsonObject.getIntValue("errcode") != 0) {
                return AjaxResult.error("微信登录失败: " + jsonObject.getString("errmsg"));
            }
            
            // 获取openid和session_key
            String openid = jsonObject.getString("openid");
            String sessionKey = jsonObject.getString("session_key");
            
            if (openid == null || openid.isEmpty()) {
                return AjaxResult.error("获取openid失败");
            }
            
            // 根据openid查询用户
            WxUser wxUser = wxUserService.selectWxUserByPhone(phone);
            
            // 用户不存在，自动注册
            if (wxUser == null) {
                wxUser = new WxUser();
                wxUser.setPhone(phone);
                wxUser.setOpenid(openid);
                wxUser.setSessionKey(sessionKey);
                wxUser.setNickName("微信用户_"+phone); // 设置默认昵称
                wxUser.setStatus("0");  // 正常状态
                wxUser.setDelFlag("0"); // 未删除
                wxUser.setLastLoginTime(new Date());
                
                // 注册新用户
                wxUserService.registerWxUser(wxUser);
                
                // 获取新注册的用户信息
                wxUser = wxUserService.selectWxUserByOpenid(openid);
            } else {
                if(wxUser.getStatus().equals("1")){
                    return AjaxResult.error("该手机号已停用");
                }
                // 更新用户登录信息
                WxUser updateUser = new WxUser();
                updateUser.setUserId(wxUser.getUserId());
                updateUser.setSessionKey(sessionKey);
                updateUser.setLastLoginTime(new Date());
                updateUser.setOpenid(openid);
                wxUserService.updateWxUserLoginInfo(updateUser);
                
                // 重新获取最新的用户信息
                wxUser = wxUserService.selectWxUserByOpenid(openid);
            }
            
            // 构建返回数据
            Map<String, Object> data = new HashMap<>();
            data.put("token", openid); // 使用openid作为token（简化实现）
            data.put("userInfo", wxUser);
            
            return AjaxResult.success("登录成功", data);
        } catch (Exception e) {
            log.error("微信登录异常: {}", e.getMessage(), e);
            return AjaxResult.error("登录失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新微信用户信息
     */
    @ApiOperation(value = "更新用户信息", notes = "更新微信用户的个人信息（昵称、头像等）\n" +
            "请求参数格式：{\n" +
            "  \"nickName\": \"用户昵称\",\n" +
            "  \"avatarUrl\": \"头像URL\",\n" +
            "  \"gender\": \"性别（0未知 1男 2女）\",\n" +
            "  \"country\": \"国家\",\n" +
            "  \"province\": \"省份\",\n" +
            "  \"city\": \"城市\"\n" +
            "}\n" +
            "返回结果格式：{\n" +
            "  \"code\": 200,\n" +
            "  \"msg\": \"操作成功\",\n" +
            "  \"data\": 用户ID\n" +
            "}")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "Authorization", value = "用户登录令牌", required = true, paramType = "header", dataType = "String")
    })
    @PostMapping("/updateInfo")
    public AjaxResult updateInfo(@ApiParam(value = "用户信息", required = true) @RequestBody WxUser wxUser,
                               javax.servlet.http.HttpServletRequest request) {
        // 从Token中获取用户的openid
        String token = request.getHeader("Authorization");
        if (token == null || token.isEmpty()) {
            return AjaxResult.error("未登录");
        }
        
        // 根据Token查询用户信息
        WxUser currentUser = wxUserService.getWxUserByToken(token);
        if (currentUser == null) {
            return AjaxResult.error("用户不存在或登录已过期");
        }
        
        // 设置用户ID，确保更新的是当前登录用户
        wxUser.setUserId(currentUser.getUserId());
        // 只允许更新个人信息，不允许更新关键字段
        wxUser.setOpenid(null);
        wxUser.setUnionid(null);
        wxUser.setSessionKey(null);
        wxUser.setStatus(null);
        wxUser.setDelFlag(null);
        
        // 更新用户信息
        int rows = wxUserService.updateWxUser(wxUser);
        if (rows > 0) {
            return AjaxResult.success(wxUser.getUserId());
        }
        return AjaxResult.error("更新用户信息失败");
    }
    
    /**
     * 获取用户信息
     */
    @ApiOperation(value = "获取用户信息", notes = "获取当前登录用户的详细信息\n" +
            "返回结果格式：{\n" +
            "  \"code\": 200,\n" +
            "  \"msg\": \"操作成功\",\n" +
            "  \"data\": {\n" +
            "    \"userId\": 用户ID,\n" +
            "    \"openid\": \"微信openid\",\n" +
            "    \"nickName\": \"用户昵称\",\n" +
            "    \"avatarUrl\": \"头像URL\",\n" +
            "    \"gender\": \"性别（0未知 1男 2女）\",\n" +
            "    \"country\": \"国家\",\n" +
            "    \"province\": \"省份\",\n" +
            "    \"city\": \"城市\",\n" +
            "    \"phone\": \"手机号码\",\n" +
            "    \"status\": \"状态（0正常 1停用）\"\n" +
            "  }\n" +
            "}")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "Authorization", value = "用户登录令牌", required = true, paramType = "header", dataType = "String")
    })
    @GetMapping("/info")
    public AjaxResult getUserInfo(javax.servlet.http.HttpServletRequest request) {
        // 从Token中获取用户的openid
        String token = request.getHeader("Authorization");
        if (token == null || token.isEmpty()) {
            return AjaxResult.error("未登录");
        }
        
        // 根据Token查询用户信息
        WxUser wxUser = wxUserService.getWxUserByToken(token);
        if (wxUser == null) {
            return AjaxResult.error("用户不存在或登录已过期");
        }
        
        return AjaxResult.success(wxUser);
    }

    @PostMapping("/getPhoneNumber")
    public AjaxResult getPhoneNumber(@RequestBody Map<String,Object> data)
    {
        //通过appid和secret来获取token
        //WXContent.APPID是自定义的全局变量
        String tokenUrl = String.format("https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s", appid, secret);
        JSONObject token = JSON.parseObject(HttpUtils.sendGet(tokenUrl));

        //通过token和code来获取用户手机号
        String url = "https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=" + token.getString("access_token");

        //封装请求体
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("code", data.get("code").toString());

        //封装请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<Map<String, String>> httpEntity = new HttpEntity<>(paramMap,headers);

        //通过RestTemplate发送请求，获取到用户手机号码
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<Object> response = restTemplate.postForEntity(url, httpEntity, Object.class);

        //返回到前端展示
        return AjaxResult.success(response.getBody());
    }



}


