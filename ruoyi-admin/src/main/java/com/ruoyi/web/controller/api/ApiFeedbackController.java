package com.ruoyi.web.controller.api;

import java.util.List;

import com.ruoyi.system.domain.WxUser;
import com.ruoyi.system.service.IWxUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.TokenCheck;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.system.domain.Feedback;
import com.ruoyi.system.service.IFeedbackService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

import javax.servlet.http.HttpServletRequest;

/**
 * 用户反馈 API接口
 * 
 * <AUTHOR>
 */
@Api(tags = "小程序用户反馈接口")
@RestController
@RequestMapping("/api/feedback")
public class ApiFeedbackController extends BaseController
{
    @Autowired
    private IFeedbackService feedbackService;

    @Autowired
    private IWxUserService wxUserService;



    /**
     * 获取用户反馈列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "获取用户反馈列表", notes = "获取当前登录用户的反馈记录")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "Authorization", value = "用户token", paramType = "header", required = true),
        @ApiImplicitParam(name = "pageNum", value = "当前页码", paramType = "query", dataType = "int", example = "1"),
        @ApiImplicitParam(name = "pageSize", value = "每页记录数", paramType = "query", dataType = "int", example = "10")
    })
    public AjaxResult list(HttpServletRequest request)
    {
        startPage();
        // 从Token中获取用户的openid
        String token = request.getHeader("Authorization");
        if (token == null || token.isEmpty()) {
            return AjaxResult.error("未登录");
        }

        // 根据Token查询用户信息
        WxUser wxUser = wxUserService.getWxUserByToken(token);
        List<Feedback> list = feedbackService.selectFeedbackListByUserId(wxUser.getUserId());
        return AjaxResult.success(list);
    }

    /**
     * 提交反馈
     */
    @PostMapping("/submit")
    @ApiOperation(value = "提交反馈", notes = "提交用户反馈信息")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "Authorization", value = "用户token", paramType = "header", required = true),
        @ApiImplicitParam(name = "feedback", value = "反馈信息", paramType = "body", dataTypeClass = Feedback.class, required = true)
    })
    public AjaxResult submit(@RequestBody Feedback feedback,HttpServletRequest request)
    {
        String token = request.getHeader("Authorization");
        if (token == null || token.isEmpty()) {
            return AjaxResult.error("未登录");
        }

        // 根据Token查询用户信息
        WxUser wxUser = wxUserService.getWxUserByToken(token);
        feedback.setUserId(wxUser.getUserId());
        return toAjax(feedbackService.insertFeedback(feedback));
    }
} 