package com.ruoyi.web.controller.system;

import java.io.BufferedReader;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.web.controller.tool.*;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.OrderInfo;
import com.ruoyi.system.service.IOrderInfoService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 微信支付Controller
 *
 * <AUTHOR>
 */
@Api(tags = "微信支付管理")
@RestController
@RequestMapping("/system/wxpay")
public class WxPayController extends BaseController
{
    private static final Logger log = LoggerFactory.getLogger(WxPayController.class);
    @Autowired
    private PaymentUtil paymentUtil;
    @Autowired
    private WechatUtil wechatUtil;
    @Autowired
    private WXJSAPISDKUtility wxJsApiSdkUtility;
    @Autowired
    private IOrderInfoService orderInfoService;

    /**
     * 创建微信支付订单
     */
    @ApiOperation(value = "创建微信支付订单", notes = "根据订单编号和用户OpenID创建微信支付订单，返回支付参数")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "orderNo", value = "订单编号", required = true, dataType = "String", dataTypeClass = String.class),
        @ApiImplicitParam(name = "openid", value = "用户OpenID", required = true, dataType = "String", dataTypeClass = String.class)
    })
    @Log(title = "创建微信支付订单", businessType = BusinessType.OTHER)
    @PostMapping("/create")
    public AjaxResult createOrder(@RequestBody Map<String, String> params)
    {
        String orderNo = params.get("orderNo");
        String openid = params.get("openid");

        if (orderNo == null || orderNo.isEmpty())
        {
            return error("订单编号不能为空");
        }

        if (openid == null || openid.isEmpty())
        {
            return error("用户openid不能为空");
        }

        try
        {
            // 调用订单服务创建微信支付订单
            Map<String, String> payParams = orderInfoService.createWxPayOrder(orderNo, openid);
            return success(payParams);
        }
        catch (Exception e)
        {
            log.error("创建微信支付订单失败：{}", e.getMessage(), e);
            return error(e.getMessage());
        }
    }

    /**
     * 查询支付状态
     */
    @ApiOperation(value = "查询微信支付状态", notes = "根据订单编号查询微信支付订单状态")
    @ApiImplicitParam(name = "orderNo", value = "订单编号", required = true, dataType = "String", paramType = "path", dataTypeClass = String.class)
    @GetMapping("/query/{orderNo}")
    public AjaxResult queryOrderStatus(@PathVariable("orderNo") String orderNo)
    {
        if (orderNo == null || orderNo.isEmpty())
        {
            return error("订单编号不能为空");
        }

        try
        {
            // 查询微信支付订单状态
            String status = orderInfoService.queryWxPayOrderStatus(orderNo);
            return success(status);
        }
        catch (Exception e)
        {
            log.error("查询微信支付订单状态失败：{}", e.getMessage(), e);
            return error(e.getMessage());
        }
    }

    /**
     * 微信支付结果通知
     * 注意：此接口不返回AjaxResult，而是直接返回XML字符串
     */
    @ApiOperation(value = "微信支付结果通知", notes = "接收微信支付结果通知，并返回处理结果。此接口直接返回XML格式数据，不使用AjaxResult")
    @PostMapping("/notify")
    public String payNotify(HttpServletRequest request, HttpServletResponse response)
    {
        log.info("接收到微信支付结果通知");

        // 读取请求体
        StringBuilder sb = new StringBuilder();
        try (BufferedReader reader = request.getReader())
        {
            String line;
            while ((line = reader.readLine()) != null)
            {
                sb.append(line);
            }
        }
        catch (IOException e)
        {
            log.error("读取微信支付通知请求体失败：{}", e.getMessage(), e);
            return "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[读取请求失败]]></return_msg></xml>";
        }

        String xmlData = sb.toString();
        log.debug("微信支付通知内容：{}", xmlData);

        // 处理支付结果通知
        try
        {
            return orderInfoService.handleWxPayNotify(xmlData);
        }
        catch (Exception e)
        {
            log.error("处理微信支付通知失败：{}", e.getMessage(), e);
            return "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[处理通知失败]]></return_msg></xml>";
        }
    }

    /**
     * 微信支付结果通知
     * 注意：此接口不返回AjaxResult，而是直接返回XML字符串
     */
    @ApiOperation(value = "微信支付结果通知", notes = "接收微信支付结果通知，并返回处理结果。此接口直接返回XML格式数据，不使用AjaxResult")
    @PostMapping("/refundOrder")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderNo", value = "订单编号", required = true, dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "openId", value = "用户OpenID", required = true, dataType = "String", dataTypeClass = String.class)
    })
    public AjaxResult refundOrder(@RequestBody Map<String, String> params,HttpServletRequest request)
    {
        String orderNo = params.get("orderNo");
        String openId = params.get("openId");
        String remoteAddr = request.getRemoteAddr();
        SignatureDTO signatureDTO = wechatRefund(orderNo, openId,remoteAddr);
        return AjaxResult.success(signatureDTO);
    }


    public SignatureDTO wechatRefund(String orderNo,String openId,String remoteAddr)  {
        try {
            //获取详情
            OrderInfo orderInfo = orderInfoService.selectOrderInfoByOrderNo(orderNo);
            //userChargeDTO = fundService.getUserChargeByChargeNo(chargeNo);
            //初始化微信统一下单接口
            WechatPayRequest payRequest = new WechatPayRequest();
            payRequest.setAppid(paymentUtil.getWechatAppIdMiniProgram());
            payRequest.setMch_id(paymentUtil.getWechatMerchantId());
            payRequest.setNonce_str(RandomStringUtils.randomAlphanumeric(32));
            payRequest.setOut_trade_no(orderNo);
            payRequest.setBody("套餐购买");
            payRequest.setTotal_fee(orderInfo.getAmount().multiply(new BigDecimal(100))
                    .intValue());
            payRequest.setSpbill_create_ip(remoteAddr);
            payRequest.setNotify_url(paymentUtil.getWechatNotifyUrl());
            payRequest.setTrade_type("JSAPI");
            payRequest.setAttach("会员充值");
            payRequest.setProduct_id("HYCZ");
            payRequest.setOpenid(openId);
            payRequest.setSign(wechatUtil.getSign(payRequest));
            String requestXML = wechatUtil.toXML(payRequest);
            requestXML = new String(requestXML.getBytes("utf-8"),"iso-8859-1");
            logger.info("Wechat unified order request: "+requestXML);
            //下单
            String wechatResponseStr = wechatUtil.postWechatUnifiedOrder(requestXML);
            logger.info("Wechat unified order Response: "+wechatResponseStr);
            WechatPayResponse payResponse = (WechatPayResponse)wechatUtil.fromXML2WechatResponse(WechatPayResponse.class,wechatResponseStr);
            if("SUCCESS".equals(payResponse.getReturn_code()) && "SUCCESS".equals(payResponse.getResult_code())){
                SignatureDTO signatureDTO = wxJsApiSdkUtility.getWXPaySignature(payResponse.getPrepay_id());
                signatureDTO.setOut_trade_no(payRequest.getOut_trade_no());
                return signatureDTO;
            }
            return new SignatureDTO();
        } catch (Exception e) {
            return new SignatureDTO();
        }
    }
}