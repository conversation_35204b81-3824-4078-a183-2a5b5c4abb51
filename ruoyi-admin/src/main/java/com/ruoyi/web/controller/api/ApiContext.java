package com.ruoyi.web.controller.api;

import com.ruoyi.common.core.domain.model.WxLoginUser;

/**
 * API上下文，用于存储当前线程的用户信息
 * 
 * <AUTHOR>
 */
public class ApiContext {
    
    private static final ThreadLocal<WxLoginUser> loginUserThreadLocal = new ThreadLocal<>();
    
    /**
     * 设置当前登录用户
     * 
     * @param loginUser 登录用户信息
     */
    public static void setLoginUser(WxLoginUser loginUser) {
        loginUserThreadLocal.set(loginUser);
    }
    
    /**
     * 获取当前登录用户
     * 
     * @return 登录用户信息
     */
    public static WxLoginUser getLoginUser() {
        return loginUserThreadLocal.get();
    }
    
    /**
     * 清除当前线程的上下文
     */
    public static void clear() {
        loginUserThreadLocal.remove();
    }
} 