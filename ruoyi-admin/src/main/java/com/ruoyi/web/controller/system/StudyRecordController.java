package com.ruoyi.web.controller.system;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.StudyRecord;
import com.ruoyi.system.service.IStudyRecordService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

/**
 * 学习记录Controller
 * 
 * <AUTHOR>
 */
@Api(tags = "学习记录管理")
@RestController
@RequestMapping("/system/record")
public class StudyRecordController extends BaseController
{
    @Autowired
    private IStudyRecordService studyRecordService;

    /**
     * 查询学习记录列表
     */
    @ApiOperation(value = "获取学习记录列表", notes = "分页查询学习记录列表数据")
    @PreAuthorize("@ss.hasPermi('system:record:list')")
    @GetMapping("/list")
    public TableDataInfo list(StudyRecord studyRecord)
    {
        startPage();
        List<StudyRecord> list = studyRecordService.selectStudyRecordList(studyRecord);
        return getDataTable(list);
    }

    /**
     * 导出学习记录列表
     */
    @ApiOperation(value = "导出学习记录", notes = "导出所有符合条件的学习记录数据")
    @PreAuthorize("@ss.hasPermi('system:record:export')")
    @Log(title = "学习记录", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(StudyRecord studyRecord)
    {
        List<StudyRecord> list = studyRecordService.selectStudyRecordList(studyRecord);
        ExcelUtil<StudyRecord> util = new ExcelUtil<StudyRecord>(StudyRecord.class);
        return util.exportExcel(list, "学习记录数据");
    }

    /**
     * 获取学习记录详细信息
     */
    @ApiOperation(value = "获取学习记录详细", notes = "根据记录ID获取学习记录详细信息")
    @ApiImplicitParam(name = "recordId", value = "记录ID", required = true, dataType = "Long", paramType = "path", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermi('system:record:query')")
    @GetMapping(value = "/{recordId}")
    public AjaxResult getInfo(@PathVariable("recordId") Long recordId)
    {
        return success(studyRecordService.selectStudyRecordByRecordId(recordId));
    }

    /**
     * 新增学习记录
     */
    @ApiOperation(value = "新增学习记录", notes = "新增学习记录信息")
    @PreAuthorize("@ss.hasPermi('system:record:add')")
    @Log(title = "学习记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody StudyRecord studyRecord)
    {
        return toAjax(studyRecordService.insertStudyRecord(studyRecord));
    }

    /**
     * 修改学习记录
     */
    @ApiOperation(value = "修改学习记录", notes = "修改学习记录信息")
    @PreAuthorize("@ss.hasPermi('system:record:edit')")
    @Log(title = "学习记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody StudyRecord studyRecord)
    {
        return toAjax(studyRecordService.updateStudyRecord(studyRecord));
    }

    /**
     * 删除学习记录
     */
    @ApiOperation(value = "删除学习记录", notes = "根据记录ID删除学习记录信息")
    @ApiImplicitParam(name = "recordIds", value = "记录ID数组", required = true, dataType = "Long[]", paramType = "path", dataTypeClass = Long[].class)
    @PreAuthorize("@ss.hasPermi('system:record:remove')")
    @Log(title = "学习记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{recordIds}")
    public AjaxResult remove(@PathVariable Long[] recordIds)
    {
        return toAjax(studyRecordService.deleteStudyRecordByRecordIds(recordIds));
    }
    
    /**
     * 小程序查询用户学习记录列表
     */
    @ApiOperation(value = "获取用户学习记录列表", notes = "根据用户ID获取该用户的所有学习记录")
    @ApiImplicitParam(name = "userId", value = "用户ID", required = true, dataType = "Long", paramType = "path", dataTypeClass = Long.class)
    @GetMapping("/listByUser/{userId}")
    public AjaxResult listByUser(@PathVariable("userId") Long userId)
    {
        List<StudyRecord> list = studyRecordService.selectStudyRecordListByUserId(userId);
        return success(list);
    }
    
    /**
     * 小程序查询用户在套餐下的学习记录列表
     */
    @ApiOperation(value = "获取用户套餐学习记录", notes = "根据用户ID和套餐ID获取该用户在特定套餐下的学习记录")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "userId", value = "用户ID", required = true, dataType = "Long", paramType = "path", dataTypeClass = Long.class),
        @ApiImplicitParam(name = "packageId", value = "套餐ID", required = true, dataType = "Long", paramType = "path", dataTypeClass = Long.class)
    })
    @GetMapping("/listByPackage/{userId}/{packageId}")
    public AjaxResult listByPackage(@PathVariable("userId") Long userId, @PathVariable("packageId") Long packageId)
    {
        List<StudyRecord> list = studyRecordService.selectStudyRecordListByPackageId(userId, packageId);
        return success(list);
    }
    
    /**
     * 小程序查询用户的特定资料学习记录
     */
    @ApiOperation(value = "获取用户资料学习记录", notes = "根据用户ID和资料ID获取该用户特定资料的学习记录")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "userId", value = "用户ID", required = true, dataType = "Long", paramType = "path", dataTypeClass = Long.class),
        @ApiImplicitParam(name = "materialId", value = "资料ID", required = true, dataType = "Long", paramType = "path", dataTypeClass = Long.class)
    })
    @GetMapping("/getRecord/{userId}/{materialId}")
    public AjaxResult getRecord(@PathVariable("userId") Long userId, @PathVariable("materialId") Long materialId)
    {
        StudyRecord record = studyRecordService.selectStudyRecordByUserIdAndMaterialId(userId, materialId);
        return success(record);
    }
    
    /**
     * 小程序更新学习进度
     */
    @ApiOperation(value = "更新学习进度", notes = "更新用户特定资料的学习进度信息")
    @PostMapping("/updateProgress")
    public AjaxResult updateProgress(@RequestBody StudyRecord studyRecord)
    {
        if (studyRecord.getUserId() == null || studyRecord.getMaterialId() == null || 
            studyRecord.getPackageId() == null || studyRecord.getMaterialType() == null)
        {
            return error("缺少必要参数");
        }
        
        return toAjax(studyRecordService.updateProgress(
            studyRecord.getUserId(), 
            studyRecord.getMaterialId(),
            studyRecord.getPackageId(),
            studyRecord.getMaterialType(),
            studyRecord.getProgress(),
            studyRecord.getLastPosition(),
            studyRecord.getStudyTime()
        ));
    }
} 