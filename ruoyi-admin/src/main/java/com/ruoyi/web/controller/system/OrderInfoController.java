package com.ruoyi.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.OrderInfo;
import com.ruoyi.system.service.IOrderInfoService;
import com.ruoyi.system.service.IWxUserService;
import com.ruoyi.system.service.IExamPackageService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 订单管理Controller
 * 
 * <AUTHOR>
 */
@Api(tags = "订单管理")
@RestController
@RequestMapping("/system/order")
public class OrderInfoController extends BaseController
{
    @Autowired
    private IOrderInfoService orderInfoService;
    
    @Autowired
    private IWxUserService wxUserService;
    
    @Autowired
    private IExamPackageService examPackageService;

    /**
     * 查询订单列表
     */
    @ApiOperation(value = "获取订单列表", notes = "根据条件分页查询订单列表数据")
    @PreAuthorize("@ss.hasPermi('system:order:list')")
    @GetMapping("/list")
    public TableDataInfo list(OrderInfo orderInfo)
    {
        startPage();
        List<OrderInfo> list = orderInfoService.selectOrderInfoList(orderInfo);
        return getDataTable(list);
    }

    /**
     * 导出订单列表
     */
    @ApiOperation(value = "导出订单", notes = "导出所有符合条件的订单数据")
    @PreAuthorize("@ss.hasPermi('system:order:export')")
    @Log(title = "订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OrderInfo orderInfo)
    {
        List<OrderInfo> list = orderInfoService.selectOrderInfoList(orderInfo);
        ExcelUtil<OrderInfo> util = new ExcelUtil<OrderInfo>(OrderInfo.class);
        util.exportExcel(response, list, "订单数据");
    }

    /**
     * 获取订单详细信息
     */
    @ApiOperation(value = "获取订单详细", notes = "根据订单ID获取订单详细信息")
    @ApiImplicitParam(name = "orderId", value = "订单ID", required = true, dataType = "Long", paramType = "path", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermi('system:order:query')")
    @GetMapping(value = "/{orderId}")
    public AjaxResult getInfo(@PathVariable("orderId") Long orderId)
    {
        return success(orderInfoService.selectOrderInfoByOrderId(orderId));
    }

    /**
     * 新增订单
     */
    @ApiOperation(value = "新增订单", notes = "新增订单信息")
    @PreAuthorize("@ss.hasPermi('system:order:add')")
    @Log(title = "订单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OrderInfo orderInfo)
    {
        return toAjax(orderInfoService.insertOrderInfo(orderInfo));
    }

    /**
     * 修改订单
     */
    @ApiOperation(value = "修改订单", notes = "修改订单信息")
    @PreAuthorize("@ss.hasPermi('system:order:edit')")
    @Log(title = "订单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OrderInfo orderInfo)
    {
        return toAjax(orderInfoService.updateOrderInfo(orderInfo));
    }

    /**
     * 删除订单
     */
    @ApiOperation(value = "删除订单", notes = "根据订单ID删除订单信息")
    @ApiImplicitParam(name = "orderIds", value = "订单ID数组", required = true, dataType = "Long[]", paramType = "path", dataTypeClass = Long[].class)
    @PreAuthorize("@ss.hasPermi('system:order:remove')")
    @Log(title = "订单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{orderIds}")
    public AjaxResult remove(@PathVariable Long[] orderIds)
    {
        return toAjax(orderInfoService.deleteOrderInfoByOrderIds(orderIds));
    }
    
    /**
     * 支付订单
     */
    @ApiOperation(value = "支付订单", notes = "手动支付订单，需要提供订单编号、支付渠道和交易流水号")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "orderNo", value = "订单编号", required = true, dataType = "String", dataTypeClass = String.class),
        @ApiImplicitParam(name = "payChannel", value = "支付渠道", required = true, dataType = "String", dataTypeClass = String.class),
        @ApiImplicitParam(name = "transactionId", value = "交易流水号", required = true, dataType = "String", dataTypeClass = String.class)
    })
    @PreAuthorize("@ss.hasPermi('system:order:edit')")
    @Log(title = "支付订单", businessType = BusinessType.UPDATE)
    @PostMapping("/pay")
    public AjaxResult payOrder(@RequestBody OrderInfo orderInfo)
    {
        if (orderInfo.getOrderNo() == null || orderInfo.getOrderNo().isEmpty())
        {
            return error("订单编号不能为空");
        }
        if (orderInfo.getPayChannel() == null || orderInfo.getPayChannel().isEmpty())
        {
            return error("支付渠道不能为空");
        }
        if (orderInfo.getTransactionId() == null || orderInfo.getTransactionId().isEmpty())
        {
            return error("交易流水号不能为空");
        }
        
        return toAjax(orderInfoService.payOrder(orderInfo.getOrderNo(), orderInfo.getPayChannel(), orderInfo.getTransactionId()));
    }
    
    /**
     * 取消订单
     */
    @ApiOperation(value = "取消订单", notes = "取消待支付状态的订单")
    @ApiImplicitParam(name = "orderNo", value = "订单编号", required = true, dataType = "String", dataTypeClass = String.class)
    @PreAuthorize("@ss.hasPermi('system:order:edit')")
    @Log(title = "取消订单", businessType = BusinessType.UPDATE)
    @PostMapping("/cancel")
    public AjaxResult cancelOrder(@RequestBody OrderInfo orderInfo)
    {
        if (orderInfo.getOrderNo() == null || orderInfo.getOrderNo().isEmpty())
        {
            return error("订单编号不能为空");
        }
        
        return toAjax(orderInfoService.cancelOrder(orderInfo.getOrderNo()));
    }
} 