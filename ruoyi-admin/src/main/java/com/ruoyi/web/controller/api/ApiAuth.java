package com.ruoyi.web.controller.api;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.ruoyi.common.annotation.TokenCheck;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.WxLoginUser;
import com.ruoyi.system.domain.WxUser;
import com.ruoyi.system.service.IWxUserService;

import javax.servlet.http.HttpServletRequest;

/**
 * API鉴权处理
 * 
 * <AUTHOR>
 */
@Aspect
@Component
public class ApiAuth
{
    @Autowired
    private IWxUserService wxUserService;

    /**
     * 配置切入点
     */
    @Pointcut("@annotation(com.ruoyi.common.annotation.TokenCheck)")
    public void tokenCheckPointcut()
    {
    }

    /**
     * 环绕通知，验证token
     */
    @Around("tokenCheckPointcut() && @annotation(tokenCheck)")
    public Object doAround(ProceedingJoinPoint point, TokenCheck tokenCheck) throws Throwable
    {
        // 是否验证token
        if (tokenCheck.required())
        {
            // 获取请求对象
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = attributes.getRequest();

            // 从请求头获取token
            String token = request.getHeader("Authorization");
            if (!StringUtils.hasText(token))
            {
                return AjaxResult.error(401, "请先登录");
            }

            // 根据token获取微信用户信息
            WxUser wxUser = wxUserService.getWxUserByToken(token);
            if (wxUser == null)
            {
                return AjaxResult.error(401, "登录已过期，请重新登录");
            }

            // 创建登录用户对象并设置到当前线程上下文
            WxLoginUser loginUser = new WxLoginUser(wxUser.getUserId(), wxUser.getNickName(), wxUser.getOpenid());
            loginUser.setAvatarUrl(wxUser.getAvatarUrl());
            loginUser.setPhone(wxUser.getPhone());
            ApiContext.setLoginUser(loginUser);
        }

        // 执行目标方法
        return point.proceed();
    }
} 