package com.ruoyi.web.controller.system;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.UserPackage;
import com.ruoyi.system.domain.UserPackageImport;
import com.ruoyi.system.service.IUserPackageService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 用户套餐管理Controller
 * 
 * <AUTHOR>
 */
@Api(tags = "用户套餐管理")
@RestController
@RequestMapping("/system/userPackage")
public class UserPackageController extends BaseController
{
    @Autowired
    private IUserPackageService userPackageService;

    /**
     * 查询用户套餐列表
     */
    @PreAuthorize("@ss.hasPermi('system:userPackage:list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询用户套餐列表", notes = "查询用户套餐列表")
    public TableDataInfo list(UserPackage userPackage)
    {
        startPage();
        List<UserPackage> list = userPackageService.selectUserPackageList(userPackage);
        return getDataTable(list);
    }

    /**
     * 导出用户套餐列表
     */
    @PreAuthorize("@ss.hasPermi('system:userPackage:export')")
    @Log(title = "用户套餐", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出用户套餐列表", notes = "导出用户套餐列表")
    public void export(HttpServletResponse response, UserPackage userPackage)
    {
        List<UserPackage> list = userPackageService.selectUserPackageList(userPackage);
        ExcelUtil<UserPackage> util = new ExcelUtil<UserPackage>(UserPackage.class);
        util.exportExcel(response, list, "用户套餐数据");
    }

    /**
     * 获取用户套餐详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:userPackage:query')")
    @GetMapping(value = "/{userPackageId}")
    @ApiOperation(value = "获取用户套餐详情", notes = "根据ID获取用户套餐详情")
    public AjaxResult getInfo(@PathVariable("userPackageId") Long userPackageId)
    {
        return success(userPackageService.selectUserPackageByUserPackageId(userPackageId));
    }

    /**
     * 新增用户套餐
     */
    @PreAuthorize("@ss.hasPermi('system:userPackage:add')")
    @Log(title = "用户套餐", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增用户套餐", notes = "新增用户套餐")
    public AjaxResult add(@RequestBody UserPackage userPackage)
    {
        return toAjax(userPackageService.insertUserPackage(userPackage));
    }

    /**
     * 修改用户套餐
     */
    @PreAuthorize("@ss.hasPermi('system:userPackage:edit')")
    @Log(title = "用户套餐", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改用户套餐", notes = "修改用户套餐")
    public AjaxResult edit(@RequestBody UserPackage userPackage)
    {
        return toAjax(userPackageService.updateUserPackage(userPackage));
    }

    /**
     * 删除用户套餐
     */
    @PreAuthorize("@ss.hasPermi('system:userPackage:remove')")
    @Log(title = "用户套餐", businessType = BusinessType.DELETE)
    @DeleteMapping("/{userPackageIds}")
    @ApiOperation(value = "删除用户套餐", notes = "根据ID删除用户套餐")
    public AjaxResult remove(@PathVariable Long[] userPackageIds)
    {
        return toAjax(userPackageService.deleteUserPackageByUserPackageIds(userPackageIds));
    }
    
    /**
     * 更新过期套餐状态
     */
    @PreAuthorize("@ss.hasPermi('system:userPackage:edit')")
    @Log(title = "更新过期套餐", businessType = BusinessType.UPDATE)
    @PostMapping("/updateExpiredStatus")
    @ApiOperation(value = "更新过期套餐状态", notes = "批量更新所有已过期但状态未更新的套餐")
    public AjaxResult updateExpiredStatus()
    {
        return toAjax(userPackageService.updateExpiredStatus());
    }
    
    /**
     * 导入用户套餐数据
     */
    @PreAuthorize("@ss.hasPermi('system:userPackage:import')")
    @Log(title = "导入用户套餐", businessType = BusinessType.IMPORT)
    @PostMapping("/importData/{packageId}")
    @ApiOperation(value = "导入用户套餐数据", notes = "导入用户套餐数据")
    public AjaxResult importData(MultipartFile file, @PathVariable("packageId")Long packageId) throws Exception
    {
        Map<String, Object> result = userPackageService.importUserPackage(file, packageId);
        return AjaxResult.success(result);
    }

    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('system:userPackage:import')")
    @PostMapping("/importTemplate")
    @ApiOperation(value = "下载导入模板", notes = "下载导入模板")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<UserPackageImport> util = new ExcelUtil<UserPackageImport>(UserPackageImport.class);
        util.importTemplateExcel(response, "用户套餐导入模板");
    }
} 