package com.ruoyi.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.ExamPackage;
import com.ruoyi.system.service.IExamPackageService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

/**
 * 题库套餐Controller
 * 
 * <AUTHOR>
 */
@Api(tags = "题库套餐管理")
@RestController
@RequestMapping("/system/package")
public class ExamPackageController extends BaseController
{
    @Autowired
    private IExamPackageService examPackageService;

    /**
     * 查询题库套餐列表
     */
    @ApiOperation(value = "获取套餐列表", notes = "分页查询题库套餐列表数据")
    @PreAuthorize("@ss.hasPermi('system:package:list')")
    @GetMapping("/list")
    public TableDataInfo list(ExamPackage examPackage)
    {
        startPage();
        List<ExamPackage> list = examPackageService.selectExamPackageList(examPackage);
        return getDataTable(list);
    }

    /**
     * 查询题库套餐列表
     */
    @ApiOperation(value = "小程序获取套餐列表", notes = "小程序分页查询题库套餐列表数据")
    @GetMapping("/wx/list")
    public TableDataInfo wxGetList(ExamPackage examPackage)
    {
        startPage();
        List<ExamPackage> list = examPackageService.selectExamPackageList(examPackage);
        return getDataTable(list);
    }

    /**
     * 导出题库套餐列表
     */
    @ApiOperation(value = "导出套餐数据", notes = "导出所有符合条件的题库套餐数据")
    @PreAuthorize("@ss.hasPermi('system:package:export')")
    @Log(title = "题库套餐", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ExamPackage examPackage)
    {
        List<ExamPackage> list = examPackageService.selectExamPackageList(examPackage);
        ExcelUtil<ExamPackage> util = new ExcelUtil<ExamPackage>(ExamPackage.class);
        util.exportExcel(response, list, "题库套餐数据");
    }

    /**
     * 获取题库套餐详细信息
     */
    @ApiOperation(value = "获取套餐详细", notes = "根据套餐ID获取题库套餐详细信息")
    @ApiImplicitParam(name = "packageId", value = "套餐ID", required = true, dataType = "Long", paramType = "path", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermi('system:package:query')")
    @GetMapping(value = "/{packageId}")
    public AjaxResult getInfo(@PathVariable("packageId") Long packageId)
    {
        return success(examPackageService.selectExamPackageByPackageId(packageId));
    }

    /**
     * 获取题库套餐详细信息
     */
    @ApiOperation(value = "小程序获取套餐详细", notes = "小程序根据套餐ID获取题库套餐详细信息")
    @ApiImplicitParam(name = "packageId", value = "套餐ID", required = true, dataType = "Long", paramType = "path", dataTypeClass = Long.class)
    @GetMapping(value = "/wx/{packageId}")
    public AjaxResult wxGetInfo(@PathVariable("packageId") Long packageId)
    {
        return success(examPackageService.selectExamPackageByPackageId(packageId));
    }

    /**
     * 新增题库套餐
     */
    @ApiOperation(value = "新增套餐", notes = "新增题库套餐信息")
    @PreAuthorize("@ss.hasPermi('system:package:add')")
    @Log(title = "题库套餐", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody ExamPackage examPackage)
    {
        examPackage.setCreateBy(SecurityUtils.getUsername());
        return toAjax(examPackageService.insertExamPackage(examPackage));
    }

    /**
     * 修改题库套餐
     */
    @ApiOperation(value = "修改套餐", notes = "修改题库套餐信息")
    @PreAuthorize("@ss.hasPermi('system:package:edit')")
    @Log(title = "题库套餐", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody ExamPackage examPackage)
    {
        examPackage.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(examPackageService.updateExamPackage(examPackage));
    }

    /**
     * 删除题库套餐
     */
    @ApiOperation(value = "删除套餐", notes = "根据套餐ID删除题库套餐信息")
    @ApiImplicitParam(name = "packageIds", value = "套餐ID数组", required = true, dataType = "Long[]", paramType = "path", dataTypeClass = Long[].class)
    @PreAuthorize("@ss.hasPermi('system:package:remove')")
    @Log(title = "题库套餐", businessType = BusinessType.DELETE)
    @DeleteMapping("/{packageIds}")
    public AjaxResult remove(@PathVariable Long[] packageIds)
    {
        return toAjax(examPackageService.deleteExamPackageByPackageIds(packageIds));
    }
    
    /**
     * 获取题库套餐选择框列表
     */
    @ApiOperation(value = "获取套餐选择框列表", notes = "获取所有可用的题库套餐列表用于下拉选择")
    @GetMapping("/optionselect")
    public AjaxResult optionselect()
    {
        List<ExamPackage> packages = examPackageService.selectExamPackageAll();
        return success(packages);
    }
} 