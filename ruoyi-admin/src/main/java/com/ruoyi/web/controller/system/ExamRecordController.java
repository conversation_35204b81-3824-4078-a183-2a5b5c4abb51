package com.ruoyi.web.controller.system;

import java.util.List;
import java.math.BigDecimal;
import java.math.RoundingMode;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.ExamRecord;
import com.ruoyi.system.service.IExamRecordService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.utils.CalculateUtils;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 考试记录Controller
 * 
 * <AUTHOR>
 */
@Api(tags = "考试记录管理")
@RestController
@RequestMapping("/system/examRecord")
public class ExamRecordController extends BaseController
{
    @Autowired
    private IExamRecordService examRecordService;

    /**
     * 查询考试记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:examRecord:list')")
    @GetMapping("/list")
    @ApiOperation(value = "查询考试记录列表", notes = "查询考试记录列表")
    public TableDataInfo list(ExamRecord examRecord)
    {
        startPage();
        List<ExamRecord> list = examRecordService.selectExamRecordList(examRecord);
        list.forEach(x->{
            // 计算完成率（正确率），保留两位小数
            x.setFinishRate(CalculateUtils.calculateFinishRate(
                x.getCorrectCount(), x.getWrongCount(), x.getEmptyCount()));

            // 判断是否合格
            Double score = x.getScore();
            if (score != null) {
                x.setIsPass(score > 60 ? "合格" : "不合格");
            } else {
                x.setIsPass("未评分");
            }
        });
        return getDataTable(list);
    }

    /**
     * 导出考试记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:examRecord:export')")
    @Log(title = "考试记录", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    @ApiOperation(value = "导出考试记录列表", notes = "导出考试记录列表")
    public void export(HttpServletResponse response, ExamRecord examRecord)
    {
        List<ExamRecord> list = examRecordService.selectExamRecordList(examRecord);
        ExcelUtil<ExamRecord> util = new ExcelUtil<ExamRecord>(ExamRecord.class);
        util.exportExcel(response, list, "考试记录数据");
    }

    /**
     * 获取考试记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:examRecord:query')")
    @GetMapping(value = "/{recordId}")
    @ApiOperation(value = "获取考试记录详情", notes = "根据ID获取考试记录详情")
    public AjaxResult getInfo(@PathVariable("recordId") Long recordId)
    {
        return success(examRecordService.selectExamRecordByRecordId(recordId));
    }

    /**
     * 新增考试记录
     */
    @PreAuthorize("@ss.hasPermi('system:examRecord:add')")
    @Log(title = "考试记录", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增考试记录", notes = "新增考试记录")
    public AjaxResult add(@RequestBody ExamRecord examRecord)
    {
        return toAjax(examRecordService.insertExamRecord(examRecord));
    }

    /**
     * 修改考试记录
     */
    @PreAuthorize("@ss.hasPermi('system:examRecord:edit')")
    @Log(title = "考试记录", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改考试记录", notes = "修改考试记录")
    public AjaxResult edit(@RequestBody ExamRecord examRecord)
    {
        return toAjax(examRecordService.updateExamRecord(examRecord));
    }

    /**
     * 删除考试记录
     */
    @PreAuthorize("@ss.hasPermi('system:examRecord:remove')")
    @Log(title = "考试记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{recordIds}")
    @ApiOperation(value = "删除考试记录", notes = "根据ID删除考试记录")
    public AjaxResult remove(@PathVariable Long[] recordIds)
    {
        return toAjax(examRecordService.deleteExamRecordByRecordIds(recordIds));
    }
} 