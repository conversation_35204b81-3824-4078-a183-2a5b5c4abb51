-- ----------------------------
-- 用户套餐关系表
-- ----------------------------
drop table if exists user_package;
create table user_package (
  user_package_id    bigint(20)      not null auto_increment    comment '用户套餐ID',
  user_id            bigint(20)      not null                   comment '用户ID',
  package_id         bigint(20)      not null                   comment '套餐ID',
  order_id           bigint(20)      not null                   comment '订单ID',
  start_time         datetime        not null                   comment '开始时间',
  end_time           datetime        not null                   comment '结束时间',
  status             char(1)         default '0'                comment '状态（0有效 1已过期）',
  create_time        datetime        default null               comment '创建时间',
  update_time        datetime        default null               comment '更新时间',
  remark             varchar(500)    default null               comment '备注',
  primary key (user_package_id),
  key idx_user_id (user_id),
  key idx_package_id (package_id),
  key idx_order_id (order_id)
) engine=innodb auto_increment=100 default charset=utf8mb4 comment = '用户套餐关系表';

-- ----------------------------
-- 用户套餐状态字典
-- ----------------------------
-- 用户套餐状态字典类型
insert into sys_dict_type values(100, '用户套餐状态', 'user_package_status', '0', 'admin', sysdate(), '', null, '用户套餐状态列表');

-- 用户套餐状态字典数据
insert into sys_dict_data values(1000, 1, '有效', '0', 'user_package_status', '', 'success', 'Y', '0', 'admin', sysdate(), '', null, '有效状态');
insert into sys_dict_data values(1001, 2, '已过期', '1', 'user_package_status', '', 'danger', 'N', '0', 'admin', sysdate(), '', null, '已过期状态');

-- ----------------------------
-- 用户套餐管理菜单
-- ----------------------------
-- 用户套餐管理菜单
insert into sys_menu values('2500', '用户套餐管理', '2300', '2', 'userPackage', 'system/userPackage/index', '', '', 1, 0, 'C', '0', '0', 'system:userPackage:list', 'list', 'admin', sysdate(), '', null, '用户套餐管理菜单');

-- 用户套餐管理按钮
insert into sys_menu values('2501', '用户套餐查询', '2500', '1', '', '', '', '', 1, 0, 'F', '0', '0', 'system:userPackage:query', '#', 'admin', sysdate(), '', null, '');
insert into sys_menu values('2502', '用户套餐新增', '2500', '2', '', '', '', '', 1, 0, 'F', '0', '0', 'system:userPackage:add', '#', 'admin', sysdate(), '', null, '');
insert into sys_menu values('2503', '用户套餐修改', '2500', '3', '', '', '', '', 1, 0, 'F', '0', '0', 'system:userPackage:edit', '#', 'admin', sysdate(), '', null, '');
insert into sys_menu values('2504', '用户套餐删除', '2500', '4', '', '', '', '', 1, 0, 'F', '0', '0', 'system:userPackage:remove', '#', 'admin', sysdate(), '', null, '');
insert into sys_menu values('2505', '用户套餐导出', '2500', '5', '', '', '', '', 1, 0, 'F', '0', '0', 'system:userPackage:export', '#', 'admin', sysdate(), '', null, '');