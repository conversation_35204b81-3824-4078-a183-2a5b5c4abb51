-- ----------------------------
-- 微信用户表
-- ----------------------------
drop table if exists wx_user;
create table wx_user (
  user_id           bigint(20)      not null auto_increment    comment '用户ID',
  openid            varchar(50)     not null                   comment '微信openid',
  unionid           varchar(50)     default null               comment '微信unionid',
  nick_name         varchar(50)     default null               comment '微信昵称',
  avatar_url        varchar(255)    default null               comment '头像URL',
  gender            char(1)         default '0'                comment '性别（0未知 1男 2女）',
  country           varchar(50)     default null               comment '国家',
  province          varchar(50)     default null               comment '省份',
  city              varchar(50)     default null               comment '城市',
  phone             varchar(11)     default null               comment '手机号码',
  session_key       varchar(100)    default null               comment '会话密钥',
  last_login_time   datetime        default null               comment '最后登录时间',
  status            char(1)         default '0'                comment '账号状态（0正常 1停用）',
  del_flag          char(1)         default '0'                comment '删除标志（0代表存在 2代表删除）',
  create_time       datetime        default null               comment '创建时间',
  update_time       datetime        default null               comment '更新时间',
  remark            varchar(500)    default null               comment '备注',
  primary key (user_id),
  unique key idx_openid (openid)
) engine=innodb auto_increment=100 default charset=utf8mb4 comment = '微信用户表';

-- ----------------------------
-- 错题表
-- ----------------------------
drop table if exists exam_mistake;
create table exam_mistake (
  mistake_id        bigint(20)      not null auto_increment    comment '错题ID',
  user_id           bigint(20)      not null                   comment '用户ID',
  question_id       bigint(20)      not null                   comment '题目ID',
  wrong_answer      varchar(100)    default null               comment '错误答案',
  mistake_count     int(11)         default 1                  comment '错误次数',
  last_mistake_time datetime        default null               comment '最后错误时间',
  status            char(1)         default '0'                comment '状态（0未复习 1已复习）',
  create_time       datetime        default null               comment '创建时间',
  update_time       datetime        default null               comment '更新时间',
  primary key (mistake_id),
  unique key idx_user_question (user_id, question_id),
  key idx_user_id (user_id),
  key idx_question_id (question_id)
) engine=innodb auto_increment=100 default charset=utf8mb4 comment = '错题表';

-- ----------------------------
-- 微信用户和错题管理菜单
-- ----------------------------
-- 一级菜单
insert into sys_menu values('2099', '微信用户', '0', '5', 'wxuser', null, '', '', 1, 0, 'M', '0', '0', '', 'wechat', 'admin', sysdate(), '', null, '微信用户菜单');

-- 二级菜单
insert into sys_menu values('2100', '微信用户管理', '2099', '1', 'wxuser', 'system/wxuser/index', '', '', 1, 0, 'C', '0', '0', 'system:wxuser:list', 'user', 'admin', sysdate(), '', null, '微信用户管理菜单');
insert into sys_menu values('2200', '错题管理', '2099', '2', 'mistake', 'system/mistake/index', '', '', 1, 0, 'C', '0', '0', 'system:mistake:list', 'edit', 'admin', sysdate(), '', null, '错题管理菜单');

-- 微信用户管理按钮
insert into sys_menu values('2101', '微信用户查询', '2100', '1', '', '', '', '', 1, 0, 'F', '0', '0', 'system:wxuser:query', '#', 'admin', sysdate(), '', null, '');
insert into sys_menu values('2102', '微信用户新增', '2100', '2', '', '', '', '', 1, 0, 'F', '0', '0', 'system:wxuser:add', '#', 'admin', sysdate(), '', null, '');
insert into sys_menu values('2103', '微信用户修改', '2100', '3', '', '', '', '', 1, 0, 'F', '0', '0', 'system:wxuser:edit', '#', 'admin', sysdate(), '', null, '');
insert into sys_menu values('2104', '微信用户删除', '2100', '4', '', '', '', '', 1, 0, 'F', '0', '0', 'system:wxuser:remove', '#', 'admin', sysdate(), '', null, '');
insert into sys_menu values('2105', '微信用户导出', '2100', '5', '', '', '', '', 1, 0, 'F', '0', '0', 'system:wxuser:export', '#', 'admin', sysdate(), '', null, '');

-- 错题管理按钮
insert into sys_menu values('2201', '错题查询', '2200', '1', '', '', '', '', 1, 0, 'F', '0', '0', 'system:mistake:query', '#', 'admin', sysdate(), '', null, '');
insert into sys_menu values('2202', '错题新增', '2200', '2', '', '', '', '', 1, 0, 'F', '0', '0', 'system:mistake:add', '#', 'admin', sysdate(), '', null, '');
insert into sys_menu values('2203', '错题修改', '2200', '3', '', '', '', '', 1, 0, 'F', '0', '0', 'system:mistake:edit', '#', 'admin', sysdate(), '', null, '');
insert into sys_menu values('2204', '错题删除', '2200', '4', '', '', '', '', 1, 0, 'F', '0', '0', 'system:mistake:remove', '#', 'admin', sysdate(), '', null, '');
insert into sys_menu values('2205', '错题导出', '2200', '5', '', '', '', '', 1, 0, 'F', '0', '0', 'system:mistake:export', '#', 'admin', sysdate(), '', null, ''); 