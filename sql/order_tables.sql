-- ----------------------------
-- 订单表
-- ----------------------------
drop table if exists order_info;
create table order_info (
  order_id           bigint(20)      not null auto_increment    comment '订单ID',
  order_no           varchar(32)     not null                   comment '订单编号',
  user_id            bigint(20)      not null                   comment '用户ID',
  package_id         bigint(20)      not null                   comment '套餐ID',
  package_name       varchar(100)    not null                   comment '套餐名称',
  amount             decimal(10,2)   not null                   comment '订单金额',
  status             char(1)         not null                   comment '订单状态（0待支付 1已支付 2已取消 3已过期）',
  pay_time           datetime        default null               comment '支付时间',
  expire_time        datetime        default null               comment '过期时间',
  pay_channel        varchar(20)     default null               comment '支付渠道（wechat微信 alipay支付宝）',
  transaction_id     varchar(64)     default null               comment '交易流水号',
  create_time        datetime        default null               comment '创建时间',
  update_time        datetime        default null               comment '更新时间',
  remark             varchar(500)    default null               comment '备注',
  primary key (order_id),
  unique key idx_order_no (order_no),
  key idx_user_id (user_id),
  key idx_package_id (package_id)
) engine=innodb auto_increment=100 default charset=utf8mb4 comment = '订单表';

-- ----------------------------
-- 订单管理菜单
-- ----------------------------
-- 一级菜单
insert into sys_menu values('2300', '订单管理', '0', '6', 'order', null, '', '', 1, 0, 'M', '0', '0', '', 'shopping', 'admin', sysdate(), '', null, '订单管理菜单');

-- 订单管理菜单
insert into sys_menu values('2310', '订单列表', '2300', '1', 'order', 'system/order/index', '', '', 1, 0, 'C', '0', '0', 'system:order:list', 'list', 'admin', sysdate(), '', null, '订单管理菜单');

-- 订单按钮
insert into sys_menu values('2311', '订单查询', '2310', '1', '', '', '', '', 1, 0, 'F', '0', '0', 'system:order:query', '#', 'admin', sysdate(), '', null, '');
insert into sys_menu values('2312', '订单新增', '2310', '2', '', '', '', '', 1, 0, 'F', '0', '0', 'system:order:add', '#', 'admin', sysdate(), '', null, '');
insert into sys_menu values('2313', '订单修改', '2310', '3', '', '', '', '', 1, 0, 'F', '0', '0', 'system:order:edit', '#', 'admin', sysdate(), '', null, '');
insert into sys_menu values('2314', '订单删除', '2310', '4', '', '', '', '', 1, 0, 'F', '0', '0', 'system:order:remove', '#', 'admin', sysdate(), '', null, '');
insert into sys_menu values('2315', '订单导出', '2310', '5', '', '', '', '', 1, 0, 'F', '0', '0', 'system:order:export', '#', 'admin', sysdate(), '', null, ''); 