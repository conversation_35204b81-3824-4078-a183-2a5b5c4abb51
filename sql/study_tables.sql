-- ----------------------------
-- 学习资料表
-- ----------------------------
drop table if exists study_material;
create table study_material (
  material_id         bigint(20)      not null auto_increment    comment '资料ID',
  material_name       varchar(100)    not null                   comment '资料名称',
  material_type       char(1)         not null                   comment '资料类型（0电子书 1视频）',
  resource_url        varchar(255)    not null                   comment '资源链接',
  cover_img           varchar(255)    default null               comment '封面图片',
  material_desc       text            default null               comment '资料介绍',
  theme               varchar(100)    default null               comment '资源主题',
  package_id          bigint(20)      not null                   comment '所属套餐ID',
  file_size           bigint(20)      default 0                  comment '文件大小(KB)',
  view_count          int(11)         default 0                  comment '浏览次数',
  download_count      int(11)         default 0                  comment '下载次数',
  status              char(1)         default '0'                comment '状态（0正常 1停用）',
  create_by           varchar(64)     default ''                 comment '创建者',
  create_time         datetime        default null               comment '发布时间',
  update_by           varchar(64)     default ''                 comment '更新者',
  update_time         datetime        default null               comment '更新时间',
  remark              varchar(500)    default null               comment '备注',
  primary key (material_id),
  key idx_package_id (package_id),
  key idx_material_type (material_type)
) engine=innodb auto_increment=100 default charset=utf8mb4 comment = '学习资料表';

-- ----------------------------
-- 学习记录表
-- ----------------------------
drop table if exists study_record;
create table study_record (
  record_id           bigint(20)      not null auto_increment    comment '记录ID',
  user_id             bigint(20)      not null                   comment '用户ID',
  material_id         bigint(20)      not null                   comment '资料ID',
  package_id          bigint(20)      not null                   comment '套餐ID',
  material_type       char(1)         not null                   comment '资料类型（0电子书 1视频）',
  progress            int(11)         default 0                  comment '学习进度(百分比)',
  last_position       varchar(20)     default '0'                comment '上次学习位置',
  study_time          int(11)         default 0                  comment '学习时长(秒)',
  create_time         datetime        default null               comment '创建时间',
  update_time         datetime        default null               comment '更新时间',
  primary key (record_id),
  unique key idx_user_material (user_id, material_id),
  key idx_user_id (user_id),
  key idx_material_id (material_id),
  key idx_package_id (package_id)
) engine=innodb auto_increment=100 default charset=utf8mb4 comment = '学习记录表';

-- ----------------------------
-- 学习资料管理菜单
-- ----------------------------
-- 一级菜单
insert into sys_menu values('2400', '学习资料', '0', '7', 'material', null, '', '', 1, 0, 'M', '0', '0', '', 'education', 'admin', sysdate(), '', null, '学习资料管理菜单');

-- 学习资料菜单
insert into sys_menu values('2410', '资料管理', '2400', '1', 'material', 'system/material/index', '', '', 1, 0, 'C', '0', '0', 'system:material:list', 'documentation', 'admin', sysdate(), '', null, '资料管理菜单');

-- 学习资料按钮
insert into sys_menu values('2411', '资料查询', '2410', '1', '', '', '', '', 1, 0, 'F', '0', '0', 'system:material:query', '#', 'admin', sysdate(), '', null, '');
insert into sys_menu values('2412', '资料新增', '2410', '2', '', '', '', '', 1, 0, 'F', '0', '0', 'system:material:add', '#', 'admin', sysdate(), '', null, '');
insert into sys_menu values('2413', '资料修改', '2410', '3', '', '', '', '', 1, 0, 'F', '0', '0', 'system:material:edit', '#', 'admin', sysdate(), '', null, '');
insert into sys_menu values('2414', '资料删除', '2410', '4', '', '', '', '', 1, 0, 'F', '0', '0', 'system:material:remove', '#', 'admin', sysdate(), '', null, '');
insert into sys_menu values('2415', '资料导出', '2410', '5', '', '', '', '', 1, 0, 'F', '0', '0', 'system:material:export', '#', 'admin', sysdate(), '', null, '');

-- 学习记录菜单
insert into sys_menu values('2420', '学习记录', '2400', '2', 'record', 'system/record/index', '', '', 1, 0, 'C', '0', '0', 'system:record:list', 'time-range', 'admin', sysdate(), '', null, '学习记录菜单');

-- 学习记录按钮
insert into sys_menu values('2421', '记录查询', '2420', '1', '', '', '', '', 1, 0, 'F', '0', '0', 'system:record:query', '#', 'admin', sysdate(), '', null, '');
insert into sys_menu values('2422', '记录导出', '2420', '2', '', '', '', '', 1, 0, 'F', '0', '0', 'system:record:export', '#', 'admin', sysdate(), '', null, ''); 