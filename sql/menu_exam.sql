-- 消防知识考试题库模块菜单SQL
-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('题库管理', '3', '10', 'exam', null, 1, 0, 'M', '0', '0', '', 'education', 'admin', sysdate(), '', null, '题库管理菜单');

-- 二级菜单
INSERT INTO sys_menu(menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('题目分类', (SELECT @parentId := LAST_INSERT_ID()), 1, 'category', 'system/category/index', 1, 0, 'C', '0', '0', 'system:category:list', 'tree', 'admin', sysdate(), '', null, '题目分类菜单');

-- 题目分类按钮
INSERT INTO sys_menu(menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('题目分类查询', @parentId, 1, '#', '', 1, 0, 'F', '0', '0', 'system:category:query', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu(menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('题目分类新增', @parentId, 2, '#', '', 1, 0, 'F', '0', '0', 'system:category:add', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu(menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('题目分类修改', @parentId, 3, '#', '', 1, 0, 'F', '0', '0', 'system:category:edit', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu(menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('题目分类删除', @parentId, 4, '#', '', 1, 0, 'F', '0', '0', 'system:category:remove', '#', 'admin', sysdate(), '', null, '');

-- 题库套餐菜单
INSERT INTO sys_menu(menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('题库套餐', (SELECT @menuId := menu_id FROM sys_menu WHERE menu_name = '题库管理' AND parent_id = '3' LIMIT 1), 2, 'package', 'system/package/index', 1, 0, 'C', '0', '0', 'system:package:list', 'shopping', 'admin', sysdate(), '', null, '题库套餐菜单');

-- 题库套餐按钮
INSERT INTO sys_menu(menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('题库套餐查询', @menuId, 1, '#', '', 1, 0, 'F', '0', '0', 'system:package:query', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu(menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('题库套餐新增', @menuId, 2, '#', '', 1, 0, 'F', '0', '0', 'system:package:add', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu(menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('题库套餐修改', @menuId, 3, '#', '', 1, 0, 'F', '0', '0', 'system:package:edit', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu(menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('题库套餐删除', @menuId, 4, '#', '', 1, 0, 'F', '0', '0', 'system:package:remove', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu(menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('题库套餐导出', @menuId, 5, '#', '', 1, 0, 'F', '0', '0', 'system:package:export', '#', 'admin', sysdate(), '', null, '');

-- 题库题目菜单
INSERT INTO sys_menu(menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('题库题目', (SELECT @menuId := menu_id FROM sys_menu WHERE menu_name = '题库管理' AND parent_id = '3' LIMIT 1), 3, 'question', 'system/question/index', 1, 0, 'C', '0', '0', 'system:question:list', 'form', 'admin', sysdate(), '', null, '题库题目菜单');

-- 题库题目按钮
INSERT INTO sys_menu(menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('题库题目查询', @menuId, 1, '#', '', 1, 0, 'F', '0', '0', 'system:question:query', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu(menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('题库题目新增', @menuId, 2, '#', '', 1, 0, 'F', '0', '0', 'system:question:add', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu(menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('题库题目修改', @menuId, 3, '#', '', 1, 0, 'F', '0', '0', 'system:question:edit', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu(menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('题库题目删除', @menuId, 4, '#', '', 1, 0, 'F', '0', '0', 'system:question:remove', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu(menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('题库题目导出', @menuId, 5, '#', '', 1, 0, 'F', '0', '0', 'system:question:export', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu(menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES ('题库题目导入', @menuId, 6, '#', '', 1, 0, 'F', '0', '0', 'system:question:import', '#', 'admin', sysdate(), '', null, ''); 