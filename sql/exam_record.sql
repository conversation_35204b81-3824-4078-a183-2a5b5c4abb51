-- 创建考试记录表
CREATE TABLE `exam_record` (
  `record_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `package_id` bigint(20) NOT NULL COMMENT '套餐ID',
  `exam_start_time` datetime DEFAULT NULL COMMENT '考试开始时间',
  `exam_end_time` datetime DEFAULT NULL COMMENT '考试结束时间',
  `score` int(11) DEFAULT '0' COMMENT '分数',
  `correct_count` int(11) DEFAULT '0' COMMENT '正确数',
  `wrong_count` int(11) DEFAULT '0' COMMENT '错误数',
  `empty_count` int(11) DEFAULT '0' COMMENT '空题数',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1删除）',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`record_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_package_id` (`package_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='考试记录表';

-- 添加考试记录菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('考试记录管理', 1, 10, 'examRecord', 'system/examRecord/index', 1, 0, 'C', '0', '0', 'system:examRecord:list', 'form', 'admin', NOW(), '', null, '考试记录菜单');

-- 菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('考试记录查询', @parentId, 1, '#', '', 1, 0, 'F', '0', '0', 'system:examRecord:query', '#', 'admin', NOW(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('考试记录新增', @parentId, 2, '#', '', 1, 0, 'F', '0', '0', 'system:examRecord:add', '#', 'admin', NOW(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('考试记录修改', @parentId, 3, '#', '', 1, 0, 'F', '0', '0', 'system:examRecord:edit', '#', 'admin', NOW(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('考试记录删除', @parentId, 4, '#', '', 1, 0, 'F', '0', '0', 'system:examRecord:remove', '#', 'admin', NOW(), '', null, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('考试记录导出', @parentId, 5, '#', '', 1, 0, 'F', '0', '0', 'system:examRecord:export', '#', 'admin', NOW(), '', null, '');