-- 添加导入用户套餐菜单权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('导入用户套餐', (SELECT menu_id FROM sys_menu WHERE perms = 'system:userPackage:list'), 5, 'import', 'system/userPackage/import', 1, 0, 'C', '1', '0', 'system:userPackage:import', 'upload', 'admin', sysdate(), '', null, '导入用户套餐菜单');

-- 添加按钮权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('用户套餐导入', (SELECT menu_id FROM sys_menu WHERE perms = 'system:userPackage:list'), 6, '#', '', 1, 0, 'F', '0', '0', 'system:userPackage:import', '#', 'admin', sysdate(), '', null, ''); 