-- 创建用户学习进度表
-- 用于记录用户在每个套餐下的学习进度情况

DROP TABLE IF EXISTS `user_study_progress`;

CREATE TABLE `user_study_progress` (
  `progress_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '进度ID',
  `package_id` bigint(20) NOT NULL COMMENT '套餐ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `sort_num` int(11) DEFAULT NULL COMMENT '当前学习进度排序号',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`progress_id`),
  UNIQUE KEY `uk_user_package` (`user_id`, `package_id`) COMMENT '用户套餐唯一索引',
  KEY `idx_user_id` (`user_id`) COMMENT '用户ID索引',
  KEY `idx_package_id` (`package_id`) COMMENT '套餐ID索引',
  KEY `idx_sort_num` (`sort_num`) COMMENT '排序号索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户学习进度表';
