-- 修改考试记录表字段默认值
-- 将score、correct_count、wrong_count、empty_count字段的默认值从0改为NULL
-- 这样可以实现不传参数就不赋值的效果

-- 修改score字段，移除默认值0，允许NULL
ALTER TABLE `exam_record` MODIFY COLUMN `score` int(11) DEFAULT NULL COMMENT '分数';

-- 修改correct_count字段，移除默认值0，允许NULL  
ALTER TABLE `exam_record` MODIFY COLUMN `correct_count` int(11) DEFAULT NULL COMMENT '正确数';

-- 修改wrong_count字段，移除默认值0，允许NULL
ALTER TABLE `exam_record` MODIFY COLUMN `wrong_count` int(11) DEFAULT NULL COMMENT '错误数';

-- 修改empty_count字段，移除默认值0，允许NULL
ALTER TABLE `exam_record` MODIFY COLUMN `empty_count` int(11) DEFAULT NULL COMMENT '空题数';
