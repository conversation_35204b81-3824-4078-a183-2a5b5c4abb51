-- ----------------------------
-- 反馈信息表
-- ----------------------------
drop table if exists feedback;
create table feedback (
  feedback_id       bigint(20)      not null auto_increment    comment '反馈ID',
  user_id           bigint(20)      not null                   comment '用户ID',
  content           varchar(500)    not null                   comment '反馈内容',
  contact           varchar(100)                               comment '联系方式',
  feedback_type     char(1)         default '0'                comment '反馈类型（0建议 1问题 2其他）',
  images            varchar(1000)                              comment '图片地址，多个以逗号分隔',
  status            char(1)         default '0'                comment '状态（0未处理 1已处理）',
  reply             varchar(500)                               comment '回复内容',
  reply_time        datetime                                   comment '回复时间',
  reply_by          varchar(64)                                comment '回复人',
  create_time       datetime        default null               comment '创建时间',
  update_time       datetime        default null               comment '更新时间',
  remark            varchar(500)    default null               comment '备注',
  primary key (feedback_id),
  key idx_user_id (user_id)
) engine=innodb auto_increment=100 default charset=utf8mb4 comment = '反馈信息表';

-- ----------------------------
-- 反馈相关字典数据
-- ----------------------------
-- 反馈类型字典
insert into sys_dict_type values(101, '反馈类型', 'feedback_type', '0', 'admin', sysdate(), '', null, '反馈类型列表');
insert into sys_dict_data values(1010, 1, '建议', '0', 'feedback_type', '', 'primary', 'Y', '0', 'admin', sysdate(), '', null, '建议');
insert into sys_dict_data values(1011, 2, '问题', '1', 'feedback_type', '', 'warning', 'N', '0', 'admin', sysdate(), '', null, '问题');
insert into sys_dict_data values(1012, 3, '其他', '2', 'feedback_type', '', 'info', 'N', '0', 'admin', sysdate(), '', null, '其他');

-- 反馈状态字典
insert into sys_dict_type values(102, '反馈状态', 'feedback_status', '0', 'admin', sysdate(), '', null, '反馈状态列表');
insert into sys_dict_data values(1020, 1, '未处理', '0', 'feedback_status', '', 'danger', 'Y', '0', 'admin', sysdate(), '', null, '未处理');
insert into sys_dict_data values(1021, 2, '已处理', '1', 'feedback_status', '', 'success', 'N', '0', 'admin', sysdate(), '', null, '已处理');

-- ----------------------------
-- 反馈管理菜单
-- ----------------------------
-- 反馈管理菜单
insert into sys_menu values('2600', '反馈管理', '1', '10', 'feedback', 'system/feedback/index', '', 'feedback', 1, 0, 'C', '0', '0', 'system:feedback:list', 'message', 'admin', sysdate(), '', null, '反馈管理菜单');

-- 反馈管理按钮
insert into sys_menu values('2601', '反馈查询', '2600', '1', '', '', '', '', 1, 0, 'F', '0', '0', 'system:feedback:query', '#', 'admin', sysdate(), '', null, '');
insert into sys_menu values('2602', '反馈回复', '2600', '2', '', '', '', '', 1, 0, 'F', '0', '0', 'system:feedback:reply', '#', 'admin', sysdate(), '', null, '');
insert into sys_menu values('2603', '反馈删除', '2600', '3', '', '', '', '', 1, 0, 'F', '0', '0', 'system:feedback:remove', '#', 'admin', sysdate(), '', null, '');
insert into sys_menu values('2604', '反馈导出', '2600', '4', '', '', '', '', 1, 0, 'F', '0', '0', 'system:feedback:export', '#', 'admin', sysdate(), '', null, '');

-- 小程序API接口权限菜单
insert into sys_menu values('2650', '小程序反馈接口', '2120', '6', 'apiFeedback', 'system/apiFeedback/index', '', 'apiFeedback', 1, 0, 'C', '0', '0', 'system:apiFeedback:list', 'list', 'admin', sysdate(), '', null, '小程序反馈接口菜单'); 