-- ----------------------------
-- 系统标识配置项
-- ----------------------------

-- 检查是否已存在配置项，不存在则插入
INSERT INTO sys_config (config_name, config_key, config_value, config_type, create_by, create_time, remark)
SELECT '系统Logo', 'sys.index.logo', 'https://你的logo地址', 'N', 'admin', sysdate(), '系统Logo图片地址'
FROM DUAL WHERE NOT EXISTS (SELECT 1 FROM sys_config WHERE config_key = 'sys.index.logo');

INSERT INTO sys_config (config_name, config_key, config_value, config_type, create_by, create_time, remark)
SELECT '联系人电话', 'sys.contact.phone', '18888888888', 'N', 'admin', sysdate(), '系统联系人电话'
FROM DUAL WHERE NOT EXISTS (SELECT 1 FROM sys_config WHERE config_key = 'sys.contact.phone');

INSERT INTO sys_config (config_name, config_key, config_value, config_type, create_by, create_time, remark)
SELECT '客服电话', 'sys.service.phone', '************', 'N', 'admin', sysdate(), '系统客服电话'
FROM DUAL WHERE NOT EXISTS (SELECT 1 FROM sys_config WHERE config_key = 'sys.service.phone');

INSERT INTO sys_config (config_name, config_key, config_value, config_type, create_by, create_time, remark)
SELECT '联系人姓名', 'sys.contact.name', '管理员', 'N', 'admin', sysdate(), '系统联系人姓名'
FROM DUAL WHERE NOT EXISTS (SELECT 1 FROM sys_config WHERE config_key = 'sys.contact.name'); 